import { NextResponse } from 'next/server.js';

// Rate limit constants
const KOL_FEED_LIMIT = process.env.NODE_ENV === 'development' ? 1000 : 50; // 50 req/min for KOL feed
const DEFAULT_LIMIT = process.env.NODE_ENV === 'development' ? 1000 : 60;  // 60 req/min for others

// Simple CEX endpoint rate limits - realistic for normal usage
const CEX_LIMITS = {
  requests: process.env.NODE_ENV === 'development' ? 1000 : 30,  // 30 requests per minute (generous)
  burst: process.env.NODE_ENV === 'development' ? 100 : 10       // 10 requests in 10 seconds (prevent spam)
};

// Simple token bucket rate limiter for Edge Runtime
class TokenBucketRateLimiter {
  constructor(options) {
    this.maxTokens = options.points;
    this.refillRate = options.points / options.duration; // tokens per second
    this.store = new Map();
  }

  async consume(key) {
    const now = Date.now() / 1000; // Convert to seconds
    const bucket = this.store.get(key) || { tokens: this.maxTokens, lastRefill: now };

    // Refill tokens based on time passed
    const timePassed = now - bucket.lastRefill;
    const refillAmount = timePassed * this.refillRate;
    bucket.tokens = Math.min(this.maxTokens, bucket.tokens + refillAmount);
    bucket.lastRefill = now;

    // Check if we can consume a token
    if (bucket.tokens < 1) {
      const waitTime = (1 - bucket.tokens) / this.refillRate;
      throw { msBeforeNext: waitTime * 1000 };
    }

    // Consume token
    bucket.tokens -= 1;
    this.store.set(key, bucket);

    return { remaining: Math.floor(bucket.tokens) };
  }
}

// Rate limiter stores
const defaultRateLimiter = new TokenBucketRateLimiter({
  points: DEFAULT_LIMIT,
  duration: 60, // Per 60 seconds
});

const kolFeedRateLimiter = new TokenBucketRateLimiter({
  points: KOL_FEED_LIMIT,
  duration: 60, // Per 60 seconds
});

const cexRateLimiter = new TokenBucketRateLimiter({
  points: CEX_LIMITS.requests,
  duration: 60, // Per 60 seconds
});

const cexBurstLimiter = new TokenBucketRateLimiter({
  points: CEX_LIMITS.burst,
  duration: 10, // Per 10 seconds
});

// Security headers configuration
const securityHeaders = {
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'X-Permitted-Cross-Domain-Policies': 'none',
  'X-DNS-Prefetch-Control': 'off',
  'Content-Security-Policy': process.env.NODE_ENV === 'development'
    ? "default-src * 'unsafe-inline' 'unsafe-eval'; script-src * 'unsafe-inline' 'unsafe-eval'; connect-src * 'unsafe-inline'; img-src * data: blob: 'unsafe-inline'; frame-src *; style-src * 'unsafe-inline';"
    : "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https: ws: wss:;",
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
};

// CORS configuration
export const corsConfig = {
  origin: process.env.NODE_ENV === 'development'
    ? [
        'http://localhost:3000',
        'http://localhost:3001',
        'https://stalkchain.local',
        ...(process.env.SITE_URL ? [process.env.SITE_URL] : [])
      ]
    : [
        'https://stalkchain.com',
        'https://www.stalkchain.com',
        'https://app.stalkchain.com',
        ...(process.env.SITE_URL ? [process.env.SITE_URL] : [])
      ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Cookie', 'X-Requested-With'],
  exposedHeaders: ['Set-Cookie'],
  credentials: true,
  maxAge: 86400, // 24 hours
};

// Simple rate limiting middleware
export async function rateLimitMiddleware(req) {
  if (process.env.NODE_ENV === 'development') {
    return null; // Skip rate limiting in development
  }

  // Skip rate limiting for tokeninfo and proxy endpoints
  if (req.nextUrl.pathname.includes('/api/tokeninfo')) {
    return null;
  }

  const ip = req.headers.get('x-forwarded-for')?.split(',')[0] ||
    req.headers.get('x-real-ip') ||
    '127.0.0.1';

  try {
    // Special handling for CEX batch endpoint - simple burst + rate limiting
    if (req.nextUrl.pathname === '/api/cex/batch') {
      // Check burst limit first (prevents spam/DDoS)
      await cexBurstLimiter.consume(`${ip}:burst`);
      
      // Check regular rate limit
      const result = await cexRateLimiter.consume(ip);
      
      const headers = new Headers();
      headers.set('X-RateLimit-Limit', String(CEX_LIMITS.requests));
      headers.set('X-RateLimit-Remaining', String(result.remaining));
      
      return { headers };
    }

    // Standard rate limiting for other endpoints
    const isKolFeed = req.nextUrl.pathname.startsWith('/api/kolFeed');
    const limiter = isKolFeed ? kolFeedRateLimiter : defaultRateLimiter;
    const result = await limiter.consume(ip);
    
    const headers = new Headers();
    headers.set('X-RateLimit-Limit', isKolFeed ? String(KOL_FEED_LIMIT) : String(DEFAULT_LIMIT));
    headers.set('X-RateLimit-Remaining', String(result.remaining));
    
    return { headers };
  } catch (error) {
    // Enhanced error response for CEX endpoint
    if (req.nextUrl.pathname === '/api/cex/batch') {
      return new NextResponse(
        JSON.stringify({ 
          error: 'Rate limit exceeded',
          message: 'Too many requests. Please wait before trying again.',
          retryAfter: Math.ceil(error.msBeforeNext / 1000)
        }),
        {
          status: 429,
          headers: {
            'Content-Type': 'application/json',
            'Retry-After': String(Math.ceil(error.msBeforeNext / 1000)),
            'X-RateLimit-Limit': String(CEX_LIMITS.requests),
            'X-RateLimit-Remaining': '0'
          }
        }
      );
    }

    // Standard error response for other endpoints
    const isKolFeed = req.nextUrl.pathname.startsWith('/api/kolFeed');
    return new NextResponse(
      JSON.stringify({ error: 'Too many requests' }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'Retry-After': String(Math.ceil(error.msBeforeNext / 1000)),
          'X-RateLimit-Limit': isKolFeed ? String(KOL_FEED_LIMIT) : String(DEFAULT_LIMIT),
          'X-RateLimit-Remaining': '0'
        }
      }
    );
  }
}

// Security headers middleware
export function addSecurityHeaders(headers) {
  Object.entries(securityHeaders).forEach(([key, value]) => {
    headers.set(key, value);
  });

  // Add CORS headers for /api/proxy endpoint
  headers.set('Access-Control-Allow-Origin', '*');
  headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type');
} 