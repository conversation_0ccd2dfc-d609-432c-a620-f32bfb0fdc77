"use client";

import { useState, useMemo, useEffect, useCallback, useRef } from "react";
import dynamic from "next/dynamic";
import Cookies from 'js-cookie';
import { useInfiniteQuery, useQueryClient } from "@tanstack/react-query";
import DOMPurify from "isomorphic-dompurify";
import { useStream } from "@/context/SocketIoContext";
import { defaultDashboardPath } from "@/config/site";
import { classifySearchInput } from "@/helpers/SearchHelper";
import LiveDcaFilters from "./components/LiveDcaFilters";

const LiveDcaTable = dynamic(() => import("@/components/tables/LiveDcaTable"));
const TokenPressureCard = dynamic(() => import("@/content/dca-orders-feed-2/TokenPressureCard"));

// Cookie constants
const MIN_USD_COOKIE = 'active_orders_min_usd';
const SOUND_SETTINGS_COOKIE = 'active_orders_sound_enabled';
const HIDE_PRESSURE_COOKIE = 'active_orders_hide_pressure';

/**
 * Fetches live DCA orders from the API.
 * This function is used by `useInfiniteQuery`.
 * @param {object} context - The context from react-query.
 * @param {string|null} context.pageParam - The cursor for pagination.
 * @param {object} context.queryKey - The query key array.
 * @returns {Promise<object>} The API response data.
 */
const fetchLiveDcaOrders = async ({ pageParam = null, queryKey }) => {
  const [, { search, status, minUsd, maxUsd }] = queryKey;
  try {
    const queryParams = new URLSearchParams();
    if (pageParam) queryParams.append("cursor", DOMPurify.sanitize(pageParam));
    if (status) queryParams.append("status", DOMPurify.sanitize(status));
    if (search) queryParams.append("search", DOMPurify.sanitize(search));
    if (minUsd) queryParams.append("minUsd", DOMPurify.sanitize(minUsd));
    if (maxUsd) queryParams.append("maxUsd", DOMPurify.sanitize(maxUsd));

    const response = await fetch(`/api/liveDcaFeed?${queryParams}`, { next: { revalidate: 0 } });

    if (!response.ok) {
      if (response.status === 403) {
        console.error(`[LiveDcaFeed] 403 Unauthorized - redirecting to dashboard`);
        window.location.href = defaultDashboardPath;
        throw new Error("Unauthorized");
      }
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json();
    if (result?.error === "unauthorized") {
      console.error(`[LiveDcaFeed] API returned unauthorized error`);
      window.location.href = defaultDashboardPath;
      throw new Error("Unauthorized");
    }

    return result.data;
  } catch (error) {
    console.error("Error fetching live DCA orders:", error);
    throw error;
  }
};

/**
 * Calculates derived fields from an order object for frontend consumption.
 * Now simplified to use pre-calculated values from notifications.
 * @param {object} order - The raw order object.
 * @returns {object} The order object with minimal processing.
 */
const calculateDerivedFields = (order) => {
  // Only calculate what's absolutely necessary and not provided
  const isHealthy = order.order_status === 'active' && !order.is_overdue;

  return {
    ...order,
    // Use pre-calculated values when available, fallback to calculation only if needed
    progress_percentage: order.progress_percentage !== undefined 
      ? order.progress_percentage 
      : (order.in_deposited > 0 ? parseFloat((order.total_input_spent / order.in_deposited * 100).toFixed(2)) : 0),
    is_healthy: isHealthy
  };
};


// Initial state loader from cookies
const getInitialState = () => {
  try {
    const minUsd = Cookies.get(MIN_USD_COOKIE);
    const soundEnabled = Cookies.get(SOUND_SETTINGS_COOKIE);
    const hidePressure = Cookies.get(HIDE_PRESSURE_COOKIE);
    
    return {
      search: "",
      status: "all",
      minUsd: minUsd ? parseInt(minUsd, 10) : 1000,
      soundEnabled: soundEnabled === undefined ? true : soundEnabled === 'true',
      hidePressure: hidePressure === undefined ? false : hidePressure === 'true',
      maxUsd: undefined, // Not currently used in UI, but available
    };
  } catch (error) {
    console.error("Error loading initial state from cookies:", error);
    return {
      search: "",
      status: "all",
      minUsd: 1000,
      soundEnabled: true,
      hidePressure: false,
      maxUsd: undefined,
    };
  }
};

/**
 * PageContent for the Live DCA Feed.
 *
 * This component serves as the main entry point for the Live DCA Feed page.
 * It manages the application state, including all filters, and orchestrates
 * data fetching and rendering of child components.
 *
 * - Initializes filter state from browser cookies for persistence.
 * - Uses the `useLiveDcaFeed` hook to fetch real-time and historical data.
 * - Renders the `LiveDcaFilters` component for user interactions.
 * - Renders the `TokenPressureCard` to display market pressure insights.
 * - Renders the `LiveDcaTable` with the fetched data.
 *
 * @returns {JSX.Element} The rendered page content for the live DCA feed.
 */
export default function PageContent() {
  const [filters, setFilters] = useState(() => getInitialState());
  const [isInitialized, setIsInitialized] = useState(false);
  const queryClient = useQueryClient();
  const { isConnected, subscribeToEvent } = useStream();
  const notificationSound = useRef(null);
  const filtersRef = useRef(filters);
  const lastDataRef = useRef(null);
  const stableTableDataRef = useRef([]);

  // Memoize queryKey to prevent unnecessary refetches
  const queryKey = useMemo(() => ["live-dca", { 
    search: filters.search, 
    status: filters.status === 'all' ? undefined : filters.status, 
    minUsd: filters.minUsd.toString(),
    maxUsd: filters.maxUsd,
  }], [filters]);

  // Update filtersRef whenever filters change
  useEffect(() => {
    filtersRef.current = filters;
  }, [filters]);

  // This effect runs once on mount to mark component as initialized
  useEffect(() => {
    setIsInitialized(true);
  }, []);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isFetchingNextPage,
    isLoading,
    isError,
    error,
  } = useInfiniteQuery({
    queryKey,
    queryFn: fetchLiveDcaOrders,
    getNextPageParam: (lastPage) => lastPage?.nextCursor || undefined,
    enabled: isInitialized,
    refetchOnWindowFocus: false,
    staleTime: 0,
    gcTime: 5 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });

  // Initialize audio on component mount
  useEffect(() => {
    notificationSound.current = new Audio("/sounds/alert-1.mp3");
    notificationSound.current.preload = "auto";
  }, []);



  /**
   * Plays a notification sound if sound is enabled.
   * @param {string} eventType - The type of event that occurred.
   */
  const playNotificationSound = useCallback((eventType) => {
    // Get current sound setting dynamically to avoid dependency issues
    const currentSoundEnabled = filtersRef.current.soundEnabled;
    if (currentSoundEnabled && eventType === 'order_opened' && notificationSound.current) {
      notificationSound.current.currentTime = 0;
      notificationSound.current.play().catch(e => console.error("Error playing sound:", e));
    }
  }, []);

  /**
   * Handles real-time events from the WebSocket stream.
   * Updates the React Query cache based on the event type.
   * @param {object} message - The message from the WebSocket.
   */
  const handleRealtimeEvent = useCallback((message) => {
    const { type } = message;

    // Get current filters and queryKey dynamically to avoid dependency issues
    const currentFilters = filtersRef.current;
    const currentQueryKey = ["live-dca", {
      search: currentFilters.search,
      status: currentFilters.status === 'all' ? undefined : currentFilters.status,
      minUsd: currentFilters.minUsd.toString(),
      maxUsd: currentFilters.maxUsd,
    }];

    // Helper function to check if order matches current filters
    const orderMatchesCurrentFilters = (order) => {
      const { status, search, minUsd, maxUsd } = currentFilters;
      if (status && status !== 'all' && order.order_status !== status) return false;

      if (search && search.trim()) {
        const searchClassification = classifySearchInput(search);
        const searchLower = search.toLowerCase();
        if (searchClassification.type === 'address' && searchClassification.isValid) {
          if (order.user_key !== search && order.input_mint !== search && order.output_mint !== search && order.dca_key !== search) {
            return false;
          }
        } else {
          if (!order.user_key?.toLowerCase().includes(searchLower) && !order.input_mint?.toLowerCase().includes(searchLower) && !order.output_mint?.toLowerCase().includes(searchLower) && !order.dca_key?.toLowerCase().includes(searchLower)) {
            return false;
          }
        }
      }

      const orderUsd = order.in_deposited_usd || 0;
      if (minUsd && orderUsd < parseFloat(minUsd)) return false;
      if (maxUsd && orderUsd > parseFloat(maxUsd)) return false;

      return true;
    };

    switch (type) {
      case 'order_opened': {
        if (orderMatchesCurrentFilters(message.order)) {
          queryClient.setQueryData(currentQueryKey, (oldData) => {
            if (!oldData?.pages) return oldData;
            const newOrder = { ...calculateDerivedFields(message.order), isNew: true };
            const newPages = [...oldData.pages];
            newPages[0] = { ...newPages[0], results: [newOrder, ...(newPages[0]?.results || [])] };
            return { ...oldData, pages: newPages };
          });
          playNotificationSound(type);
        }
        break;
      }
      case 'order_filled': {
        const { orderUpdate } = message;
        queryClient.setQueryData(currentQueryKey, (oldData) => {
          if (!oldData?.pages) return oldData;
          const updatedPages = oldData.pages.map(page => ({
            ...page,
            results: page.results.map(order => {
              if (order.dca_key === orderUpdate.dca_key) {
                const updatedOrder = { ...order, ...orderUpdate };
                return { ...updatedOrder, ...calculateDerivedFields(updatedOrder) };
              }
              return order;
            })
          }));
          return { ...oldData, pages: updatedPages };
        });
        break;
      }
      case 'order_closed': {
        queryClient.setQueryData(currentQueryKey, (oldData) => {
          if (!oldData?.pages) return oldData;
          return {
            ...oldData,
            pages: oldData.pages.map(page => ({
              ...page,
              results: page.results.map(order => {
                if (order.dca_key === message.dca_key) {
                  const updatedOrder = {
                    ...order,
                    order_status: message.close_data?.order_status || 'completed',
                    closed_at: message.metadata?.timestamp,
                    user_closed: message.close_data?.user_closed || false,
                    next_expected_fill_at: null,
                    remaining_input_usd: 0
                  };
                  return orderMatchesCurrentFilters(updatedOrder) ? { ...updatedOrder, ...calculateDerivedFields(updatedOrder) } : null;
                }
                return order;
              }).filter(Boolean)
            }))
          };
        });
        break;
      }
      case 'orders_expired': {
        queryClient.setQueryData(currentQueryKey, (oldData) => {
          if (!oldData?.pages || !message.expired_dca_keys?.length) return oldData;
          return {
            ...oldData,
            pages: oldData.pages.map(page => ({
              ...page,
              results: page.results.map(order => {
                if (message.expired_dca_keys.includes(order.dca_key)) {
                  const updatedOrder = {
                    ...order,
                    order_status: 'overdue_expired',
                    closed_at: message.metadata?.timestamp || new Date().toISOString(),
                    next_expected_fill_at: null,
                    remaining_input_usd: 0,
                    updated_at: new Date().toISOString()
                  };
                  return { ...updatedOrder, ...calculateDerivedFields(updatedOrder) };
                }
                return order;
              })
            }))
          };
        });
        break;
      }
      case 'backfill_completed': {
        queryClient.refetchQueries({ queryKey: currentQueryKey });
        break;
      }
      default:
        break;
    }
  }, [queryClient, playNotificationSound]);

  // Subscribe to WebSocket events
  useEffect(() => {
    // Only subscribe if the connection is active, the query is enabled,
    // and the initial page load is complete.
    if (isConnected && isInitialized && !isLoading) {
      const unsubscribe = subscribeToEvent("live_dca_feed", handleRealtimeEvent);
      return () => { if (unsubscribe) unsubscribe(); };
    }
  }, [isConnected, isInitialized, isLoading, handleRealtimeEvent, subscribeToEvent]);

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // Use a stable reference for table data to prevent infinite re-renders
  const rawTableData = useMemo(() => {
    if (!data?.pages) return [];
    console.log('data', JSON.stringify(data?.pages, null, 2));
    return data.pages.flatMap(page => page.results || []);
  }, [data?.pages?.length]);

  return (
    <div className="flex flex-col gap-0 items-center sm:items-start">
      <div className="w-full mx-auto max-w-screen-xl px-0 md:px-10 py-5">
        <div className="">
          <h1 className="text-xl mb-1 font-semibold">Live DCA Feed</h1>
          <div className="text-sm text-default-500">
            Real-time Jupiter DCA orders with live progress tracking and status updates.
          </div>
        </div>

        <div className="mt-8">
          <LiveDcaFilters 
            filters={filters}
            onFilterChange={handleFilterChange}
          />
        </div>

        <div className="mt-5 !mb-0">
          <TokenPressureCard isHidden={filters.hidePressure} />
        </div>

        <LiveDcaTable 
          data={rawTableData}
          isLoading={isLoading}
          isFetching={isFetching}
          isFetchingNextPage={isFetchingNextPage}
          fetchNextPage={fetchNextPage}
          hasNextPage={hasNextPage}
          isError={isError}
          error={error}
          filters={filters}
        />
      </div>
    </div>
  );
} 