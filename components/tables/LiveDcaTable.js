"use client";

import { useMemo, useRef, useEffect, useCallback, useState } from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { Spinner } from '@heroui/spinner';
import { useQueryClient } from '@tanstack/react-query';
import StalkchainTableComponent from '@/components/tables/StalkchainTableComponent';
import { SolanaWallet, SolanaToken } from '@/components/UI/SolanaAddress';
import { formatCurrency } from '@/helpers/TextFormat';
import TimeAgoDisplay from '@/components/dashboard/TimeAgoDisplay';
import { useQuery } from '@tanstack/react-query';

// Color scheme from DCA components
const DCA_COLORS = {
  ACTIVE: { bg: 'bg-blue-100', text: 'text-blue-700', progressBg: 'bg-blue-100', progressFill: 'bg-blue-600' },
  COMPLETED: { bg: 'bg-green-100', text: 'text-green-700', progressBg: 'bg-green-100', progressFill: 'bg-green-600' },
  CANCELED: { bg: 'bg-red-100', text: 'text-red-700', progressBg: 'bg-red-100', progressFill: 'bg-red-600' },
  OVERDUE_EXPIRED: { bg: 'bg-red-100', text: 'text-red-700', progressBg: 'bg-red-100', progressFill: 'bg-red-600' }
};

// Format cycle frequency
const formatDuration = (seconds) => {
  if (!seconds) return "N/A";
  if (seconds < 60) return `${seconds}s`;
  if (seconds < 3600) return `${Math.floor(seconds / 60)}m`;
  if (seconds < 86400) return `${Math.floor(seconds / 3600)}h`;
  return `${Math.floor(seconds / 86400)}d`;
};

// Format estimated completion time from timestamp
const formatETA = (estimatedCompletionAt) => {
  if (!estimatedCompletionAt) return null;
  
  const now = new Date();
  const completionTime = new Date(estimatedCompletionAt);
  const diffMs = completionTime - now;
  
  if (diffMs <= 0) return "Soon";
  
  const diffSeconds = Math.floor(diffMs / 1000);
  if (diffSeconds < 60) return `${diffSeconds}s`;
  if (diffSeconds < 3600) return `${Math.floor(diffSeconds / 60)}m`;
  if (diffSeconds < 86400) return `${Math.floor(diffSeconds / 3600)}h`;
  return `${Math.floor(diffSeconds / 86400)}d`;
};

const columnHelper = createColumnHelper();

export default function LiveDcaTable({
  data,
  isLoading,
  isFetching,
  isFetchingNextPage,
  fetchNextPage,
  hasNextPage,
  isError,
  error,
  filters, // Pass filters down for animationConfig
}) {
  const observerTarget = useRef(null);
  const queryClient = useQueryClient();

  // Animation configuration - TEMPORARILY DISABLED TO DEBUG FREEZING
  const animationConfig = useMemo(() => ({
    getAnimationClass: (item) => {
      return ""; // Disable animations to debug
    },
    clearNewAfter: 2000,
    uniqueKey: 'dca_key',
    dataKey: ["live-dca", {
      search: filters.search,
      status: filters.status === 'all' ? undefined : filters.status,
      minUsd: filters.minUsd.toString(),
      maxUsd: filters.maxUsd,
    }]
  }), [filters.search, filters.status, filters.minUsd, filters.maxUsd]);

  // Intersection observer for infinite scroll - TEMPORARILY DISABLED TO DEBUG FREEZING
  // const handleIntersection = useCallback((entries) => {
  //   console.log('Intersection observed');
  //   const target = entries[0];
  //   if (target.isIntersecting && hasNextPage && !isFetchingNextPage) {
  //     fetchNextPage();
  //   }
  // }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  // useEffect(() => {
  //   const element = observerTarget.current;
  //   if (!element) return;
  //   const observer = new IntersectionObserver(handleIntersection, { threshold: 0.1 });
  //   observer.observe(element);
  //   return () => observer.disconnect();
  // }, [handleIntersection]);

  const columns = useMemo(() => [
    columnHelper.accessor('opened_at', {
      header: 'Opened',
      cell: (info) => (
        <TimeAgoDisplay timestamp={new Date(info.getValue()).getTime()} className="text-xs" />
      ),
    }),
    columnHelper.accessor('user_key', {
      header: 'Wallet',
      cell: (info) => (
        <SolanaWallet 
          address={info.getValue()} 
          size="xs"
          className="text-xs font-mono"
        />
      ),
    }),
    columnHelper.accessor('in_deposited_usd', {
      header: 'Amount',
      cell: (info) => (
        <div className="text-sm font-medium">
          {formatCurrency(info.getValue() || 0, 2)}
        </div>
      ),
    }),
    columnHelper.accessor('progress_percentage', {
      header: 'Progress',
      cell: (info) => {
        const row = info.row.original;
        const status = row.order_status?.toUpperCase() || 'ACTIVE';
        const colors = DCA_COLORS[status] || DCA_COLORS.ACTIVE;
        const progress = info.getValue() || 0;

        if (status === 'COMPLETED') {
          return (
            <div className="w-full space-y-1 mt-2">
              <div className={`w-16 h-2 rounded-full ${colors.progressBg}`}><div className={`h-2 rounded-full transition-all duration-300 ${colors.progressFill}`} style={{ width: '100%' }} /></div>
              <span className={`text-xs ${colors.text}`}>Completed</span>
            </div>
          );
        }
        if (status === 'CANCELED') {
          return (
            <div className="w-full space-y-1 mt-2">
              <div className={`w-16 h-2 rounded-full ${colors.progressBg}`}><div className={`h-2 rounded-full transition-all duration-300 ${colors.progressFill}`} style={{ width: `${progress}%` }} /></div>
              <span className={`text-xs text-center ${colors.text}`}>Canceled</span>
            </div>
          );
        }
        if (status === 'OVERDUE_EXPIRED') {
          return (
            <div className="w-full space-y-1 mt-2">
              <div className={`w-16 h-2 rounded-full ${colors.progressBg}`}><div className={`h-2 rounded-full transition-all duration-300 ${colors.progressFill}`} style={{ width: `${progress}%` }} /></div>
              <span className={`text-xs ${colors.text}`}>Overdue</span>
            </div>
          );
        }
        return (
          <div className="w-full space-y-1 mt-2">
            <div className={`w-16 h-2 rounded-full ${colors.progressBg}`}><div className={`h-2 rounded-full transition-all duration-300 ${colors.progressFill}`} style={{ width: `${Math.min(progress, 100)}%` }} /></div>
            <div className="text-xs">{Number(progress || 0).toFixed(1)}%</div>
          </div>
        );
      },
    }),
    columnHelper.accessor('remaining_input_usd', {
      header: 'Remaining',
      cell: (info) => {
        const row = info.row.original;
        const remaining = info.getValue() || 0;
        const status = row.order_status?.toLowerCase() || 'active';
        const displayRemaining = (status === 'completed' || status === 'canceled') ? 0 : remaining;
        return <div className={`text-sm font-medium transition-all duration-300 ${displayRemaining > 0 ? 'text-green-600' : 'text-gray-500'}`}>{formatCurrency(displayRemaining, 2)}</div>;
      },
    }),
    columnHelper.accessor('input_mint', {
      header: 'Selling', 
      cell: (info) => {
        const row = info.row.original;
        const tokenAddress = info.getValue();
        return <div className="max-w-44"><SolanaToken key={`${row.dca_key}-input-${tokenAddress}`} address={tokenAddress} size="xs" /></div>;
      },
    }),
    columnHelper.accessor('output_mint', {
      header: 'Buying',
      cell: (info) => {
        const row = info.row.original;
        const tokenAddress = info.getValue();
        return <div className="max-w-44"><SolanaToken key={`${row.dca_key}-output-${tokenAddress}`} address={tokenAddress} size="xs" /></div>;
      },
    }),
    columnHelper.accessor('cycle_frequency', {
      header: 'Details',
      cell: (info) => {
        const row = info.row.original;
        const cycleFrequency = info.getValue() || 0;
        const amountPerCycle = row.in_amount_per_cycle || 0;
        const usdPrice = row.input_usd_price || 0;
        const usdAmountPerCycle = amountPerCycle * usdPrice;
        return (
          <div className="text-xs">
            <div className="font-medium">{formatCurrency(usdAmountPerCycle, 2)}</div>
            <div className="text-gray-500">every {formatDuration(cycleFrequency)}</div>
          </div>
        );
      },
    }),
    columnHelper.accessor('impact_scores', {
      header: 'Impact',
      cell: (info) => {
        const row = info.row.original;
        const getScoreColor = (score) => {
          if (score >= 8) return 'text-red-600 font-bold';
          if (score >= 6) return 'text-orange-600 font-medium';
          if (score >= 4) return 'text-yellow-600';
          return 'text-gray-500';
        };
        const scores = row.impactScores;
        const isLoadingThisRow = row.impactScoresLoading;
        if (isLoadingThisRow) {
          return <div className="text-xs space-y-1"><div className="h-3 w-8 bg-gray-200 rounded animate-pulse"></div><div className="h-3 w-8 bg-gray-200 rounded animate-pulse"></div></div>;
        }
        if (!scores) return <div className="text-xs text-gray-500">N/A</div>;
        return (
          <div className="text-xs space-y-1">
            <div className={`${getScoreColor(scores.inputScore)}`}>S: {scores.inputScore}/10</div>
            <div className={`${getScoreColor(scores.outputScore)}`}>B: {scores.outputScore}/10</div>
          </div>
        );
      },
    }),
    columnHelper.accessor('dca_key', {
      header: 'ETA',
      cell: (info) => {
        const row = info.row.original;
        
        // Rule #1: If not active, show N/A
        if (row.is_overdue || 
            row.order_status === 'overdue_expired' || 
            row.order_status === 'completed' || 
            row.order_status === 'canceled') {
          return <div className="text-xs text-gray-500">N/A</div>;
        }
        
        const endingIn = formatETA(row.estimated_completion_at);
        if (!endingIn) return <div className="text-xs text-gray-500">N/A</div>;
        return <div className="text-xs font-medium">{endingIn}</div>;
      },
    }),
  ], []);

  // Impact score fetching logic
  const [impactScoresCache, setImpactScoresCache] = useState({});
  const [loadingDcaKeys, setLoadingDcaKeys] = useState(new Set());

  const missingDcaKeys = useMemo(() => {
    if (!data || data.length === 0) return [];
    return data
      .filter(row => row.dca_key && !impactScoresCache[row.dca_key])
      .map(row => row.dca_key);
  }, [data, impactScoresCache]);

  const { data: newImpactScores, refetch: fetchImpactScores } = useQuery({
    queryKey: ['impact-scores-batch', missingDcaKeys.sort()],
    queryFn: async () => {
      if (missingDcaKeys.length === 0) return {};
      setLoadingDcaKeys(prev => new Set([...prev, ...missingDcaKeys]));
      const ordersToFetch = data.filter(row => missingDcaKeys.includes(row.dca_key));
      const batchSize = 25;
      const batches = [];
      for (let i = 0; i < ordersToFetch.length; i += batchSize) {
        batches.push(ordersToFetch.slice(i, i + batchSize));
      }
      const batchPromises = batches.map(async (batch) => {
        const response = await fetch('/api/impact-scores/batch', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ orders: batch })
        });
        if (!response.ok) return {};
        const result = await response.json();
        return result.success ? result.data : {};
      });
      const batchResults = await Promise.all(batchPromises);
      return batchResults.reduce((acc, batchResult) => ({ ...acc, ...batchResult }), {});
    },
    enabled: false, // ⚡ DEFERRED LOADING - Don't run automatically
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });

  // ⚡ MANUAL TRIGGER - Fetch impact scores AFTER main table loads
  // TEMPORARILY DISABLED TO DEBUG HANGING ISSUE
  // const fetchImpactScoresRef = useRef(fetchImpactScores);
  // fetchImpactScoresRef.current = fetchImpactScores;

  // useEffect(() => {
  //   if (!isLoading && data && missingDcaKeys.length > 0) {
  //     // Trigger impact scores fetch after main table is rendered
  //     fetchImpactScoresRef.current();
  //   }
  // }, [isLoading, data?.length, missingDcaKeys.length]);

  useEffect(() => {
    if (newImpactScores && Object.keys(newImpactScores).length > 0) {
      setImpactScoresCache(prev => ({ ...prev, ...newImpactScores }));
      setLoadingDcaKeys(prev => {
        const newSet = new Set(prev);
        Object.keys(newImpactScores).forEach(key => newSet.delete(key));
        return newSet;
      });
    }
  }, [newImpactScores]);

  // TEMPORARILY SIMPLIFIED TO DEBUG FREEZING - just use raw data
  const tableData = data || [];

  const isUpdating = isFetching && !isLoading && !isFetchingNextPage && tableData.length > 0;

  const skeletonConfig = useMemo(() => ({
    columnWidths: ["w-15", "w-40", "w-15", "w-16", "w-15", "w-36", "w-36", "w-12", "w-10", "w-8"],
    columnHeights: ["h-5", "h-7", "h-6", "h-7", "h-6", "h-10", "h-10", "h-7", "h-7", "h-5"]
  }), []);

  if (isError) {
    return <div className="text-red-500 p-4 text-center">Error loading orders: {error?.message}</div>;
  }

  return (
    <div className="mt-8">
      <StalkchainTableComponent
        data={tableData}
        columns={columns}
        isLoading={isLoading}
        skeletonRows={20}
        skeletonConfig={skeletonConfig}
        variant="default"
        size="default"
        enableSorting={false}
        enableAnimations={true}
        animationConfig={animationConfig}
        queryClient={queryClient}
        showLoadingBanner={isUpdating}
        tableOptions={{
          getRowId: (row) => row.dca_key,
        }}
        emptyTitle="No DCA Orders"
        emptyMessage="No DCA orders found. Orders will appear here in real-time."
        footerContent={
          hasNextPage && (
            <div 
              ref={observerTarget} 
              className="flex w-full justify-center p-4 h-20"
            >
              <Spinner size="sm" />
            </div>
          )
        }
      />
    </div>
  );
} 