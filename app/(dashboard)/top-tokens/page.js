"use server";

import dynamic from "next/dynamic";
import WithAuth from "@/hooks/withAuth";
import { dashboardNavItems } from "@/config/dashboard";
const PageContent = dynamic(() => import("@/content/top-tokens/page"));

export async function generateMetadata() {
  let pathname = "/top-tokens";

  const flatNavItems = dashboardNavItems.flatMap(section => section.items);
  const thisPathLabel = flatNavItems.find(item => item.href === pathname)?.label;
  return {
    title: thisPathLabel || '',
  };
}

export default async function Page() {
  const AuthenticatedPageContent = await WithAuth(PageContent);
  return <AuthenticatedPageContent />;
}