# Setting Up TimescaleDB for Jupiter DCA Data: Step-by-Step Guide

## Database Naming Conventions and Best Practices

### Table Naming Rules (STRICT)

1. **Prefixing System**:
   - Time-series events/streams: `stream_[platform]_[entity]` (e.g., `stream_jupiter_dca`, `stream_wallets_fresh`)
   - Reference data: `ref_[entity]_[purpose]` (e.g., `ref_tokens_metadata`, `ref_tokens_prices`)
   - Aggregated data: `agg_[platform]_[entity]_[timeframe]` (e.g., `agg_jupiter_volume_hourly`)
   - Classifications/lists: `list_[entity]_[type]` (e.g., `list_wallets_cex`, `list_tokens_spam`)

2. **Hypertable Designation**:
   - Hypertables MUST use a `_ht` suffix regardless of their prefix (e.g., `stream_jupiter_dca_ht`, `ref_tokens_prices_ht`)
   - Time-series data tables MUST be hypertables (including price histories, event streams, etc.)
   - Metadata/classification tables that aren't time-series should NOT be hypertables

   **Reference vs. List Tables Explained**:
   - `ref_` tables: Contain comprehensive metadata and attributes about entities. They serve as the authoritative source of information and often have relationships with many other tables. Examples:
     - `ref_tokens_metadata` - Contains static token details like name, symbol, decimals, etc. (not a hypertable)
     - `ref_tokens_prices_ht` - Contains historical token price information (hypertable with time-series data)
     - `ref_markets_info` - Contains trading pair information (not a hypertable)
     
   - `list_` tables: Contain classifications or categorizations used for filtering or tagging. They are typically binary (in/out) or categorical in nature. Examples:
     - `list_tokens_spam` - List of token addresses identified as spam
     - `list_wallets_cex` - List of wallet addresses belonging to centralized exchanges
     - `list_tokens_stablecoin` - List of tokens classified as stablecoins

3. **Naming Format**:
   - All table names MUST use lowercase snake_case
   - Table names MUST be plural (e.g., `wallets_fresh` not `wallet_fresh`)
   - Names MUST be descriptive and indicate purpose
   - Maximum name length: 63 characters (PostgreSQL limit)

4. **Column Naming**:
   - Primary keys: `id` for auto-incrementing keys
   - Timestamps: Use `time` for hypertable partitioning field
   - Foreign keys: `[table_singular]_id` (e.g., `token_id`, `wallet_id`)
   - Boolean flags: Use `is_[state]` format (e.g., `is_active`, `is_spam`)
   - All column names MUST use lowercase snake_case

5. **Index Naming**:
   - Format: `idx_[tablename]_[columnname(s)]` (e.g., `idx_stream_jupiter_dca_ht_time`)
   - Compound indexes: List columns in order (e.g., `idx_stream_jupiter_dca_ht_user_time`)
   - Partial indexes: Add condition hint (e.g., `idx_stream_jupiter_dca_ht_type_opened`)

### Schema Organization

- Use the public schema for all tables
- Use consistent prefixes as defined above for logical grouping
- Use TimescaleDB features appropriately: hypertables for time-series data only

## Prerequisites

- PostgreSQL 12+ installed
- TimescaleDB extension installed
- Database user with CREATE privileges

## Step 1: Enable TimescaleDB Extension

```sql
CREATE EXTENSION IF NOT EXISTS timescaledb;
```

## Step 2: Create the Main Hypertable

Based on your Jupiter DCA data structure and requirements, here's an optimized schema:

```sql
-- Create the main table first (required before converting to hypertable)
CREATE TABLE stream_jupiter_dca (
    -- Transaction identifier (no longer a PRIMARY KEY by itself)
    signature TEXT NOT NULL,   -- Solana transaction signature
    
    -- Event type classifier
    event_type TEXT NOT NULL CHECK (event_type IN ('fill', 'opened', 'closed')),
    
    -- Time dimensions (crucial for time-series)
    time TIMESTAMPTZ NOT NULL,        -- Timestamp for time-series partitioning
    slot BIGINT NOT NULL,             -- Solana slot for ordering/reference
    
    -- Entity relationships
    user_key TEXT NOT NULL,           -- User who owns/initiated the DCA order
    dca_key TEXT NOT NULL,            -- Unique DCA order identifier
    
    -- Token information
    input_mint TEXT NOT NULL,         -- Input token address (sell token)
    output_mint TEXT NOT NULL,        -- Output token address (buy token)
    input_decimals INTEGER,           -- Decimal places for input token
    output_decimals INTEGER,          -- Decimal places for output token
    
    -- Price information
    input_usd_price NUMERIC(24, 12),  -- USD price of input token
    output_usd_price NUMERIC(24, 12), -- USD price of output token
    
    -- OPENED event fields
    in_deposited NUMERIC(36, 12),            -- Amount initially deposited
    cycle_frequency INTEGER,                 -- Seconds between swaps
    in_amount_per_cycle NUMERIC(36, 12),     -- Amount to swap per cycle
    created_at TIMESTAMPTZ,                  -- When the DCA was created
    in_deposited_usd NUMERIC(36, 12),        -- USD value of deposit
    
    -- FILL event fields
    input_amount NUMERIC(36, 12),            -- Input amount in this fill
    output_amount NUMERIC(36, 12),           -- Output amount in this fill
    fee_mint TEXT,                           -- Fee token address
    fee_amount NUMERIC(36, 12),              -- Fee amount paid
    price_per_token NUMERIC(36, 12),         -- Execution price (input/output)
    price_inverse NUMERIC(36, 12),           -- Inverse price (output/input)
    input_amount_usd NUMERIC(36, 12),        -- USD value of input
    output_amount_usd NUMERIC(36, 12),       -- USD value of output
    
    -- CLOSED event fields
    total_in_withdrawn NUMERIC(36, 12),      -- Input withdrawn when closed
    total_out_withdrawn NUMERIC(36, 12),     -- Output withdrawn when closed
    unfilled_amount NUMERIC(36, 12),         -- Unfilled/returned amount
    user_closed BOOLEAN,                     -- True if user canceled
    total_in_withdrawn_usd NUMERIC(36, 12),  -- USD value of input withdrawn
    total_out_withdrawn_usd NUMERIC(36, 12), -- USD value of output withdrawn
    unfilled_amount_usd NUMERIC(36, 12),     -- USD value of unfilled amount
    
    -- Full message storage (optional but useful)
    raw_data JSONB,                          -- Original message content
    
    -- Create a unique constraint including time column
    UNIQUE (signature, time)
);

-- Convert to a TimescaleDB hypertable
SELECT create_hypertable('stream_jupiter_dca', 'time');

-- Rename to add the _ht suffix to indicate it's a hypertable
ALTER TABLE stream_jupiter_dca RENAME TO stream_jupiter_dca_ht;
```

## Step 3: Create Optimized Indexes

Based on your requirements, create these indexes to optimize common query patterns:

```sql
-- Index for filtering by event type and time (common query pattern)
CREATE INDEX idx_stream_jupiter_dca_ht_type_time ON stream_jupiter_dca_ht(event_type, time DESC);

-- Index for user queries (finding all DCAs for a user)
CREATE INDEX idx_stream_jupiter_dca_ht_user_time ON stream_jupiter_dca_ht(user_key, time DESC);

-- Index for tracking specific DCA orders through their lifecycle
CREATE INDEX idx_stream_jupiter_dca_ht_dca_time ON stream_jupiter_dca_ht(dca_key, time DESC);

-- Index for token pair analysis (find all events for a token pair)
CREATE INDEX idx_stream_jupiter_dca_ht_tokens_time ON stream_jupiter_dca_ht(input_mint, output_mint, time DESC);

-- Specialized indexes for each event type (improves filtering)
CREATE INDEX idx_stream_jupiter_dca_ht_type_opened ON stream_jupiter_dca_ht(time DESC) 
WHERE event_type = 'opened';

CREATE INDEX idx_stream_jupiter_dca_ht_type_fill ON stream_jupiter_dca_ht(time DESC) 
WHERE event_type = 'fill';

CREATE INDEX idx_stream_jupiter_dca_ht_type_closed ON stream_jupiter_dca_ht(time DESC) 
WHERE event_type = 'closed';

-- Index for active DCAs (opened but not closed)
CREATE INDEX idx_stream_jupiter_dca_ht_dca_opened ON stream_jupiter_dca_ht(dca_key, time DESC) 
WHERE event_type = 'opened';

CREATE INDEX idx_stream_jupiter_dca_ht_dca_closed ON stream_jupiter_dca_ht(dca_key, time DESC) 
WHERE event_type = 'closed';
```

## Step 4: Configure TimescaleDB Compression

Compression helps reduce storage costs and can improve query performance for historical data:

```sql
-- Configure compression settings (customize retention period as needed)
ALTER TABLE stream_jupiter_dca_ht SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'event_type,dca_key,user_key',
    timescaledb.compress_orderby = 'time DESC'
);

-- Add compression policy (compress chunks older than 7 days)
SELECT add_compression_policy('stream_jupiter_dca_ht', INTERVAL '7 days');
```

## Step 5: Create Data Insertion Function

Create a function with robust error handling to streamline data insertion:

```sql
CREATE OR REPLACE FUNCTION public.insert_stream_jupiter_dca(
    p_event_type text, 
    p_time timestamptz, 
    p_slot int8, 
    p_signature text, 
    p_user_key text, 
    p_dca_key text, 
    p_input_mint text, 
    p_output_mint text, 
    p_input_decimals int4, 
    p_output_decimals int4, 
    p_input_usd_price numeric, 
    p_output_usd_price numeric, 
    -- OPENED fields
    p_in_deposited numeric = NULL::numeric, 
    p_cycle_frequency int4 = NULL::integer, 
    p_in_amount_per_cycle numeric = NULL::numeric, 
    p_created_at timestamptz = NULL::timestamp with time zone, 
    p_in_deposited_usd numeric = NULL::numeric, 
    -- FILL fields
    p_input_amount numeric = NULL::numeric, 
    p_output_amount numeric = NULL::numeric, 
    p_fee_mint text = NULL::text, 
    p_fee_amount numeric = NULL::numeric, 
    p_price_per_token numeric = NULL::numeric, 
    p_price_inverse numeric = NULL::numeric, 
    p_input_amount_usd numeric = NULL::numeric, 
    p_output_amount_usd numeric = NULL::numeric, 
    -- CLOSED fields
    p_total_in_withdrawn numeric = NULL::numeric, 
    p_total_out_withdrawn numeric = NULL::numeric, 
    p_unfilled_amount numeric = NULL::numeric, 
    p_user_closed bool = NULL::boolean, 
    p_total_in_withdrawn_usd numeric = NULL::numeric, 
    p_total_out_withdrawn_usd numeric = NULL::numeric, 
    p_unfilled_amount_usd numeric = NULL::numeric, 
    -- Raw data
    p_raw_data jsonb = NULL::jsonb
) RETURNS void AS $BODY$
DECLARE
    v_signature_exists BOOLEAN;
BEGIN
    -- Check if signature already exists to avoid duplicates
    SELECT EXISTS(
        SELECT 1 FROM stream_jupiter_dca_ht
        WHERE signature = p_signature
    ) INTO v_signature_exists;
    
    -- Only insert if signature doesn't already exist
    IF NOT v_signature_exists THEN
        BEGIN
            INSERT INTO stream_jupiter_dca_ht (
                event_type, time, slot, signature, user_key, dca_key,
                input_mint, output_mint, input_decimals, output_decimals,
                input_usd_price, output_usd_price,
                -- OPENED fields
                in_deposited, cycle_frequency, in_amount_per_cycle, created_at, in_deposited_usd,
                -- FILL fields
                input_amount, output_amount, fee_mint, fee_amount,
                price_per_token, price_inverse, input_amount_usd, output_amount_usd,
                -- CLOSED fields
                total_in_withdrawn, total_out_withdrawn, unfilled_amount, user_closed,
                total_in_withdrawn_usd, total_out_withdrawn_usd, unfilled_amount_usd,
                -- Raw data
                raw_data
            )
            VALUES (
                p_event_type, p_time, p_slot, p_signature, p_user_key, p_dca_key,
                p_input_mint, p_output_mint, p_input_decimals, p_output_decimals,
                p_input_usd_price, p_output_usd_price,
                -- OPENED fields
                p_in_deposited, p_cycle_frequency, p_in_amount_per_cycle, p_created_at, p_in_deposited_usd,
                -- FILL fields
                p_input_amount, p_output_amount, p_fee_mint, p_fee_amount,
                p_price_per_token, p_price_inverse, p_input_amount_usd, p_output_amount_usd,
                -- CLOSED fields
                p_total_in_withdrawn, p_total_out_withdrawn, p_unfilled_amount, p_user_closed,
                p_total_in_withdrawn_usd, p_total_out_withdrawn_usd, p_unfilled_amount_usd,
                -- Raw data
                p_raw_data
            );
            
            -- Optional: Log successful insertion
            RAISE DEBUG 'Inserted event: % (type: %)', p_signature, p_event_type;
        EXCEPTION 
            WHEN unique_violation THEN
                -- This is a safety check in case of race conditions
                RAISE NOTICE 'Duplicate event detected despite check: %', p_signature;
            WHEN others THEN
                -- Log other errors
                RAISE EXCEPTION 'Error inserting event %: %', p_signature, SQLERRM;
        END;
    ELSE
        -- Log skipped duplicates
        RAISE NOTICE 'Skipping duplicate event: %', p_signature;
    END IF;
END;
$BODY$ LANGUAGE plpgsql VOLATILE COST 100;
```

## Step 6: JavaScript Integration Example

Here's how to integrate this with your Node.js application:

```javascript
// Example snippet for your Kafka processor or direct insertion
const { Pool } = require('pg');

// Database connection pool
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
});

// Function to insert event into TimescaleDB
async function insertDcaEvent(event) {
  try {
    // Extract ISO datetime from event
    const eventTime = new Date(event.datetime);
    
    // Build parameter list based on event type
    const params = [
      event.event_type,
      eventTime,
      parseInt(event.slot),
      event.signature,
      event.user_key,
      event.dca_key,
      event.input_mint,
      event.output_mint,
      event.input_decimals,
      event.output_decimals,
      event.input_usd_price,
      event.output_usd_price
    ];
    
    // Add event-specific fields
    if (event.event_type === 'opened') {
      params.push(
        event.in_deposited,
        event.cycle_frequency,
        event.in_amount_per_cycle,
        new Date(event.created_at * 1000), // Convert Unix timestamp to date
        event.in_deposited_usd
      );
    } else if (event.event_type === 'fill') {
      // Add NULL values for OPENED-specific fields
      params.push(null, null, null, null, null);
      
      // Add FILL-specific fields
      params.push(
        event.input_amount,
        event.output_amount,
        event.fee_mint,
        event.fee_amount,
        event.price_per_token,
        event.price_inverse,
        event.input_amount_usd,
        event.output_amount_usd
      );
    } else if (event.event_type === 'closed') {
      // Add OPENED fields (some are shared with CLOSED)
      params.push(
        event.in_deposited,
        event.cycle_frequency,
        event.in_amount_per_cycle,
        new Date(event.created_at * 1000),
        event.in_deposited_usd
      );
      
      // Add NULL values for FILL-specific fields
      params.push(null, null, null, null, null, null, null, null);
      
      // Add CLOSED-specific fields
      params.push(
        event.total_in_withdrawn,
        event.total_out_withdrawn,
        event.unfilled_amount,
        event.user_closed,
        event.total_in_withdrawn_usd,
        event.total_out_withdrawn_usd,
        event.unfilled_amount_usd
      );
    }
    
    // Add raw JSON data
    params.push(JSON.stringify(event));
    
    // Create placeholder string for SQL query
    const placeholders = params.map((_, i) => `$${i + 1}`).join(', ');
    
    // Insert data using the function
    await pool.query(`SELECT insert_stream_jupiter_dca(${placeholders})`, params);
    
    console.log(`Inserted ${event.event_type} event for DCA key: ${event.dca_key}`);
  } catch (error) {
    console.error('Error inserting into TimescaleDB:', error);
    console.error('Failed event:', event.signature);
  }
}

// When you process an event
async function processEvent(event) {
  await insertDcaEvent(event);
}
```

## Step 7: Verify Your Setup

After setting up, verify your configuration:

```sql
-- Check that hypertable was created correctly
SELECT * FROM timescaledb_information.hypertables 
WHERE hypertable_name = 'stream_jupiter_dca_ht';

-- Check indexes
\d stream_jupiter_dca_ht

-- Check compression settings
SELECT * FROM timescaledb_information.compression_settings 
WHERE hypertable_name = 'stream_jupiter_dca_ht';
```

## Key Optimization Notes

1. The `time` column is used for partitioning the hypertable, creating efficient "chunks" of data.
2. The unique constraint on `(signature, time)` ensures no duplicate transactions while being compatible with TimescaleDB's requirements.
3. The duplicate detection in the insertion function provides an additional safety layer even beyond the database constraint.
4. The specialized indexes support your key requirements:
   - Finding all active DCAs (opened but not closed)
   - Retrieving all events for specific tokens
   - Finding all DCAs associated with a user
5. The compression policy helps manage data storage efficiently while keeping recent data in uncompressed form for faster access.

## Next Steps

After setting up this foundation, you can add:
1. Continuous aggregates for common analytics
2. Retention policies if you want to automatically remove old data
3. User-defined functions for common analysis patterns
4. Views for simplified querying

This optimized schema allows you to track the complete lifecycle of DCA orders while supporting efficient querying for all your stated requirements.
