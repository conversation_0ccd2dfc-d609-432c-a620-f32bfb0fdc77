/*
 Navicat Premium Dump SQL

 Source Server         : Stalkchain postgre
 Source Server Type    : PostgreSQL
 Source Server Version : 170004 (170004)
 Source Host           : db-postgresql-nyc3-07797-do-user-17612973-0.m.db.ondigitalocean.com:25060
 Source Catalog        : defaultdb
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 170004 (170004)
 File Encoding         : 65001

 Date: 16/06/2025 15:23:05
*/


-- ----------------------------
-- Table structure for ref_tokens_prices_ht
-- ----------------------------
DROP TABLE IF EXISTS "public"."ref_tokens_prices_ht";
CREATE TABLE "public"."ref_tokens_prices_ht" (
  "time" timestamptz(6) NOT NULL,
  "mint_address" varchar(44) COLLATE "pg_catalog"."default" NOT NULL,
  "price_usd" numeric(36,12) NOT NULL,
  "source_type" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "source_signature" varchar(88) COLLATE "pg_catalog"."default",
  "source_data" jsonb
)
;
ALTER TABLE "public"."ref_tokens_prices_ht" OWNER TO "doadmin";

-- ----------------------------
-- Indexes structure for table ref_tokens_prices_ht
-- ----------------------------
CREATE INDEX "idx_ref_tokens_prices_ht_mint_closest" ON "public"."ref_tokens_prices_ht" USING btree (
  "mint_address" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_ref_tokens_prices_ht_mint_source" ON "public"."ref_tokens_prices_ht" USING btree (
  "mint_address" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "source_type" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "time" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_ref_tokens_prices_ht_mint_time" ON "public"."ref_tokens_prices_ht" USING btree (
  "mint_address" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "time" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);
CREATE INDEX "idx_ref_tokens_prices_time_range" ON "public"."ref_tokens_prices_ht" USING btree (
  "mint_address" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "time" "pg_catalog"."timestamptz_ops" ASC NULLS LAST
);
CREATE INDEX "idx_ref_tokens_special" ON "public"."ref_tokens_prices_ht" USING btree (
  "mint_address" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
) WHERE mint_address::text = ANY (ARRAY['So11111111111111111111111111111111111111112'::character varying, 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'::character varying, 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB'::character varying]::text[]);
CREATE INDEX "ref_tokens_prices_time_idx" ON "public"."ref_tokens_prices_ht" USING btree (
  "time" "pg_catalog"."timestamptz_ops" DESC NULLS FIRST
);

-- ----------------------------
-- Triggers structure for table ref_tokens_prices_ht
-- ----------------------------
CREATE TRIGGER "ts_insert_blocker" BEFORE INSERT ON "public"."ref_tokens_prices_ht"
FOR EACH ROW
EXECUTE PROCEDURE "_timescaledb_functions"."insert_blocker"();

-- ----------------------------
-- Primary Key structure for table ref_tokens_prices_ht
-- ----------------------------
ALTER TABLE "public"."ref_tokens_prices_ht" ADD CONSTRAINT "ref_tokens_prices_pkey" PRIMARY KEY ("time", "mint_address", "source_type");
