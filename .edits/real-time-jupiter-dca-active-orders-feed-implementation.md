# Real-time Jupiter DCA Active Orders Feed Implementation

### Table of Contents

1. System Architecture Overview
2. Data Flow
3. Event Types and Schema
4. Implementation Goals
5. Database Implementation
6. Monitoring and Maintenance

### System Architecture Overview

#### High-Level Architecture

```
Kafka Stream (stalkchain-dca-swaps)
    ↓
PostgreSQL Insert Function
    ↓
stream_jupiter_dca_ht (Hypertable)
    ↓
Event Trigger (fn_jupiter_dca_event_handler_listener)
    ↓
stream_jupiter_dca_active_orders (State Table)
    ↓
PostgreSQL NOTIFY → Real-time Listeners
```

#### Components

1. **Data Source**: Kafka topic `stalkchain-dca-swaps` providing real-time DCA events
2. **Storage Layer**: TimescaleDB hypertable `stream_jupiter_dca_ht` for permanent event storage
3. **State Management**: Regular table `stream_jupiter_dca_active_orders` for current active orders
4. **Event Processing**: PostgreSQL triggers for real-time updates
5. **Notification System**: PostgreSQL NOTIFY/LISTEN for downstream systems
6. **Cleanup Jobs**: Periodic functions to remove overdue orders

### Data Flow

#### 1. Event Ingestion Flow

```
Jupiter DCA Transaction on Solana
    ↓
Blockchain Processor (stalkchain-kp-jupdca)
    ↓
Kafka Producer
    ↓
Kafka Topic: stalkchain-dca-swaps
    ↓
Your Kafka Consumer
    ↓
insert_stream_jupiter_dca() function
    ↓
stream_jupiter_dca_ht (hypertable)
```

#### 2. Active Orders Update Flow

```
New Event in stream_jupiter_dca_ht
    ↓
AFTER INSERT Trigger Fires
    ↓
fn_jupiter_dca_event_handler_listener()
    ↓
Check: Is token spam? → Skip if yes
    ↓
Process based on event_type:
    - opened → INSERT into active orders
    - fill → UPDATE active orders progress
    - closed → DELETE from active orders
    ↓
Send PostgreSQL NOTIFY
```

#### 3. Overdue Detection Flow

```
Scheduled Job (every minute)
    ↓
fn_jupiter_dca_check_overdue_orders()
    ↓
Mark orders as overdue
    ↓
Remove orders overdue > 5 minutes
    ↓
Send notifications for removed orders
```

### Event Types and Schema

#### DCA Lifecycle

Each DCA order follows this lifecycle:

1. **OPENED** - User creates a new DCA order
2. **FILL** - Periodic swaps execute (multiple times)
3. **CLOSED** - Order completes or user cancels

#### Event Type: OPENED

Triggered when a user creates a new DCA order.

**Key Fields:**

```javascript
{
  "event_type": "opened",
  "slot": 332979063,
  "timestamp": 1744462621,
  "datetime": "2025-04-12T12:57:01.000Z",
  "signature": "5Xc4Mwc1jpi3WkUcmh9b8h...",  // Transaction signature
  "user_key": "G54YVGAWq2J28moGvtju9q...",    // User's wallet
  "dca_key": "5BZmCarmaNZekLhK6n5ByxD...",    // Unique DCA identifier
  "in_deposited": 700000,                      // Total amount to DCA (UI amount)
  "input_mint": "6xAtWWHmXdZvtBLZu4D4PE...",   // Token to sell
  "output_mint": "EyTRi25eJmmc52PcfgjjND...",  // Token to buy
  "input_decimals": 6,
  "output_decimals": 6,
  "cycle_frequency": 60,                       // Seconds between swaps
  "in_amount_per_cycle": 350000,              // Amount per swap (UI amount)
  "created_at": 1744462621,                    // Unix timestamp
  "input_usd_price": 0.00016587,              // Current USD price
  "output_usd_price": 0.00008471,
  "in_deposited_usd": 116.12                  // Total USD value
}
```

**Important Notes:**

* All amounts are "UI amounts" (already decimal-adjusted)
* `cycle_frequency` determines when the order should execute next
* `in_amount_per_cycle` × number of cycles should ≈ `in_deposited`

#### Event Type: FILL

Triggered each time a DCA swap executes.

**Key Fields:**

```javascript
{
  "event_type": "fill",
  "slot": 332979066,
  "timestamp": 1744462623,
  "datetime": "2025-04-12T12:57:03.000Z",
  "signature": "2xeCQWRpEcYSFMNBNGo7aFQ...",
  "user_key": "G54YVGAWq2J28moGvtju9q...",
  "dca_key": "5BZmCarmaNZekLhK6n5ByxD...",    // Links to OPENED event
  "input_mint": "6xAtWWHmXdZvtBLZu4D4PE...",
  "output_mint": "EyTRi25eJmmc52PcfgjjND...",
  "input_amount": 350000,                      // Amount swapped (UI amount)
  "output_amount": 680905.348029,              // Amount received (UI amount)
  "fee_mint": "EyTRi25eJmmc52PcfgjjND...",    // Fee token
  "fee_amount": 681.586934,                    // Fee paid (UI amount)
  "price_per_token": 0.514021,                 // Execution price
  "price_inverse": 1.945443,
  "input_usd_price": 0.0001663,
  "output_usd_price": 0.0000847,
  "input_amount_usd": 58.22,                   // USD value swapped
  "output_amount_usd": 57.68                   // USD value received
}
```

**Processing Logic:**

* Updates `last_fill_at` timestamp
* Calculates next expected fill time
* Updates running totals and averages
* Resets overdue status

#### Event Type: CLOSED

Triggered when a DCA order completes or is cancelled.

**Key Fields:**

```javascript
{
  "event_type": "closed",
  "slot": 332979016,
  "timestamp": 1744462603,
  "datetime": "2025-04-12T12:56:43.000Z",
  "signature": "3gLr6PPdiMMGYCodNa6JPuk...",
  "user_key": "G7JMZzn6z42XtzyF5D3bcyQ...",
  "dca_key": "6Sm3SxkUvy9bN5XX9ftm1Mye...",
  "in_deposited": 602329.634766,
  "total_in_withdrawn": 0,                     // Unspent input returned
  "total_out_withdrawn": 2.27886621,           // Total output received
  "unfilled_amount": 0,                        // Remaining unexecuted amount
  "user_closed": false,                        // false = completed, true = cancelled
  "input_usd_price": 0.00046757,
  "output_usd_price": 125.89155,
  "in_deposited_usd": 281.63,
  "total_in_withdrawn_usd": 0,
  "total_out_withdrawn_usd": 286.89,
  "unfilled_amount_usd": 0
}
```

**Important Flags:**

* `user_closed = true`: User manually cancelled
* `user_closed = false`: DCA completed naturally

### Implementation Goals

* **Real-time Active Orders Tracking**: Maintain an up-to-date table of all currently active DCA orders
* **Event-Driven Updates**: React instantly to opened, filled, and closed DCA events
* **Automatic Overdue Detection**: Mark orders as overdue and remove those overdue by >5 minutes
* **Spam Token Filtering**: Automatically exclude orders involving known spam tokens
* **Progress Tracking**: Track fills, amounts spent/received, and calculate progress percentages
* **Price Monitoring**: Track current USD prices and calculate total USD values
* **Performance Optimization**: Use simple inserts/updates/deletes instead of complex views
* **Real-time Notifications**: Emit PostgreSQL notifications for downstream systems

#### **PostgreSQL Notifications**

| **Channel**                  | **Triggered By** | **Payload Contains**                        | **Purpose**                    |
| ---------------------------- | ---------------- | ------------------------------------------- | ------------------------------ |
| `jupiter_dca_order_opened`   | OPENED event     | dca\_key, user\_key, token mints, USD value | New order created              |
| `jupiter_dca_order_filled`   | FILL event       | dca\_key, amounts, USD values, price        | Fill executed                  |
| `jupiter_dca_order_closed`   | CLOSED event     | dca\_key, order\_status, withdrawal amounts | Order completed/canceled       |
| `jupiter_dca_orders_expired` | Overdue function | count, timestamp, action                    | Orders marked overdue\_expired |

#### **Scheduled Function**

**Function Name**: `fn_jupiter_dca_check_overdue_orders()`\
**Run Schedule**: Every minute (via pg\_cron)\
**Purpose**: Mark overdue orders and expire those >5 minutes overdue\
**Notification**: Sends `jupiter_dca_orders_expired` when orders are marked as overdue\_expired

### Implementation

#### Step 1: Create the Active Orders Table

**Note:** This is NOT a hypertable because it tracks current state, not time-series data. Hypertables are for append-only time-series data, while this table requires updates and deletes.

```sql
-- Create table for tracking active Jupiter DCA orders
-- This is a state table, not a time-series table, so it's not a hypertable
CREATE TABLE stream_jupiter_dca_active_orders (
    dca_key TEXT PRIMARY KEY,
    user_key TEXT NOT NULL,
    
    -- Token information
    input_mint TEXT NOT NULL,
    output_mint TEXT NOT NULL,
    input_decimals INTEGER NOT NULL,
    output_decimals INTEGER NOT NULL,
    
    -- DCA configuration
    in_deposited NUMERIC(36, 12) NOT NULL,
    in_amount_per_cycle NUMERIC(36, 12) NOT NULL,
    cycle_frequency INTEGER NOT NULL,
    
    -- Timing information
    created_at TIMESTAMPTZ NOT NULL,
    opened_at TIMESTAMPTZ NOT NULL,
    last_fill_at TIMESTAMPTZ,
    next_expected_fill_at TIMESTAMPTZ,
    estimated_completion_at TIMESTAMPTZ,
    closed_at TIMESTAMPTZ,
    
    -- Progress tracking
    total_fills INTEGER DEFAULT 0,
    expected_total_fills INTEGER NOT NULL,
    total_input_spent NUMERIC(36, 12) DEFAULT 0,
    total_output_received NUMERIC(36, 12) DEFAULT 0,
    last_fill_input_amount NUMERIC(36, 12),
    last_fill_output_amount NUMERIC(36, 12),
    
    -- Price information (updated on each fill)
    input_usd_price NUMERIC(24, 12),
    output_usd_price NUMERIC(24, 12),
    last_fill_price_per_token NUMERIC(36, 12),
    average_fill_price NUMERIC(36, 12),
    
    -- USD value calculations
    in_deposited_usd NUMERIC(36, 12),
    total_input_spent_usd NUMERIC(36, 12) DEFAULT 0,
    total_output_received_usd NUMERIC(36, 12) DEFAULT 0,
    remaining_input_usd NUMERIC(36, 12),
    
    -- Status and timing
    order_status TEXT NOT NULL DEFAULT 'active' CHECK (order_status IN ('active', 'completed', 'canceled', 'overdue_expired')),
    is_overdue BOOLEAN DEFAULT false,
    minutes_overdue NUMERIC(10, 2) DEFAULT 0,
    
    -- Metadata
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX idx_jupiter_dca_active_orders_user ON stream_jupiter_dca_active_orders(user_key);
CREATE INDEX idx_jupiter_dca_active_orders_status ON stream_jupiter_dca_active_orders(order_status);
CREATE INDEX idx_jupiter_dca_active_orders_status_user ON stream_jupiter_dca_active_orders(order_status, user_key);
CREATE INDEX idx_jupiter_dca_active_orders_next_fill ON stream_jupiter_dca_active_orders(next_expected_fill_at) WHERE order_status = 'active';
CREATE INDEX idx_jupiter_dca_active_orders_estimated_completion ON stream_jupiter_dca_active_orders(estimated_completion_at) WHERE order_status = 'active' AND estimated_completion_at IS NOT NULL;
CREATE INDEX idx_jupiter_dca_active_orders_overdue ON stream_jupiter_dca_active_orders(is_overdue) WHERE is_overdue = true AND order_status = 'active';
CREATE INDEX idx_jupiter_dca_active_orders_updated ON stream_jupiter_dca_active_orders(updated_at);
CREATE INDEX idx_jupiter_dca_active_orders_tokens ON stream_jupiter_dca_active_orders(input_mint, output_mint);
CREATE INDEX idx_jupiter_dca_active_orders_closed_at ON stream_jupiter_dca_active_orders(closed_at) WHERE order_status IN ('completed', 'canceled', 'overdue_expired');
```

#### Step 2: Create Event Handler Function

This function processes incoming DCA events and updates the active orders table accordingly.

```sql
CREATE OR REPLACE FUNCTION fn_jupiter_dca_maintain_active_orders()
RETURNS TRIGGER AS $$
DECLARE
    v_is_spam BOOLEAN;
    v_is_stablecoin_trade BOOLEAN;
    v_is_recent BOOLEAN;
    v_has_backfill_history BOOLEAN;
    v_should_notify BOOLEAN;
    v_input_is_stablecoin BOOLEAN;
    v_output_is_stablecoin BOOLEAN;
    v_input_is_sol_derivative BOOLEAN;
    v_output_is_sol_derivative BOOLEAN;
BEGIN
    -- Check if either token is spam
    SELECT EXISTS(
        SELECT 1 FROM list_tokens_spam 
        WHERE mint_address IN (NEW.input_mint, NEW.output_mint)
    ) INTO v_is_spam;
    
    -- Skip spam tokens entirely
    IF v_is_spam THEN
        RAISE DEBUG 'Skipping spam token trade: input=%, output=%', NEW.input_mint, NEW.output_mint;
        RETURN NEW;
    END IF;
    
    -- Check token classifications for filtering
    SELECT EXISTS(
        SELECT 1 FROM ref_stablecoins 
        WHERE address = NEW.input_mint
    ) INTO v_input_is_stablecoin;
    
    SELECT EXISTS(
        SELECT 1 FROM ref_stablecoins 
        WHERE address = NEW.output_mint
    ) INTO v_output_is_stablecoin;
    
    SELECT EXISTS(
        SELECT 1 FROM ref_sol_derivatives 
        WHERE address = NEW.input_mint
    ) INTO v_input_is_sol_derivative;
    
    SELECT EXISTS(
        SELECT 1 FROM ref_sol_derivatives 
        WHERE address = NEW.output_mint
    ) INTO v_output_is_sol_derivative;
    
    -- Determine if this is a filtered trade type
    v_is_stablecoin_trade := (
        -- Stablecoin to Stablecoin
        (v_input_is_stablecoin AND v_output_is_stablecoin) OR
        -- Stablecoin to SOL derivative  
        (v_input_is_stablecoin AND v_output_is_sol_derivative) OR
        -- SOL derivative to Stablecoin
        (v_input_is_sol_derivative AND v_output_is_stablecoin) OR
        -- SOL derivative to SOL derivative
        (v_input_is_sol_derivative AND v_output_is_sol_derivative)
    );
    
    -- Skip filtered trade types
    IF v_is_stablecoin_trade THEN
        RAISE DEBUG 'Skipping filtered trade: input=% (stable=%, sol_deriv=%), output=% (stable=%, sol_deriv=%)', 
            NEW.input_mint, v_input_is_stablecoin, v_input_is_sol_derivative,
            NEW.output_mint, v_output_is_stablecoin, v_output_is_sol_derivative;
        RETURN NEW;
    END IF;
    
    -- Determine processing strategy (same as before)
    v_is_recent := (NEW.time > NOW() - INTERVAL '15 seconds');
    
    IF NOT v_is_recent THEN
        SELECT EXISTS(
            SELECT 1 FROM backfill_jupiter_dca_ht 
            WHERE dca_key = NEW.dca_key
        ) INTO v_has_backfill_history;
        
        -- Skip backfill events - batch processing will handle them
        IF v_has_backfill_history THEN
            RAISE DEBUG 'Skipping backfill event: % (DCA: %)', NEW.signature, NEW.dca_key;
            RETURN NEW;
        END IF;
    END IF;
    
    -- Process event (real-time or old real-time)
    v_should_notify := v_is_recent;  -- Only notify for recent events
    
    -- Call processing logic with notification flag
    PERFORM fn_jupiter_dca_process_event(NEW, v_should_notify);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Separate processing function with enhanced fill notifications and ETA calculations
CREATE OR REPLACE FUNCTION fn_jupiter_dca_process_event(
    p_event stream_jupiter_dca_ht,
    p_should_notify BOOLEAN DEFAULT true
) RETURNS VOID AS $$
DECLARE
    v_avg_price NUMERIC(36, 12);
    v_new_total_fills INTEGER;
    v_new_total_input_spent NUMERIC(36, 12);
    v_new_total_output_received NUMERIC(36, 12);
    v_new_total_input_spent_usd NUMERIC(36, 12);
    v_new_total_output_received_usd NUMERIC(36, 12);
    v_new_remaining_input_usd NUMERIC(36, 12);
    v_cycle_frequency INTEGER;
    v_order_completed BOOLEAN := false;
    v_completion_threshold NUMERIC(36, 12) := 0.01; -- $0.01 USD threshold for completion
    v_order_exists BOOLEAN;
    v_total_input_deposited NUMERIC(36, 12);
    v_total_input_deposited_usd NUMERIC(36, 12);
    v_progress_percentage NUMERIC(10, 2);
    v_expected_total_fills INTEGER;
    v_remaining_fills INTEGER;
    v_estimated_completion_at TIMESTAMPTZ;
BEGIN
    -- Early validation: skip events with invalid prices
    IF p_event.input_usd_price IS NULL 
       OR p_event.output_usd_price IS NULL 
       OR p_event.input_usd_price <= 0 
       OR p_event.output_usd_price <= 0 THEN
        RAISE NOTICE 'Skipping DCA event % (type: %) due to invalid prices: input_usd_price=%, output_usd_price=%', 
            p_event.dca_key, p_event.event_type, p_event.input_usd_price, p_event.output_usd_price;
        RETURN;
    END IF;
    
    CASE p_event.event_type
        WHEN 'opened' THEN
            -- Calculate initial estimated completion time
            v_expected_total_fills := GREATEST(1, ROUND(p_event.in_deposited / NULLIF(p_event.in_amount_per_cycle, 0)))::INTEGER;
            v_estimated_completion_at := p_event.created_at + (v_expected_total_fills * p_event.cycle_frequency || ' seconds')::interval;
            
            INSERT INTO stream_jupiter_dca_active_orders (
                dca_key, user_key, 
                input_mint, output_mint, input_decimals, output_decimals,
                in_deposited, in_amount_per_cycle, cycle_frequency,
                created_at, opened_at, next_expected_fill_at,
                expected_total_fills, remaining_input_usd,
                input_usd_price, output_usd_price,
                in_deposited_usd, order_status, estimated_completion_at
            ) VALUES (
                p_event.dca_key, p_event.user_key,
                p_event.input_mint, p_event.output_mint, p_event.input_decimals, p_event.output_decimals,
                p_event.in_deposited, p_event.in_amount_per_cycle, p_event.cycle_frequency,
                p_event.created_at, p_event.time, 
                p_event.created_at + (p_event.cycle_frequency || ' seconds')::interval,
                v_expected_total_fills,
                -- Calculate initial remaining input USD
                p_event.in_deposited * p_event.input_usd_price,
                p_event.input_usd_price, p_event.output_usd_price,
                p_event.in_deposited_usd, 'active', v_estimated_completion_at
            )
            ON CONFLICT (dca_key) DO NOTHING;
            
            -- Send notification only if requested
            IF p_should_notify THEN
                PERFORM pg_notify('jupiter_dca_order_opened', json_build_object(
                    'dca_key', p_event.dca_key,
                    'user_key', p_event.user_key,
                    'input_mint', p_event.input_mint,
                    'output_mint', p_event.output_mint,
                    'in_deposited_usd', p_event.in_deposited_usd,
                    'time', p_event.time,
                    'source', 'realtime'
                )::text);
            END IF;
            
        WHEN 'fill' THEN
            -- Check if active order exists first
            SELECT EXISTS(
                SELECT 1 FROM stream_jupiter_dca_active_orders
                WHERE dca_key = p_event.dca_key AND order_status = 'active'
            ) INTO v_order_exists;
            
            -- Skip FILL if no active order exists
            IF NOT v_order_exists THEN
                RAISE NOTICE 'Skipping FILL event % - no active order found for DCA % (possibly skipped OPENED due to invalid prices)', 
                    p_event.signature, p_event.dca_key;
                RETURN;
            END IF;
            
            -- Pre-calculate all new values for auto-completion check and notification
            SELECT 
                CASE 
                    WHEN total_fills = 0 THEN p_event.price_per_token
                    ELSE ((average_fill_price * total_fills) + p_event.price_per_token) / (total_fills + 1)
                END,
                total_fills + 1,
                total_input_spent + p_event.input_amount,
                total_output_received + p_event.output_amount,
                total_input_spent_usd + p_event.input_amount_usd,
                total_output_received_usd + p_event.output_amount_usd,
                -- Calculate new remaining input USD
                (in_deposited - (total_input_spent + p_event.input_amount)) * p_event.input_usd_price,
                cycle_frequency,
                in_deposited,        -- Total input deposited
                in_deposited_usd,    -- Total input deposited USD
                -- Calculate progress percentage
                ROUND(((total_input_spent + p_event.input_amount) / NULLIF(in_deposited, 0)) * 100, 2),
                expected_total_fills
            INTO v_avg_price, v_new_total_fills, v_new_total_input_spent, v_new_total_output_received,
                 v_new_total_input_spent_usd, v_new_total_output_received_usd, 
                 v_new_remaining_input_usd, v_cycle_frequency, v_total_input_deposited, 
                 v_total_input_deposited_usd, v_progress_percentage, v_expected_total_fills
            FROM stream_jupiter_dca_active_orders
            WHERE dca_key = p_event.dca_key AND order_status = 'active';
            
            -- Calculate remaining fills and estimated completion time
            v_remaining_fills := GREATEST(0, v_expected_total_fills - v_new_total_fills);
            IF v_remaining_fills > 0 THEN
                v_estimated_completion_at := p_event.time + (v_remaining_fills * v_cycle_frequency || ' seconds')::interval;
            ELSE
                v_estimated_completion_at := NULL; -- Order will be completed
            END IF;
            
            -- Check if order should be auto-completed
            v_order_completed := (v_new_remaining_input_usd <= v_completion_threshold);
            
            IF v_order_completed THEN
                -- Auto-complete the order
                UPDATE stream_jupiter_dca_active_orders
                SET 
                    -- Mark as completed
                    order_status = 'completed',
                    closed_at = p_event.time,
                    -- Update final progress values
                    last_fill_at = p_event.time,
                    total_fills = total_fills + 1,
                    total_input_spent = total_input_spent + p_event.input_amount,
                    total_output_received = total_output_received + p_event.output_amount,
                    last_fill_input_amount = p_event.input_amount,
                    last_fill_output_amount = p_event.output_amount,
                    -- Update final price information
                    input_usd_price = p_event.input_usd_price,
                    output_usd_price = p_event.output_usd_price,
                    last_fill_price_per_token = p_event.price_per_token,
                    average_fill_price = v_avg_price,
                    -- Update final USD totals
                    total_input_spent_usd = total_input_spent_usd + p_event.input_amount_usd,
                    total_output_received_usd = total_output_received_usd + p_event.output_amount_usd,
                    -- Set remaining to 0 since order is complete
                    remaining_input_usd = 0,
                    -- Clear next expected fill and ETA since order is done
                    next_expected_fill_at = NULL,
                    estimated_completion_at = NULL,
                    -- Reset overdue status
                    is_overdue = false,
                    minutes_overdue = 0,
                    updated_at = NOW()
                WHERE dca_key = p_event.dca_key AND order_status = 'active';
                
                -- Send completion notification only if requested
                IF p_should_notify THEN
                    PERFORM pg_notify('jupiter_dca_order_closed', json_build_object(
                        'dca_key', p_event.dca_key,
                        'user_closed', false, -- Auto-completion = natural completion
                        'order_status', 'completed',
                        'total_fills', v_new_total_fills,
                        'total_input_spent', v_new_total_input_spent,
                        'total_output_received', v_new_total_output_received,
                        'total_output_received_usd', v_new_total_output_received_usd,
                        'completion_method', 'auto_detected',
                        'time', p_event.time,
                        'source', 'realtime'
                    )::text);
                END IF;
                
                RAISE NOTICE 'Auto-completed DCA order %: remaining USD = %', 
                    p_event.dca_key, v_new_remaining_input_usd;
                
            ELSE
                -- Normal fill processing (order still active)
                UPDATE stream_jupiter_dca_active_orders
                SET 
                    last_fill_at = p_event.time,
                    next_expected_fill_at = p_event.time + (cycle_frequency || ' seconds')::interval,
                    total_fills = total_fills + 1,
                    total_input_spent = total_input_spent + p_event.input_amount,
                    total_output_received = total_output_received + p_event.output_amount,
                    last_fill_input_amount = p_event.input_amount,
                    last_fill_output_amount = p_event.output_amount,
                    input_usd_price = p_event.input_usd_price,
                    output_usd_price = p_event.output_usd_price,
                    last_fill_price_per_token = p_event.price_per_token,
                    average_fill_price = v_avg_price,
                    total_input_spent_usd = total_input_spent_usd + p_event.input_amount_usd,
                    total_output_received_usd = total_output_received_usd + p_event.output_amount_usd,
                    remaining_input_usd = v_new_remaining_input_usd,
                    estimated_completion_at = v_estimated_completion_at,
                    is_overdue = false,
                    minutes_overdue = 0,
                    updated_at = NOW()
                WHERE dca_key = p_event.dca_key AND order_status = 'active';
                
                -- Send enhanced fill notification with progress data and ETA
                IF p_should_notify THEN
                    PERFORM pg_notify('jupiter_dca_order_filled', json_build_object(
                        -- Identifier
                        'dca_key', p_event.dca_key,
                        
                        -- Progress Updates (using pre-calculated variables)
                        'total_fills', v_new_total_fills,
                        'total_input_spent', v_new_total_input_spent,
                        'total_input_deposited', v_total_input_deposited,
                        'total_output_received', v_new_total_output_received,
                        'progress_percentage', v_progress_percentage,
                        'remaining_fills', v_remaining_fills,                    -- NEW
                        
                        -- USD Updates (using pre-calculated variables)
                        'total_input_spent_usd', v_new_total_input_spent_usd,
                        'total_input_deposited_usd', v_total_input_deposited_usd,
                        'total_output_received_usd', v_new_total_output_received_usd,
                        'remaining_input_usd', v_new_remaining_input_usd,
                        
                        -- Price Updates
                        'input_usd_price', p_event.input_usd_price,
                        'average_fill_price', v_avg_price,
                        
                        -- Timing Updates
                        'last_fill_at', p_event.time,
                        'next_expected_fill_at', p_event.time + (v_cycle_frequency || ' seconds')::interval,
                        'estimated_completion_at', v_estimated_completion_at,    -- NEW
                        
                        -- Metadata
                        'time', p_event.time,
                        'source', 'realtime'
                    )::text);
                END IF;
            END IF;
            
        WHEN 'closed' THEN
            -- Check if order exists (might have been skipped due to invalid prices)
            SELECT EXISTS(
                SELECT 1 FROM stream_jupiter_dca_active_orders
                WHERE dca_key = p_event.dca_key
            ) INTO v_order_exists;
            
            -- Skip CLOSED if no order exists at all
            IF NOT v_order_exists THEN
                RAISE NOTICE 'Skipping CLOSED event % - no order found for DCA % (possibly skipped OPENED due to invalid prices)', 
                    p_event.signature, p_event.dca_key;
                RETURN;
            END IF;
            
            -- Handle explicit CLOSED events (may arrive after auto-completion)
            UPDATE stream_jupiter_dca_active_orders
            SET 
                order_status = CASE 
                    WHEN p_event.user_closed = true THEN 'canceled'
                    ELSE 'completed'
                END,
                closed_at = p_event.time,
                input_usd_price = COALESCE(p_event.input_usd_price, input_usd_price),
                output_usd_price = COALESCE(p_event.output_usd_price, output_usd_price),
                remaining_input_usd = 0,
                next_expected_fill_at = NULL,
                estimated_completion_at = NULL,  -- Clear ETA when closed
                updated_at = NOW()
            WHERE dca_key = p_event.dca_key 
            AND order_status IN ('active', 'completed'); -- Allow updating already completed orders
            
            -- Send notification only if requested and if row was actually updated
            IF p_should_notify AND FOUND THEN
                PERFORM pg_notify('jupiter_dca_order_closed', json_build_object(
                    'dca_key', p_event.dca_key,
                    'user_closed', p_event.user_closed,
                    'order_status', CASE WHEN p_event.user_closed = true THEN 'canceled' ELSE 'completed' END,
                    'total_out_withdrawn', p_event.total_out_withdrawn,
                    'total_out_withdrawn_usd', p_event.total_out_withdrawn_usd,
                    'completion_method', 'blockchain_event',
                    'time', p_event.time,
                    'source', 'realtime'
                )::text);
            END IF;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger
CREATE TRIGGER trg_stream_jupiter_dca_ht_01_maintain_active_orders
AFTER INSERT ON stream_jupiter_dca_ht
FOR EACH ROW EXECUTE FUNCTION fn_jupiter_dca_maintain_active_orders();
```

#### Step 3: Periodic Overdue Check Function

This function checks for overdue orders and removes those overdue by more than 5 minutes.

```sql
-- Function to check and handle overdue Jupiter DCA orders
CREATE OR REPLACE FUNCTION fn_jupiter_dca_check_overdue_orders()
RETURNS TABLE(expired_count INTEGER, marked_overdue_count INTEGER) AS $$
DECLARE
    v_expired_count INTEGER := 0;
    v_marked_count INTEGER := 0;
    v_expired_keys TEXT[];
BEGIN
    -- Single query that handles marking overdue, updating times, and expiring orders
    WITH overdue_analysis AS (
        SELECT
            dca_key,
            ROUND(EXTRACT(EPOCH FROM (NOW() - next_expected_fill_at)) / 60, 2) as minutes_overdue_calc,
            is_overdue as currently_overdue
        FROM stream_jupiter_dca_active_orders
        WHERE
            order_status = 'active'
            AND next_expected_fill_at IS NOT NULL
            AND next_expected_fill_at < NOW()
            AND remaining_input_usd > 0  -- Good filter: skip orders with no remaining input
    ),
    mark_overdue AS (
        -- Handle orders that should be marked overdue (but not expired)
        UPDATE stream_jupiter_dca_active_orders
        SET
            is_overdue = true,
            minutes_overdue = oa.minutes_overdue_calc,
            updated_at = NOW()
        FROM overdue_analysis oa
        WHERE
            stream_jupiter_dca_active_orders.dca_key = oa.dca_key
            AND oa.minutes_overdue_calc <= 5  -- Only mark as overdue, don't expire yet
            AND NOT oa.currently_overdue  -- Only if not already marked overdue
        RETURNING 1
    ),
    update_existing_overdue AS (
        -- Update minutes_overdue for existing overdue orders (that aren't being expired)
        UPDATE stream_jupiter_dca_active_orders
        SET
            minutes_overdue = oa.minutes_overdue_calc,
            updated_at = NOW()
        FROM overdue_analysis oa
        WHERE
            stream_jupiter_dca_active_orders.dca_key = oa.dca_key
            AND oa.minutes_overdue_calc <= 5  -- Don't update if it should be expired
            AND oa.currently_overdue  -- Only existing overdue orders
        RETURNING 1
    ),
    expire_orders AS (
        -- Mark orders as overdue_expired (just status change for UI)
        UPDATE stream_jupiter_dca_active_orders
        SET
            order_status = 'overdue_expired',
            minutes_overdue = oa.minutes_overdue_calc,
            updated_at = NOW()
        FROM overdue_analysis oa
        WHERE
            stream_jupiter_dca_active_orders.dca_key = oa.dca_key
            AND oa.minutes_overdue_calc > 5  -- Mark as expired after 5 minutes
        RETURNING stream_jupiter_dca_active_orders.dca_key
    ),
    collect_results AS (
        SELECT
            ARRAY_AGG(dca_key) FILTER (WHERE dca_key IS NOT NULL) as expired_keys
        FROM expire_orders
    )
    SELECT
        COALESCE(ARRAY_LENGTH(cr.expired_keys, 1), 0),
        (SELECT COUNT(*) FROM mark_overdue),  -- Only count newly marked orders
        cr.expired_keys
    INTO v_expired_count, v_marked_count, v_expired_keys
    FROM collect_results cr;
    
    -- Send notification with affected counts (limit payload size)
    IF v_expired_count > 0 OR v_marked_count > 0 THEN
        PERFORM pg_notify('jupiter_dca_orders_expired', json_build_object(
            'expired_dca_keys', CASE 
                WHEN ARRAY_LENGTH(v_expired_keys, 1) <= 50 
                THEN COALESCE(v_expired_keys, ARRAY[]::TEXT[])
                ELSE (v_expired_keys[1:50])  -- Send first 50 keys only
            END,
            'marked_overdue_count', v_marked_count,
            'expired_count', v_expired_count,
            'timestamp', NOW(),
            'action', 'overdue_check_completed',
            'total_expired_keys', COALESCE(ARRAY_LENGTH(v_expired_keys, 1), 0)
        )::text);
    END IF;
    
    RETURN QUERY SELECT v_expired_count, v_marked_count;
END;
$$ LANGUAGE plpgsql;

-- Add index to optimize the overdue check
CREATE INDEX IF NOT EXISTS idx_jupiter_dca_active_orders_overdue_check
ON stream_jupiter_dca_active_orders(next_expected_fill_at)
WHERE order_status = 'active' AND next_expected_fill_at IS NOT NULL;

-- Schedule this to run every minute using pg_cron
-- Uncomment if pg_cron is installed:
-- SELECT cron.schedule('check-jupiter-dca-overdue', '* * * * *', 'SELECT * FROM fn_jupiter_dca_check_overdue_orders();');
```

#### Step 4: Query Helper Functions

These functions provide convenient ways to query active orders with calculated metrics.

```sql
-- Function to get Jupiter DCA orders with detailed metrics
CREATE OR REPLACE FUNCTION fn_jupiter_dca_get_orders(
    p_user_key TEXT DEFAULT NULL,
    p_order_status TEXT DEFAULT 'active',
    p_include_overdue BOOLEAN DEFAULT true,
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE (
    dca_key TEXT,
    user_key TEXT,
    input_mint TEXT,
    output_mint TEXT,
    order_status TEXT,
    progress_percentage NUMERIC,
    fills_completed INTEGER,
    fills_total INTEGER,
    remaining_input NUMERIC,
    remaining_input_usd NUMERIC,
    estimated_remaining_fills INTEGER,
    average_fill_rate NUMERIC,
    current_pnl_usd NUMERIC,
    is_healthy BOOLEAN,
    order_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ao.dca_key,
        ao.user_key,
        ao.input_mint,
        ao.output_mint,
        ao.order_status,
        -- Calculate progress percentage
        ROUND((ao.total_input_spent / NULLIF(ao.in_deposited, 0)) * 100, 2) as progress_percentage,
        -- Fills completed and total
        ao.total_fills as fills_completed,
        ao.expected_total_fills as fills_total,
        -- Remaining input
        (ao.in_deposited - ao.total_input_spent) as remaining_input,
        -- Remaining input in USD (use stored value which is updated with current prices)
        COALESCE(ao.remaining_input_usd, 0) as remaining_input_usd,
        -- Estimate remaining fills
        GREATEST(0, ao.expected_total_fills - ao.total_fills) as estimated_remaining_fills,
        -- Average output per input
        CASE 
            WHEN ao.total_input_spent > 0 
            THEN ROUND(ao.total_output_received / ao.total_input_spent, 6)
            ELSE 0
        END as average_fill_rate,
        -- Current PnL in USD
        (ao.total_output_received_usd - ao.total_input_spent_usd) as current_pnl_usd,
        -- Health check (active and not overdue)
        (ao.order_status = 'active' AND NOT ao.is_overdue) as is_healthy,
        -- Full order data as JSON
        jsonb_build_object(
            'created_at', ao.created_at,
            'opened_at', ao.opened_at,
            'last_fill_at', ao.last_fill_at,
            'next_expected_fill_at', ao.next_expected_fill_at,
            'closed_at', ao.closed_at,
            'order_status', ao.order_status,
            'total_fills', ao.total_fills,
            'expected_total_fills', ao.expected_total_fills,
            'total_input_spent', ao.total_input_spent,
            'total_output_received', ao.total_output_received,
            'total_input_spent_usd', ao.total_input_spent_usd,
            'total_output_received_usd', ao.total_output_received_usd,
            'remaining_input_usd', ao.remaining_input_usd,
            'in_deposited_usd', ao.in_deposited_usd,
            'cycle_frequency', ao.cycle_frequency,
            'in_amount_per_cycle', ao.in_amount_per_cycle,
            'input_usd_price', ao.input_usd_price,
            'output_usd_price', ao.output_usd_price,
            'average_fill_price', ao.average_fill_price,
            'last_fill_price_per_token', ao.last_fill_price_per_token,
            'is_overdue', ao.is_overdue,
            'minutes_overdue', ao.minutes_overdue
        ) as order_data
    FROM stream_jupiter_dca_active_orders ao
    WHERE 
        (p_user_key IS NULL OR ao.user_key = p_user_key)
        AND (p_order_status IS NULL OR ao.order_status = p_order_status)
        AND (p_order_status != 'active' OR p_include_overdue OR NOT ao.is_overdue)
    ORDER BY 
        CASE ao.order_status 
            WHEN 'active' THEN 1 
            WHEN 'completed' THEN 2 
            WHEN 'canceled' THEN 3 
            WHEN 'overdue_expired' THEN 4 
        END,
        ao.is_overdue DESC,  -- Overdue orders first within active
        COALESCE(ao.next_expected_fill_at, ao.closed_at) ASC  -- Then by next fill time or close time
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to get aggregated statistics
CREATE OR REPLACE FUNCTION fn_jupiter_dca_get_stats()
RETURNS TABLE (
    total_active_orders INTEGER,
    total_completed_orders INTEGER,
    total_canceled_orders INTEGER,
    total_overdue_expired_orders INTEGER,
    unique_users INTEGER,
    total_value_locked_usd NUMERIC,
    total_volume_usd NUMERIC,
    total_remaining_usd NUMERIC,
    overdue_orders INTEGER,
    healthy_orders INTEGER,
    average_order_size_usd NUMERIC,
    average_fills_per_order NUMERIC,
    most_popular_input_token TEXT,
    most_popular_output_token TEXT
) AS $$
BEGIN
    RETURN QUERY
    WITH token_stats AS (
        SELECT 
            input_mint,
            output_mint,
            COUNT(*) as order_count
        FROM stream_jupiter_dca_active_orders
        WHERE order_status = 'active'
        GROUP BY input_mint, output_mint
    )
    SELECT 
        COUNT(*) FILTER (WHERE order_status = 'active')::INTEGER as total_active_orders,
        COUNT(*) FILTER (WHERE order_status = 'completed')::INTEGER as total_completed_orders,
        COUNT(*) FILTER (WHERE order_status = 'canceled')::INTEGER as total_canceled_orders,
        COUNT(*) FILTER (WHERE order_status = 'overdue_expired')::INTEGER as total_overdue_expired_orders,
        COUNT(DISTINCT user_key)::INTEGER as unique_users,
        SUM(in_deposited_usd) FILTER (WHERE order_status = 'active') as total_value_locked_usd,
        SUM(total_input_spent_usd) as total_volume_usd,
        SUM(remaining_input_usd) FILTER (WHERE order_status = 'active') as total_remaining_usd,
        COUNT(*) FILTER (WHERE order_status = 'active' AND is_overdue)::INTEGER as overdue_orders,
        COUNT(*) FILTER (WHERE order_status = 'active' AND NOT is_overdue)::INTEGER as healthy_orders,
        AVG(in_deposited_usd) FILTER (WHERE order_status = 'active') as average_order_size_usd,
        AVG(total_fills::NUMERIC) as average_fills_per_order,
        (SELECT input_mint FROM token_stats ORDER BY order_count DESC LIMIT 1) as most_popular_input_token,
        (SELECT output_mint FROM token_stats ORDER BY order_count DESC LIMIT 1) as most_popular_output_token
    FROM stream_jupiter_dca_active_orders;
END;
$$ LANGUAGE plpgsql;
```

#### Step 5: Initial Population Script

Run this once to populate the active orders table from existing data.

```sql
-- One-time script to populate active orders from existing data
INSERT INTO stream_jupiter_dca_active_orders
WITH order_lifecycle AS (
    -- Get complete order history
    SELECT 
        dca_key,
        -- Get OPENED event data
        MAX(CASE WHEN event_type = 'opened' THEN time END) as opened_time,
        MAX(CASE WHEN event_type = 'opened' THEN user_key END) as user_key,
        MAX(CASE WHEN event_type = 'opened' THEN input_mint END) as input_mint,
        MAX(CASE WHEN event_type = 'opened' THEN output_mint END) as output_mint,
        MAX(CASE WHEN event_type = 'opened' THEN input_decimals END) as input_decimals,
        MAX(CASE WHEN event_type = 'opened' THEN output_decimals END) as output_decimals,
        MAX(CASE WHEN event_type = 'opened' THEN in_deposited END) as in_deposited,
        MAX(CASE WHEN event_type = 'opened' THEN in_amount_per_cycle END) as in_amount_per_cycle,
        MAX(CASE WHEN event_type = 'opened' THEN cycle_frequency END) as cycle_frequency,
        MAX(CASE WHEN event_type = 'opened' THEN created_at END) as created_at,
        MAX(CASE WHEN event_type = 'opened' THEN in_deposited_usd END) as in_deposited_usd,
        -- Get latest prices
        MAX(input_usd_price) as input_usd_price,
        MAX(output_usd_price) as output_usd_price,
        -- Get FILL event data
        MAX(CASE WHEN event_type = 'fill' THEN time END) as last_fill_time,
        COUNT(CASE WHEN event_type = 'fill' THEN 1 END) as fill_count,
        SUM(CASE WHEN event_type = 'fill' THEN input_amount ELSE 0 END) as total_input,
        SUM(CASE WHEN event_type = 'fill' THEN output_amount ELSE 0 END) as total_output,
        SUM(CASE WHEN event_type = 'fill' THEN input_amount_usd ELSE 0 END) as total_input_usd,
        SUM(CASE WHEN event_type = 'fill' THEN output_amount_usd ELSE 0 END) as total_output_usd,
        AVG(CASE WHEN event_type = 'fill' THEN price_per_token END) as avg_price,
        -- Check if closed
        BOOL_OR(event_type = 'closed') as is_closed
    FROM stream_jupiter_dca_ht
    WHERE time > NOW() - INTERVAL '30 days'  -- Only look at recent orders
    GROUP BY dca_key
),
filtered_active AS (
    SELECT * FROM order_lifecycle
    WHERE 
        NOT is_closed
        AND dca_key IS NOT NULL
        AND user_key IS NOT NULL
        -- Filter spam tokens
        AND NOT EXISTS (
            SELECT 1 FROM list_tokens_spam 
            WHERE mint_address IN (order_lifecycle.input_mint, order_lifecycle.output_mint)
        )
)
SELECT 
    dca_key,
    user_key,
    input_mint,
    output_mint,
    input_decimals,
    output_decimals,
    in_deposited,
    in_amount_per_cycle,
    cycle_frequency,
    created_at,
    opened_time,
    last_fill_time,
    COALESCE(last_fill_time, created_at) + (cycle_frequency || ' seconds')::interval as next_expected_fill_at,
    NULL as closed_at,
    fill_count as total_fills,
    -- Calculate expected total fills
    FLOOR(in_deposited / NULLIF(in_amount_per_cycle, 0))::INTEGER as expected_total_fills,
    total_input as total_input_spent,
    total_output as total_output_received,
    NULL as last_fill_input_amount,
    NULL as last_fill_output_amount,
    input_usd_price,
    output_usd_price,
    NULL as last_fill_price_per_token,
    avg_price as average_fill_price,
    in_deposited_usd,
    total_input_usd as total_input_spent_usd,
    total_output_usd as total_output_received_usd,
    -- Calculate remaining input USD
    (in_deposited - total_input) * input_usd_price as remaining_input_usd,
    'active' as order_status,
    false as is_overdue,
    0 as minutes_overdue,
    NOW() as updated_at
FROM filtered_active;

-- Run the overdue check immediately after population
SELECT * FROM fn_jupiter_dca_check_overdue_orders();
```

#### Step 6: Usage Examples

```sql
-- Get all active orders
SELECT * FROM fn_jupiter_dca_get_orders();

-- Get active orders for a specific user
SELECT * FROM fn_jupiter_dca_get_orders('USER_PUBLIC_KEY_HERE');

-- Get only healthy (non-overdue) active orders
SELECT * FROM fn_jupiter_dca_get_orders(NULL, 'active', false);

-- Get completed orders for a user
SELECT * FROM fn_jupiter_dca_get_orders('USER_PUBLIC_KEY_HERE', 'completed');

-- Get canceled orders
SELECT * FROM fn_jupiter_dca_get_orders(NULL, 'canceled');

-- Get all orders (active, completed, canceled) for a user
SELECT * FROM fn_jupiter_dca_get_orders('USER_PUBLIC_KEY_HERE', NULL);

-- Get overall statistics
SELECT * FROM fn_jupiter_dca_get_stats();

-- Direct query for specific token pairs (active orders only)
SELECT 
    dca_key,
    user_key,
    order_status,
    progress_percentage,
    fills_completed,
    fills_total,
    remaining_input_usd,
    total_output_received_usd - total_input_spent_usd as pnl_usd,
    next_expected_fill_at
FROM (
    SELECT 
        *,
        ROUND((total_input_spent / NULLIF(in_deposited, 0)) * 100, 2) as progress_percentage,
        total_fills as fills_completed,
        expected_total_fills as fills_total
    FROM stream_jupiter_dca_active_orders
    WHERE 
        order_status = 'active'
        AND input_mint = 'So11111111111111111111111111111111111111112'  -- SOL
        AND output_mint = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'  -- USDC
) t
ORDER BY next_expected_fill_at;

-- Monitor overdue orders
SELECT 
    dca_key,
    user_key,
    order_status,
    minutes_overdue,
    total_fills,
    expected_total_fills,
    in_deposited - total_input_spent as remaining_input,
    remaining_input_usd,
    next_expected_fill_at
FROM stream_jupiter_dca_active_orders
WHERE order_status = 'active' AND is_overdue = true
ORDER BY minutes_overdue DESC;

-- Performance analysis: completed vs canceled orders
SELECT 
    order_status,
    COUNT(*) as order_count,
    AVG(total_fills) as avg_fills_completed,
    AVG(expected_total_fills) as avg_fills_total,
    AVG(total_fills::NUMERIC / expected_total_fills * 100) as avg_completion_percentage,
    AVG(total_output_received_usd - total_input_spent_usd) as avg_pnl_usd,
    SUM(total_input_spent_usd) as total_volume_usd
FROM stream_jupiter_dca_active_orders
WHERE order_status IN ('completed', 'canceled')
GROUP BY order_status;
```

#### Step 7: Real-time Listener Example (Node.js)

```javascript
const { Client } = require('pg');

const client = new Client({
    host: process.env.DB_HOST,
    port: process.env.DB_PORT,
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
});

async function setupRealtimeListener() {
    await client.connect();
    
    // Listen for all Jupiter DCA events
    await client.query('LISTEN jupiter_dca_order_opened');
    await client.query('LISTEN jupiter_dca_order_filled');
    await client.query('LISTEN jupiter_dca_order_closed');
    await client.query('LISTEN jupiter_dca_orders_expired');
    
    client.on('notification', (msg) => {
        const event = JSON.parse(msg.payload);
        console.log(`Event ${msg.channel}:`, event);
        
        // Handle different event types
        switch(msg.channel) {
            case 'jupiter_dca_order_opened':
                console.log(`New DCA opened: ${event.dca_key}, Value: $${event.in_deposited_usd}`);
                // Update UI with new order
                break;
                
            case 'jupiter_dca_order_filled':
                console.log(`DCA filled: ${event.dca_key}, Amount: $${event.output_amount_usd}`);
                // Update progress: fills_completed/fills_total, remaining_input_usd
                break;
                
            case 'jupiter_dca_order_closed':
                console.log(`DCA ${event.order_status}: ${event.dca_key}`);
                // Move order to completed/canceled section instead of removing
                break;
                
            case 'jupiter_dca_orders_expired':
                console.log(`${event.count} orders marked as overdue_expired`);
                // Move expired orders to separate section
                break;
        }
    });
}

setupRealtimeListener().catch(console.error);
```

### State Management Strategy

#### Why Not a Hypertable?

The `stream_jupiter_dca_active_orders` table is **NOT** a hypertable because:

1. **Requires Updates**: Hypertables are optimized for append-only operations
2. **Status-Based Management**: Orders transition through states rather than being deleted
3. **Mixed Dataset**: Contains active, completed, canceled, and expired orders
4. **Point Lookups**: Optimized for key-based queries and status filtering, not time-range queries

#### State Transitions

```
Order States:
┌─────────┐
│ ACTIVE  │ ← OPENED event creates record
└────┬────┘
     │
     │ FILL events update progress:
     │ • total_fills++
     │ • remaining_input_usd updated
     │ • expected_total_fills calculated
     │
┌────▼────┐     ┌───────────┐     ┌─────────────────┐
│COMPLETED│     │ CANCELED  │     │OVERDUE_EXPIRED  │
└─────────┘     └───────────┘     └─────────────────┘
     ▲               ▲                      ▲
     │               │                      │
CLOSED event    CLOSED event         5+ min overdue
(user_closed=F) (user_closed=T)      (automatic)
```

### Error Handling and Edge Cases

#### Common Edge Cases

1. **Duplicate Events**
   * **Issue**: Kafka may deliver duplicate messages
   * **Solution**: `ON CONFLICT DO NOTHING` in OPENED handler
   * **Solution**: Primary key constraint prevents duplicates
2. **Out-of-Order Events**
   * **Issue**: FILL arrives before OPENED
   * **Solution**: UPDATE only affects existing records (no-op if missing)
3. **Missing Events**
   * **Issue**: Network issues may cause dropped events
   * **Solution**: Periodic reconciliation with main hypertable
4. **Spam Token Detection**
   * **Issue**: Orders with spam tokens should be filtered
   * **Solution**: Check `list_tokens_spam` before processing
5. **Clock Drift**
   * **Issue**: Server time vs blockchain time mismatches
   * **Solution**: Use blockchain timestamps for calculations

#### Error Recovery Procedures

```sql
-- Reconciliation function for missing/inconsistent data
CREATE OR REPLACE FUNCTION fn_jupiter_dca_reconcile_active_orders()
RETURNS TABLE(
    added INTEGER,
    updated INTEGER,
    removed INTEGER
) AS $
DECLARE
    v_added INTEGER := 0;
    v_updated INTEGER := 0;
    v_removed INTEGER := 0;
BEGIN
    -- Add missing active orders
    WITH missing_orders AS (
        INSERT INTO stream_jupiter_dca_active_orders
        SELECT DISTINCT ON (dca_key)
            dca_key, user_key, input_mint, output_mint,
            -- ... (full reconciliation logic)
        FROM stream_jupiter_dca_ht
        WHERE event_type = 'opened'
            AND dca_key NOT IN (SELECT dca_key FROM stream_jupiter_dca_active_orders)
            AND dca_key NOT IN (SELECT dca_key FROM stream_jupiter_dca_ht WHERE event_type = 'closed')
        ON CONFLICT (dca_key) DO NOTHING
        RETURNING 1
    )
    SELECT COUNT(*) INTO v_added FROM missing_orders;
    
    -- Remove orders that were closed but missed
    WITH closed_orders AS (
        DELETE FROM stream_jupiter_dca_active_orders
        WHERE dca_key IN (
            SELECT DISTINCT dca_key 
            FROM stream_jupiter_dca_ht 
            WHERE event_type = 'closed'
        )
        RETURNING 1
    )
    SELECT COUNT(*) INTO v_removed FROM closed_orders;
    
    RETURN QUERY SELECT v_added, v_updated, v_removed;
END;
$ LANGUAGE plpgsql;
```

### Performance Considerations

#### Index Strategy

```sql
-- Primary lookup patterns and their indexes:

-- 1. By DCA key (PRIMARY KEY provides this)
-- Used by: Event handlers, direct lookups

-- 2. By user (idx_jupiter_dca_active_orders_user)
-- Used by: User dashboard queries
-- Query pattern: WHERE user_key = ?

-- 3. By next fill time (idx_jupiter_dca_active_orders_next_fill)
-- Used by: Overdue checking, scheduling
-- Query pattern: WHERE next_expected_fill_at < NOW()

-- 4. By overdue status (idx_jupiter_dca_active_orders_overdue)
-- Used by: Monitoring, cleanup
-- Query pattern: WHERE is_overdue = true

-- 5. By token pair (idx_jupiter_dca_active_orders_tokens)
-- Used by: Market analysis, token metrics
-- Query pattern: WHERE input_mint = ? AND output_mint = ?
```

#### Query Performance Tips

1. **Use Function-Based Queries**: The provided functions use optimized query plans
2. **Avoid COUNT(\*) on Large Results**: Use estimates or pre-aggregated stats
3. **Partition Notifications**: Consider separate channels for high-volume events
4. **Batch Updates**: For bulk operations, use single transactions

### Monitoring and Observability

#### Key Metrics to Track

```sql
-- Real-time metrics view
CREATE OR REPLACE VIEW view_jupiter_dca_metrics AS
SELECT 
    -- Order status breakdown
    COUNT(*) FILTER (WHERE order_status = 'active') as total_active_orders,
    COUNT(*) FILTER (WHERE order_status = 'completed') as total_completed_orders,
    COUNT(*) FILTER (WHERE order_status = 'canceled') as total_canceled_orders,
    COUNT(*) FILTER (WHERE order_status = 'overdue_expired') as total_expired_orders,
    COUNT(DISTINCT user_key) as unique_users,
    
    -- Active order health
    COUNT(*) FILTER (WHERE order_status = 'active' AND is_overdue) as overdue_orders,
    AVG(minutes_overdue) FILTER (WHERE order_status = 'active' AND is_overdue) as avg_overdue_minutes,
    
    -- Financial metrics
    SUM(in_deposited_usd) FILTER (WHERE order_status = 'active') as total_value_locked_usd,
    SUM(remaining_input_usd) FILTER (WHERE order_status = 'active') as total_remaining_usd,
    SUM(total_input_spent_usd) as total_volume_processed_usd,
    SUM(total_output_received_usd - total_input_spent_usd) as total_pnl_usd,
    
    -- Progress metrics
    AVG(total_fills::NUMERIC / NULLIF(expected_total_fills, 0) * 100) FILTER (WHERE order_status = 'active') as avg_progress_percentage,
    PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY total_fills) as median_fills_per_order,
    AVG(expected_total_fills::NUMERIC) FILTER (WHERE order_status = 'active') as avg_expected_fills,
    
    -- Health indicators
    COUNT(*) FILTER (WHERE order_status = 'active' AND last_fill_at > NOW() - INTERVAL '1 hour') as fills_last_hour,
    COUNT(*) FILTER (WHERE order_status = 'active' AND created_at > NOW() - INTERVAL '1 hour') as new_orders_last_hour,
    
    -- Timestamp
    NOW() as calculated_at
FROM stream_jupiter_dca_active_orders;
```

#### Alerting Queries

```sql
-- Orders stuck for too long
SELECT 
    dca_key,
    user_key,
    minutes_overdue,
    in_deposited_usd,
    'Order overdue for ' || ROUND(minutes_overdue) || ' minutes' as alert_message
FROM stream_jupiter_dca_active_orders
WHERE is_overdue = true AND minutes_overdue > 10
ORDER BY minutes_overdue DESC;

-- Large orders at risk
SELECT 
    dca_key,
    user_key,
    in_deposited_usd,
    total_input_spent_usd,
    in_deposited_usd - total_input_spent_usd as remaining_usd
FROM stream_jupiter_dca_active_orders
WHERE 
    in_deposited_usd > 10000  -- Large orders
    AND is_overdue = true
ORDER BY remaining_usd DESC;
```

#### System Health Dashboard

```sql
-- Complete health check query
WITH system_stats AS (
    SELECT 
        -- Table stats
        pg_size_pretty(pg_total_relation_size('stream_jupiter_dca_active_orders')) as table_size,
        pg_size_pretty(pg_total_relation_size('idx_jupiter_dca_active_orders_user')) as user_index_size,
        
        -- Trigger stats
        (SELECT SUM(calls) FROM pg_stat_user_functions WHERE funcname = 'fn_jupiter_dca_event_handler_listener') as trigger_calls,
        (SELECT AVG(mean_exec_time) FROM pg_stat_user_functions WHERE funcname = 'fn_jupiter_dca_event_handler_listener') as avg_trigger_time_ms,
        
        -- Order stats
        (SELECT COUNT(*) FROM stream_jupiter_dca_active_orders) as active_orders,
        (SELECT COUNT(*) FROM stream_jupiter_dca_active_orders WHERE is_overdue) as overdue_orders,
        
        -- Recent activity
        (SELECT COUNT(*) FROM stream_jupiter_dca_ht WHERE time > NOW() - INTERVAL '1 hour') as events_last_hour
)
SELECT * FROM system_stats;
```

### Troubleshooting Guide

#### Common Issues and Solutions

1.  **Orders Not Appearing in Active Table**

    ```sql
    -- Check if order exists in main table
    SELECT * FROM stream_jupiter_dca_ht 
    WHERE dca_key = 'PROBLEMATIC_DCA_KEY' 
    ORDER BY time DESC;

    -- Check if it's a spam token
    SELECT * FROM list_tokens_spam 
    WHERE mint_address IN (
        SELECT DISTINCT input_mint FROM stream_jupiter_dca_ht WHERE dca_key = 'PROBLEMATIC_DCA_KEY'
        UNION
        SELECT DISTINCT output_mint FROM stream_jupiter_dca_ht WHERE dca_key = 'PROBLEMATIC_DCA_KEY'
    );
    ```
2.  **Orders Not Being Removed When Overdue**

    ```sql
    -- Manually run overdue check
    SELECT * FROM fn_jupiter_dca_check_overdue_orders();

    -- Check specific order status
    SELECT 
        dca_key,
        next_expected_fill_at,
        NOW() - next_expected_fill_at as overdue_duration,
        is_overdue,
        minutes_overdue
    FROM stream_jupiter_dca_active_orders
    WHERE dca_key = 'PROBLEMATIC_DCA_KEY';
    ```
3.  **Performance Degradation**

    ```sql
    -- Check for missing indexes
    SELECT schemaname, tablename, indexname, idx_scan
    FROM pg_stat_user_indexes
    WHERE tablename = 'stream_jupiter_dca_active_orders'
    ORDER BY idx_scan;

    -- Analyze table for query planner
    ANALYZE stream_jupiter_dca_active_orders;
    ```

### Maintenance Operations

#### Daily Tasks

* Monitor the `stream_jupiter_dca_active_orders` table size
* Check for any stuck orders (overdue but not removed)
* Verify trigger performance
* Review alert queries for any issues

#### Weekly Tasks

* Run reconciliation function: `SELECT * FROM fn_jupiter_dca_reconcile_active_orders();`
* Analyze average fill rates and order health
* Review spam token list updates
* Check index usage and performance
* Archive any debugging logs

#### Monthly Tasks

* Review and optimize slow queries
* Update statistics: `ANALYZE stream_jupiter_dca_active_orders;`
* Evaluate index effectiveness
* Review error patterns and update handling logic

### Notes and Best Practices

* **Not a Hypertable**: The active orders table is NOT a hypertable because it requires updates and deletes
* **High Precision**: All monetary values use NUMERIC(36,12) to prevent rounding errors
* **Real-time Prices**: USD prices are updated with each fill event to maintain current valuations
* **Spam Filtering**: System automatically excludes orders with spam tokens via `list_tokens_spam` lookup
* **Automatic Cleanup**: Orders overdue by more than 5 minutes are automatically removed
* **Event Streaming**: PostgreSQL notifications enable real-time updates to connected applications
* **Idempotent Operations**: Event handlers are designed to be safely re-run
* **Time Source**: Always use blockchain time (from events) rather than database server time
* **Reconciliation**: Periodic reconciliation ensures consistency with the main event store
