---
description: >-
  This document outlines strict naming conventions for PostgreSQL and
  TimescaleDB development.
---

# PostgreSQL & TimescaleDB Naming Conventions

### Table Naming Conventions (STRICT)

#### Prefixing System

* **Time-series events/streams**: `stream_[platform]_[entity]` (e.g., `stream_jupiter_dca`)
* **Reference data**: `ref_[entity]_[purpose]` (e.g., `ref_tokens_metadata`)
* **Aggregated data**: `agg_[platform]_[entity]_[timeframe]` (e.g., `agg_jupiter_volume_hourly`)
* **Classifications/lists**: `list_[entity]_[type]` (e.g., `list_wallets_cex`)

#### Hypertable Designation

* Hypertables MUST use a `_ht` suffix (e.g., `stream_jupiter_dca_ht`)
* Time-series data tables MUST be hypertables
* Metadata/classification tables that aren't time-series should NOT be hypertables

#### Table Naming Format

* All table names MUST use lowercase snake\_case
* Table names MUST be plural (e.g., `wallets_fresh` not `wallet_fresh`)
* Names MUST be descriptive and indicate purpose

#### Column Naming

* Primary keys: `id` for auto-incrementing keys
* Timestamps: Use `time` for hypertable partitioning field
* Foreign keys: `[table_singular]_id` (e.g., `token_id`)
* Boolean flags: Use `is_[state]` format (e.g., `is_active`)

#### Index Naming

* Format: `idx_[tablename]_[columnname(s)]` (e.g., `idx_stream_jupiter_dca_ht_time`)
* Compound indexes: List columns in order (e.g., `idx_stream_jupiter_dca_ht_user_time`)

### Function & Procedure Naming (STRICT)

* All functions MUST use the prefix `fn_` (e.g., `fn_jupiter_calculate_volume`)
* Trigger listener functions MUST end with `_listener` (e.g., `fn_jupiter_update_balance_listener`)
* Function names should include platform/context: `fn_[platform]_[action]_[entity]`
* Function names MUST use lowercase snake\_case
* Function parameters MUST be prefixed with `p_` (e.g., `p_user_id`)

### Trigger Naming (STRICT)

* All triggers MUST use the prefix trg_.
* Format: trg_[tablename]_[##]_[purpose]
  * [tablename]: The full name of the table the trigger acts upon. This groups all related triggers together alphabetically.
  * [##]: An optional two-digit number (e.g., 01, 10, 99) used to define the execution order. PostgreSQL executes triggers for the same event in alphabetical order of the trigger name. This prefix makes the order explicit.
  * [purpose]: A brief, snake_case description of the trigger's job (e.g., handle_events, notify_live_feed, update_modified_at).

**Example:** Handling Multiple Triggers

When a single event (e.g., AFTER INSERT ON stream_jupiter_dca_ht) needs to fire multiple triggers, use the numeric prefix to ensure a predictable execution order.

```sql
-- 01: This trigger handles the core business logic of updating an active orders table.
CREATE TRIGGER trg_stream_jupiter_dca_ht_01_handle_events
AFTER INSERT ON stream_jupiter_dca_ht
FOR EACH ROW EXECUTE FUNCTION fn_jupiter_dca_event_handler_listener();

-- 02: This trigger sends a simple notification for a live feed.
CREATE TRIGGER trg_stream_jupiter_dca_ht_02_notify_live_feed
AFTER INSERT ON stream_jupiter_dca_ht
FOR EACH ROW EXECUTE FUNCTION fn_jupiter_dca_event_listener();
```

### View Naming (STRICT)

* Regular views: `view_[platform]_[entity]_[purpose]` (e.g., `view_jupiter_orders_active`)
* Materialized views: `matview_[platform]_[entity]_[timeframe]` (e.g., `matview_jupiter_volume_daily`)

### Hypertable Setup Guidelines

```sql
-- Basic hypertable setup pattern
CREATE TABLE stream_example (
    id SERIAL NOT NULL,
    time TIMESTAMPTZ NOT NULL,  -- Must use TIMESTAMPTZ named 'time'
    value NUMERIC,
    metadata JSONB
);

-- Convert to hypertable
SELECT create_hypertable('stream_example', 'time');

-- Rename to add the _ht suffix
ALTER TABLE stream_example RENAME TO stream_example_ht;

-- Create time-based index
CREATE INDEX idx_stream_example_ht_time ON stream_example_ht(time DESC);

-- Optional: Configure compression for historical data
ALTER TABLE stream_example_ht SET (
    timescaledb.compress,
    timescaledb.compress_segmentby = 'entity_id',
    timescaledb.compress_orderby = 'time DESC'
);

SELECT add_compression_policy('stream_example_ht', INTERVAL '7 days');
```

### Trigger and Event Function Pattern

```sql
-- Trigger listener function pattern
CREATE OR REPLACE FUNCTION fn_example_table_update_last_modified_listener()
RETURNS TRIGGER AS $
BEGIN
    NEW.last_modified = NOW();
    RETURN NEW;
EXCEPTION
    WHEN others THEN
        RAISE WARNING 'Error in listener function: %', SQLERRM;
        RETURN NEW;
END;
$ LANGUAGE plpgsql;

-- Create the trigger using the new naming convention.
-- The '99' prefix suggests it's a utility trigger that should run last.
CREATE TRIGGER trg_example_table_99_update_last_modified
BEFORE UPDATE ON example_table
FOR EACH ROW EXECUTE FUNCTION fn_example_table_update_last_modified_listener();
```

### Data Type Guidelines

* Use `TIMESTAMPTZ` for all time values, store in UTC
* Use `NUMERIC(precision, scale)` for financial calculations
* Use `TEXT` for variable-length strings
* Use `BOOLEAN` for flags, not integers
* Use `JSONB` rather than `JSON` for better performance

### Documentation Guidelines

Include a comment block for complex functions:

```sql
/*
 * fn_calculate_average_price
 *
 * Calculates the average price for a token over a specified time period
 *
 * Parameters:
 *   p_token_id - The token identifier
 *   p_start_time - Start time
 *   p_end_time - End time
 *
 * Returns: NUMERIC - The average price
 */
```
