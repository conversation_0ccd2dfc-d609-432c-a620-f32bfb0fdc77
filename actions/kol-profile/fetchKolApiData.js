"use server";

import axios from 'axios';
import redis from '@/utils/redis.server';
import { query as pgQuery } from '@/utils/postgres'; // Added PostgreSQL query utility
const logger = console; // Or your preferred logger

// Constants for configuration
const REQUEST_TIMEOUT = 15000; // 15 seconds
const MAX_CONTENT_LENGTH = 10 * 1024 * 1024; // 10MB
const CACHE_DURATION = 86400; // 24 hours
const REDIS_KEY_PREFIX = 'kol:label:';

/**
 * Get a KOL profile by wallet address from PostgreSQL.
 * @param {string} walletAddress - The case-sensitive wallet address of the KOL
 * @returns {Promise<Object|null>} - KOL profile object or null if not found
 */
async function getKolByAddressFromPostgres(walletAddress) {
    try {
        const tableName = 'ref_kols_profiles';
        // Use the original case-sensitive walletAddress in the query
        const sql = `SELECT * FROM ${tableName} WHERE wallet = $1`;
        const result = await pgQuery(sql, [walletAddress]);

        if (result.rows.length === 0) {
            return null;
        }
        // Transform PG data to the desired output structure
        const pgData = result.rows[0];
        return {
            wallet: pgData.wallet, // Wallet address from DB
            label: pgData.label || "",
            avatar: pgData.avatar || null,
            // PostgreSQL might use is_public, ensure it's mapped to isPublic
            isPublic: typeof pgData.is_public === 'boolean' ? pgData.is_public : (typeof pgData.isPublic === 'boolean' ? pgData.isPublic : false),
            socials: pgData.socials || null,
            source: 'postgres' // Add source for debugging/logging if needed
        };
    } catch (error) {
        logger.error(`Error fetching KOL profile for ${walletAddress} from PostgreSQL:`, error.message);
        // Do not throw here, let the caller decide how to handle PG error in context of API fallback
        return null;
    }
}

/**
 * Fetches all KOL profiles with full data from Redis cache
 * If cache is empty, syncs from PostgreSQL first
 * Also triggers background sync every hour to keep data fresh
 * 
 * @param {Object} options - Query options (currently only used for response structure)
 * @returns {Promise<Object>} Result object with full KOL profile data
 */
export async function getAllKolProfiles(options = {}) {
  try {
    // Import cache utilities
    const { getAllKolProfilesWithData, syncKolProfiles } = await import('@/utils/cache/kolProfiles');

    // Try to get KOL profiles with full data from cache first
    let cacheResult = await getAllKolProfilesWithData();
    
    // If cache is empty or failed, sync from PostgreSQL (blocking)
    if (!cacheResult.success || cacheResult.count === 0) {
      console.log('KOL profiles cache is empty, syncing from PostgreSQL...');
      
      const syncResult = await syncKolProfiles();
      if (!syncResult.success) {
        console.error('Failed to sync KOL profiles:', syncResult.error);
        return {
          status: 'error',
          message: 'Failed to sync KOL profiles from database',
          data: [],
          count: 0,
          error: syncResult.error
        };
      }
      
      console.log(`Successfully synced ${syncResult.profilesAdded} KOL profiles to cache`);
      
      // Try to get from cache again after sync
      cacheResult = await getAllKolProfilesWithData();
    } else {
      // Cache has data, trigger background sync if cooldown has passed
      triggerBackgroundSync();
    }

    if (!cacheResult.success) {
      return {
        status: 'error',
        message: 'Failed to fetch KOL profiles from cache',
        data: [],
        count: 0,
        error: cacheResult.error
      };
    }

    return {
      status: 'success',
      data: cacheResult.profiles,
      count: cacheResult.count,
      fromCache: true,
      cacheTtl: cacheResult.ttl,
      pagination: {
        limit: null,
        offset: 0,
        hasMore: false
      }
    };

  } catch (error) {
    console.error('Error fetching all KOL profiles:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code || 'NO_CODE'
    });
    
    return {
      status: 'error',
      message: error.message || 'Unknown error occurred while fetching KOL profiles',
      error: {
        code: error.code || 'UNKNOWN',
        name: error.name || 'Error'
      },
      data: [],
      count: 0
    };
  }
}

/**
 * Transforms API or PostgreSQL data to the consistent final structure expected by consumers.
 * @param {Object} kolData - The raw KOL data from API or PostgreSQL.
 * @param {string} originalAddress - The case-sensitive requested wallet address.
 * @returns {Object} - The transformed KOL profile.
 */
function transformKolData(kolData, originalAddress) {
    if (!kolData) {
        return {
            wallet: originalAddress, // Use original case-sensitive address
            label: "Unknown", // Default if no data
            avatar: null,
            isPublic: false,
            socials: null,
            found: false // Explicitly indicate not found overall
        };
    }
    return {
        wallet: kolData.wallet || originalAddress, // Use original case-sensitive address if not in kolData
        label: kolData.label || "", // API might have empty label
        avatar: kolData.avatar || null,
        // API usually returns isPublic, PG transformed to isPublic
        isPublic: typeof kolData.isPublic === 'boolean' ? kolData.isPublic : false,
        socials: kolData.socials || null,
        // 'found' is more about whether we got a definitive record for the specific address
        // If kolData exists, we consider it found from one of the sources.
        found: true
    };
}


/**
 * Fetches KOL profile data, trying central API first, then PostgreSQL as fallback.
 * @param {string} address - The case-sensitive wallet address to fetch data for.
 * @param {boolean} [skipCache=false] - Whether to bypass the cache.
 * @returns {Promise<{success: boolean, data: object|null, error: string|null, cached: boolean, status: number}>} - Result object.
 */
export async function fetchKolApiMetadata(address, skipCache = false) {
    const requestTimestamp = new Date().toISOString();
    if (!address) {
        return { success: false, data: null, error: "Wallet address is required", cached: false, status: 400 };
    }
    // Use the original case-sensitive address for the cache key
    const cacheKey = `${REDIS_KEY_PREFIX}${address}`;
    let cached = false;
    let source = 'api'; // To track where the data came from

    try {
        // Try to get cached data from Redis unless skipCache is true
        if (!skipCache && redis) {
            const cachedDataString = await redis.get(cacheKey);
            if (cachedDataString) {
                cached = true;
                const parsedCache = JSON.parse(cachedDataString);
                // Return data in the consistent final transformed structure
                return { success: true, data: parsedCache.data, error: null, cached, status: 200 };
            }
        }

        let fetchedData = null;
        let apiError = null;
        let apiStatus = 0;

        // 1. Try fetching from API using the original case-sensitive address
        try {
            const getURL = `${process.env.NEXT_PUBLIC_STALKCHAIN_CENTRAL_API_URL}/feed/labels/wallet/${address}`;
            const response = await axios.get(getURL, {
                headers: {
                    'Api-Key': process.env.KOL_FEED_API_KEY,
                    'Content-Type': 'application/json'
                },
                timeout: REQUEST_TIMEOUT,
                maxContentLength: MAX_CONTENT_LENGTH,
            });
            fetchedData = response.data; // This is the direct API response data structure
            apiStatus = response.status;
            source = 'api';
        } catch (error) {
            apiError = error;
            apiStatus = error.response?.status || 500;
            logger.warn(`API request for ${address} failed. Status: ${apiStatus}, Error: ${error.message}`);
        }

        // 2. Fallback to PostgreSQL if API failed OR API returned 404 (meaning API doesn't have it)
        if (!fetchedData || apiStatus === 404) {
            logger.log(`API did not find/return data for ${address} (status: ${apiStatus}). Attempting PostgreSQL fallback.`);
            // Use original case-sensitive address for PostgreSQL query
            const pgData = await getKolByAddressFromPostgres(address);
            if (pgData) {
                fetchedData = pgData; // pgData is already in the target-like structure but with `source` field
                source = 'postgres';
                // If API had a 404, but PG found it, status should be 200 now.
                // If API had another error, and PG found it, status should be 200.
                apiStatus = 200;
                logger.log(`Successfully fetched data for ${address} from PostgreSQL.`);
            } else {
                logger.log(`No data found for ${address} in PostgreSQL either.`);
                // If API originally 404'd and PG also found nothing, it's a definitive not found.
                if (apiStatus === 404) {
                    const notFoundResponseData = transformKolData(null, address); // Will set found: false
                    return { success: false, data: notFoundResponseData, error: "KOL not found by API or PostgreSQL", cached, status: 404 };
                }
                // If API had a non-404 error and PG found nothing, use the original API error.
                // fetchedData remains null here.
            }
        }

        // 3. Process and return result
        if (fetchedData) {
            // Transform the fetchedData (either from API or PG) into the consistent final structure
            const finalTransformedData = transformKolData(fetchedData, address);

            // Cache the finalTransformedData (consistent structure)
            if (redis) {
                const dataToCache = JSON.stringify({
                    // Structure for cache should be what we expect to retrieve and return directly
                    data: finalTransformedData,
                    cacheInfo: {
                        timestamp: requestTimestamp,
                        source: source, // Record where the cached data originated from
                        expiresIn: `${CACHE_DURATION} seconds`
                    }
                });
                await redis.set(cacheKey, dataToCache, 'EX', CACHE_DURATION);
            }
            return { success: true, data: finalTransformedData, error: null, cached, status: apiStatus === 0 ? 200 : apiStatus };
        }

        // If fetchedData is still null here, it means API failed (non-404) and PG also failed/found nothing.
        // Reconstruct the original error to return if it exists
        const finalError = apiError?.response?.data?.error || apiError?.message || "Failed to fetch KOL data after all fallbacks";
        const finalStatus = apiStatus !== 0 && apiStatus !== 404 ? apiStatus : 500; // if apiStatus is 0 or 404, default to 500.

        return {
            success: false,
            data: transformKolData(null, address), // Return a not found structure
            error: finalError,
            cached,
            status: finalStatus
        };

    } catch (error) { // Catch unexpected errors in the fetchKolApiMetadata function itself
        logger.error(`[${requestTimestamp}] Critical error in fetchKolApiMetadata for ${address}:`, error.message);
        return {
            success: false,
            data: transformKolData(null, address),
            error: "An unexpected error occurred",
            cached,
            status: 500
        };
    }
} 

/**
 * Triggers a background sync if cooldown period has passed
 * Non-blocking operation with 1-hour cooldown
 */
async function triggerBackgroundSync() {
    try {
      const { syncKolProfiles } = await import('@/utils/cache/kolProfiles');
      const SYNC_COOLDOWN_KEY = 'stalkchain:kol:profiles:last_sync';
      const COOLDOWN_SECONDS = 60 * 60; // 1 hour
      
      // Check when last sync occurred
      const lastSync = await redis.get(SYNC_COOLDOWN_KEY);
      const now = Math.floor(Date.now() / 1000);
      
      if (!lastSync || (now - parseInt(lastSync)) > COOLDOWN_SECONDS) {
        // Set the sync timestamp immediately to prevent multiple syncs
        await redis.set(SYNC_COOLDOWN_KEY, now.toString(), 'EX', COOLDOWN_SECONDS * 2);
        
        // Trigger sync in background (non-blocking)
        setImmediate(async () => {
          try {
            console.log('Starting background KOL profiles sync...');
            const syncResult = await syncKolProfiles();
            if (syncResult.success) {
              console.log(`Background sync completed: ${syncResult.profilesAdded} profiles synced`);
            } else {
              console.error('Background sync failed:', syncResult.error);
            }
          } catch (error) {
            console.error('Background sync error:', error);
          }
        });
      }
    } catch (error) {
      console.error('Error checking sync cooldown:', error);
      // Don't throw - this is a background operation
    }
  }