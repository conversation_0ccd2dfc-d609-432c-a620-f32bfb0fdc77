'use server'

import DOMPurify from "isomorphic-dompurify";
import { query } from "@/utils/postgres";
import { getTokenUsdBalanceForWallets } from "@/actions/wallet/getWalletBalance";
import { getAllKolProfiles } from "@/actions/kol-profile/fetchKolApiData";

// Input validation
const validateTokenAddress = (address) => {
  if (!address || typeof address !== 'string' || address.length < 32) {
    throw new Error("Invalid token address");
  }
  return DOMPurify.sanitize(address);
};

// Validate time period (should be a valid PostgreSQL interval)
const validateTimePeriod = (period) => {
  if (!period || typeof period !== 'string') {
    // Default to 30 days if invalid
    return '30 days';
  }
  
  return period;
};

/**
 * Gets basic KOL token stats
 * 
 * @param {string} tokenAddress - The token mint address
 * @param {string} timePeriod - Time period for stats (PostgreSQL interval format)
 * @returns {Object} KOL token stats data
 */
export async function getKolTokenStats(tokenAddress, timePeriod = '60 days') {
  try {
    // Validate and sanitize inputs
    const sanitizedToken = validateTokenAddress(tokenAddress);
    const sanitizedPeriod = validateTimePeriod(timePeriod);
    
    // Query the database for KOL token stats
    const result = await query('SELECT * FROM fn_get_kols_token_stats($1, $2)', [
      sanitizedToken, 
      sanitizedPeriod
    ]);
    
    if (!result || !result.rows) {
      console.error("No valid result returned from database for KOL token stats");
      return {
        status: 'error',
        message: 'Failed to fetch KOL token stats',
        data: []
      };
    }

    return {
      status: 'success',
      data: result.rows
    };
  } catch (error) {
    console.error('Error fetching KOL token stats:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code || 'NO_CODE'
    });
    
    return {
      status: 'error',
      message: error.message || 'Unknown error occurred',
      error: {
        code: error.code || 'UNKNOWN',
        name: error.name || 'Error'
      },
      data: []
    };
  }
}

/**
 * Gets detailed KOL token stats with dust filtering
 * 
 * @param {string} tokenAddress - The token mint address
 * @param {string} timePeriod - Time period for stats (PostgreSQL interval format)
 * @param {number} dustFilter - Minimum value to filter out dust transactions (default: 0.001)
 * @returns {Object} Detailed KOL token stats data
 */
export async function getKolDetailedTokenStats(tokenAddress, timePeriod = '60 days', dustFilter = 0.001) {
  try {
    // Validate and sanitize inputs
    const sanitizedToken = validateTokenAddress(tokenAddress);
    const sanitizedPeriod = validateTimePeriod(timePeriod);
    
    // Validate dust filter
    let dustFilterValue = 0.001; // Default
    if (typeof dustFilter === 'number' && !isNaN(dustFilter) && dustFilter >= 0) {
      dustFilterValue = dustFilter;
    }
    
    // Query the database for detailed KOL token stats
    const result = await query('SELECT * FROM fn_get_kols_detailed_token_stats($1, $2, $3)', [
      sanitizedToken, 
      sanitizedPeriod,
      dustFilterValue
    ]);
    
    if (!result || !result.rows) {
      console.error("No valid result returned from database for detailed KOL token stats");
      return {
        status: 'error',
        message: 'Failed to fetch detailed KOL token stats',
        data: []
      };
    }

    // Get all KOL wallet addresses for balance check
    const walletAddresses = result.rows.map(row => row.wallet).filter(Boolean);
    
    // Use getTokenUsdBalanceForWallets to get current balances
    let walletBalances = {};
    if (walletAddresses.length > 0) {
      const balanceResult = await getTokenUsdBalanceForWallets(sanitizedToken, walletAddresses);
      
      if (balanceResult.success && balanceResult.balances) {
        // Create a lookup map of wallet to balance data
        walletBalances = balanceResult.balances.reduce((acc, balance) => {
          acc[balance.wallet] = balance;
          return acc;
        }, {});
      }
    }

    // Add real-time balance data to each row
    const enrichedData = result.rows.map(row => {
      // Get wallet balance from our balance lookup
      const walletBalance = walletBalances[row.wallet] || null;
      
      // Use real-time balance data if available, otherwise use 0
      const unsoldUsdAmount = walletBalance ? walletBalance.usdValue : 0;
      const tokenPrice = walletBalance ? walletBalance.price : 0;
      
      // Get buy and sell volumes
      const buyVolumeUsd = parseFloat(row.buy_volume_usd) || 0;
      const sellVolumeUsd = parseFloat(row.sell_volume_usd) || 0;
      
      // Calculate PnL percentage including remaining tokens 
      // Formula: ((Sell USD + Remaining USD) / Buy USD - 1) * 100
      let pnlPercentage = null;
      
      // Only calculate if we have bought tokens (avoid division by zero)
      if (buyVolumeUsd > 0) {
        // Check if there are unsold tokens
        if (unsoldUsdAmount !== 0 || sellVolumeUsd > 0) {
          pnlPercentage = ((sellVolumeUsd + unsoldUsdAmount) / buyVolumeUsd - 1) * 100;
        } else if (row.realized_pnl_percent !== null) {
          // Use DB-calculated realized PnL if no remaining tokens
          pnlPercentage = parseFloat(row.realized_pnl_percent);
        }
      }
      
      return {
        ...row,
        unsold_usd_amount: unsoldUsdAmount,
        token_price: tokenPrice || 0,
        // Format and store pnlPercentage if we have a value
        pnl_percentage: pnlPercentage !== null ? parseFloat(pnlPercentage.toFixed(2)) : null
      };
    });

    return {
      status: 'success',
      data: enrichedData
    };
  } catch (error) {
    console.error('Error fetching detailed KOL token stats:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code || 'NO_CODE'
    });
    
    return {
      status: 'error',
      message: error.message || 'Unknown error occurred',
      error: {
        code: error.code || 'UNKNOWN',
        name: error.name || 'Error'
      },
      data: []
    };
  }
}

/**
 * Gets current KOL token holdings for a specific token
 * 
 * @param {string} tokenAddress - The token mint address
 * @param {number} minUsdValue - Minimum USD value to include KOL (default: 5)
 * @returns {Object} KOL token holdings data
 */
export async function getKolTokenHoldings(tokenAddress, minUsdValue = 5) {
  try {
    // Validate and sanitize inputs
    const sanitizedToken = validateTokenAddress(tokenAddress);
    const minValue = typeof minUsdValue === 'number' && minUsdValue >= 0 ? minUsdValue : 5;
    
    // Step 1: Get all KOL profiles
    const kolProfilesResult = await getAllKolProfiles();
    
    if (kolProfilesResult.status !== 'success' || !kolProfilesResult.data) {
      console.error("Failed to fetch KOL profiles");
      return {
        status: 'error',
        message: 'Failed to fetch KOL profiles',
        data: {
          kolHoldersCount: 0,
          totalUsdValue: 0,
          totalTokenAmount: 0,
          holdings: []
        }
      };
    }
    
    // Step 2: Extract wallet addresses from KOL profiles
    const walletAddresses = kolProfilesResult.data.map(profile => profile.wallet).filter(Boolean);
    
    if (walletAddresses.length === 0) {
      return {
        status: 'success',
        message: 'No KOL wallets found',
        data: {
          kolHoldersCount: 0,
          totalUsdValue: 0,
          totalTokenAmount: 0,
          holdings: []
        }
      };
    }
    
    // Step 3: Get token balances for all KOL wallets
    const balanceResult = await getTokenUsdBalanceForWallets(sanitizedToken, walletAddresses);
    
    if (!balanceResult.success) {
      console.error("Failed to fetch token balances for KOL wallets:", balanceResult.error);
      return {
        status: 'error',
        message: 'Failed to fetch token balances for KOL wallets',
        error: balanceResult.error,
        data: {
          kolHoldersCount: 0,
          totalUsdValue: 0,
          totalTokenAmount: 0,
          holdings: []
        }
      };
    }
    
    // Step 4: Create a lookup map for KOL profiles
    const kolProfileMap = kolProfilesResult.data.reduce((acc, profile) => {
      acc[profile.wallet] = profile;
      return acc;
    }, {});
    
    // Step 5: Filter balances with minimum USD value and enrich with KOL data
    const validHoldings = balanceResult.balances
      .filter(balance => balance.usdValue && balance.usdValue >= minValue)
      .map(balance => {
        const kolProfile = kolProfileMap[balance.wallet];
        return {
          wallet: balance.wallet,
          label: kolProfile?.label || 'Unknown KOL',
          avatar: kolProfile?.avatar || null,
          isPublic: kolProfile?.isPublic || false,
          amount: balance.amount,
          rawAmount: balance.rawAmount,
          decimals: balance.decimals,
          price: balance.price,
          usdValue: balance.usdValue
        };
      })
      .sort((a, b) => b.usdValue - a.usdValue); // Sort by USD value descending
    
    // Step 6: Calculate totals
    const totalUsdValue = validHoldings.reduce((sum, holding) => sum + holding.usdValue, 0);
    const totalTokenAmount = validHoldings.reduce((sum, holding) => sum + holding.amount, 0);
    
    const result = {
      status: 'success',
      data: {
        tokenAddress: sanitizedToken,
        minUsdValue: minValue,
        kolHoldersCount: validHoldings.length,
        totalUsdValue: totalUsdValue,
        totalTokenAmount: totalTokenAmount,
        tokenPrice: balanceResult.price || 0,
        holdings: validHoldings
      }
    };
    
    // Console log for debugging (can be commented out later)
    console.log('getKolTokenHoldings result:', JSON.stringify(result, null, 2));
    
    return result;
    
  } catch (error) {
    console.error('Error fetching KOL token holdings:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code || 'NO_CODE'
    });
    
    return {
      status: 'error',
      message: error.message || 'Unknown error occurred while fetching KOL token holdings',
      error: {
        code: error.code || 'UNKNOWN',
        name: error.name || 'Error'
      },
      data: {
        kolHoldersCount: 0,
        totalUsdValue: 0,
        totalTokenAmount: 0,
        holdings: []
      }
    };
  }
} 