"use server";

import <PERSON><PERSON> from "stripe";

import { getDb } from "@/utils/mongodb";
import {
  getPaymentByIntent,
  getPaymentByIntentStripe,
} from "@/actions/checkout/getPaymentByIntent";
import { getAuthStateData } from "@/utils/authState";

const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
  apiVersion: "2024-09-30.acacia",
});

export const linkStripePrivy = async (subscriptionId, paymentIntent) => {
  // console.log("LINKING STRIPE PRIVY", subscriptionId, paymentIntent);
  try {
    // get the react db
    const reactDb = await getDb("stalkreact");
    // get the payment data db
    const payDb = await getDb("paymentdata");

    // get the user data (privy)
    const userData = await getAuthStateData();

    // get the user id from the user data
    const idUser = userData?.id;

    // get the payment details from the database
    // const dbPayment = await getPaymentByIntent(paymentIntent);
    const dbPayment = await payDb.collection("payments_stripe").findOne({
      paymentIntent: paymentIntent,
    });

    if(!dbPayment?.paymentIntent) {
      console.log("Payment not found, skipping", dbPayment);
      return false;
    }

    // make sure the payment is not already processed
    if (dbPayment?.isProcessed) {
      console.log("Payment already processed, skipping");
      return false;
    }

    // get the payment details from stripe
    const stripePayment = await getPaymentByIntentStripe(paymentIntent);

    // now make sure the stripe payment is successful
    if (stripePayment?.status !== "succeeded") {
      console.log("Stripe payment failed, skipping");
      return false;
    }

    // get the customer details from stripe
    const customer = await stripe.customers.retrieve(stripePayment.customer);

    // get the subscription details from stripe
    const subscription = await stripe.subscriptions.retrieve(dbPayment.subscriptionId);
    console.log("subscription", subscription);

    // get the user data from the react db
    const userDb = await reactDb.collection("users").findOne({
      id: idUser,
    });

    // now we need to check if the user is allready in the memberships db if no insert if yes update the subscription
    const membershipData = await payDb
      .collection("memberships")
      .findOne({ idUser: idUser });

    // legend = 3 | degen = 4
    const newIdRole = stripePayment.metadata.planType === "degen" || stripePayment.metadata.planType === "pro" ? 4 : 3;

    if (!membershipData) {
      // create a new membership in the memberships db
      const newMembership = {
        idUser: idUser,
        walletAddress: userData.linkedAccounts.find(
          (account) =>
            account.type === "wallet" && account.chainType === "solana"
        )?.address,
        idRole: newIdRole,
        startDate: new Date(),
        expirationDate: new Date(subscription.current_period_end * 1000),
        createdAt: new Date(),
        updatedAt: new Date(),
        email: customer.email,
        stripeCustomerId: customer.id,
      };
      await payDb.collection("memberships").insertOne(newMembership);
    } else {
      // since the user is already in the memberships db we need to update the subscription
      // and extend the expiration date from the current expiration date to the new expiration date
      await payDb.collection("memberships").updateOne(
        { idUser: idUser },
        {
          $set: {
            idRole: newIdRole,
            stripeCustomerId: customer.id,
            expirationDate: new Date(subscription.current_period_end * 1000),
            updatedAt: new Date(),
          },
        }
      );
    }

    // now we need to update the user data in the react db in case there are changes that need to be updated
    let dbUserUpdate = {};
    if (userDb.idRole !== newIdRole) {
      dbUserUpdate.idRole = newIdRole;
    }
    if (!userDb.oldDbWallet) {
      dbUserUpdate.oldDbWallet = userData.linkedAccounts.find(
        (account) => account.type === "wallet" && account.chainType === "solana"
      )?.address;
    }
    if (!userDb.oldDbEmail) {
      dbUserUpdate.oldDbEmail = customer.email;
    }
    // in case userdb needs update then we do so
    if (Object.keys(dbUserUpdate).length > 0) {
      await reactDb.collection("users").updateOne(
        {
          id: idUser,
        },
        {
          $set: dbUserUpdate,
        }
      );
    }

    // since payment was successful we need to update the dbPayment
    await payDb.collection("payments_stripe").updateOne(
      {
        paymentIntent: dbPayment?.paymentIntent,
      },
      {
        $set: {
          isProcessed: true,
          idUser: idUser,
          paymentStatus: stripePayment?.status,
          subscriptionStatus: stripePayment?.status,
          updatedAt: new Date(),
        },
      }
    );
    return true;
  } catch (error) {
    console.log("error", error);
  }
};

/* 
params {
  payment_intent: 'pi_3R1KRTBMwsd7cKYC4E5Rc68X',
  payment_intent_client_secret: 'pi_3R1KRTBMwsd7cKYC4E5Rc68X_secret_1OYd8o9etIKbUeXLOQJuhwydV',
  redirect_status: 'succeeded'
}
*/

/* 
dbPayment {
  success: true,
  data: {
    paymentId: '67cfbc253ee143d99a6cb596',
    paymentIntent: 'pi_3R1KRTBMwsd7cKYC4E5Rc68X',
    customerId: 'cus_RvAnUMksNPbPYo',
    email: '<EMAIL>',
    subscriptionId: 'sub_1R1KRSBMwsd7cKYC3t4TjfVo',
    subscriptionStatus: 'incomplete',
    planType: 'degen',
    billingCycle: 'yearly',
    paymentAmount: 79900,
    paymentCurrency: 'usd',
    paymentDate: 2025-03-11T04:29:25.179Z,
    paymentMethod: 'card',
    productId: 'prod_RoTJsHMs5dRwtK',
    priceId: 'price_1QwypcBMwsd7cKYCEIuTg4VI',
    idUser: null,
    referrer: null,
    metadata: {
      billingCycle: 'yearly',
      planType: 'degen',
      source: 'web_checkout',
      userId: 'pending',
      clientSecret: 'pi_3R1KRTBMwsd7cKYC4E5Rc68X_secret_1OYd8o9etIKbUeXLOQJuhwydV'
    }
  }
}
*/

/* 
stripePayment {
  id: 'pi_3R1KRTBMwsd7cKYC4E5Rc68X',
  object: 'payment_intent',
  amount: 79900,
  amount_capturable: 0,
  amount_details: { tip: {} },
  amount_received: 79900,
  application: null,
  application_fee_amount: null,
  automatic_payment_methods: null,
  canceled_at: null,
  cancellation_reason: null,
  capture_method: 'automatic',
  client_secret: 'pi_3R1KRTBMwsd7cKYC4E5Rc68X_secret_1OYd8o9etIKbUeXLOQJuhwydV',
  confirmation_method: 'automatic',
  created: 1741667363,
  currency: 'usd',
  customer: 'cus_RvAnUMksNPbPYo',
  description: 'Subscription creation',
  invoice: 'in_1R1KRTBMwsd7cKYC69iyWMAe',
  last_payment_error: null,
  latest_charge: 'ch_3R1KRTBMwsd7cKYC4LEgdc1j',
  livemode: false,
  metadata: {
    billingCycle: 'yearly',
    planType: 'degen',
    subscriptionId: 'sub_1R1KRSBMwsd7cKYC3t4TjfVo',
    userId: 'pending'
  },
  next_action: null,
  on_behalf_of: null,
  payment_method: 'pm_1R1KRuBMwsd7cKYC8roCW0k9',
  payment_method_configuration_details: null,
  payment_method_options: {
    card: {
      installments: null,
      mandate_options: null,
      network: null,
      request_three_d_secure: 'automatic'
    }
  },
  payment_method_types: [ 'card' ],
  processing: null,
  receipt_email: null,
  review: null,
  setup_future_usage: 'off_session',
  shipping: null,
  source: null,
  statement_descriptor: null,
  statement_descriptor_suffix: null,
  status: 'succeeded',
  transfer_data: null,
  transfer_group: null
}
*/

/* 
userData {
  id: null,
  idRole: 8,
  role: 'free',
  level: 1,
  isLoggedin: false,
  access: {
    'feature:kol-delay': 60,
    'feature:legend-community': false,
    'feature:messages': 0,
    'feature:sol-daily-trends:market-chart': 0,
    'feature:stalks:limit': 10,
    'feature:token:search': 0,
    'feature:wallet:search': 0,
    'modal:token': 0,
    'modal:token:tracker': 0,
    'modal:wallet': 0,
    'modal:wallet:balance': 0,
    'modal:wallet:copytraders': 0,
    'modal:wallet:pnl': 0,
    'modal:wallet:trx': 0,
    'route:accountconfig': true,
    'route:admin': 0,
    'route:cabal-finder': false,
    'route:daily-trends': 1,
    'route:dashboard': 1,
    'route:dca-orders-feed': 0,
    'route:dca-orders-whales': 0,
    'route:kol-feed': 1,
    'route:learning-center': true,
    'route:settings': 0,
    'route:stalks': 0,
    
    'route:top-tokens': 0,
    'route:transactions': 0,
    'route:trends-analytics': 0,
    'route:wallet-finder': false,
    'route:whale-accumulation': true
  }
}
*/
