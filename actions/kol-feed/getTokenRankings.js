'use server';

import { query } from '../../utils/postgres'; // Assuming path to postgres.js
import { revalidatePath } from 'next/cache';
import redis from '@/utils/redis.server';
import { getTokenAge } from '@/actions/tokens/getTokenDetails';

const logger = console; // Or your preferred logger

// Constants for Redis caching
const REDIS_KEY_PREFIX = 'token:rankings:';

/**
 * Map of cache TTLs (in seconds) based on materialized view refresh frequency
 */
const CACHE_TTL = {
    kol_token_rank_1h: 5 * 60,        // 5 minutes
    kol_token_rank_6h: 15 * 60,       // 15 minutes
    kol_token_rank_12h: 20 * 60,      // 20 minutes
    kol_token_rank_1d: 60 * 60,       // 1 hour
    kol_token_rank_7d: 6 * 60 * 60,   // 6 hours
    kol_token_rank_30d: 24 * 60 * 60, // 24 hours
};

/**
 * Maps UI period to PostgreSQL materialized view name.
 * @param {string} period - e.g., '1h', '6h', 'daily', '7d', 'all'
 * @returns {string} - Materialized view name
 */
function mapPeriodToMaterializedView(period) {
    switch (period) {
        case '1h':
            return 'kol_token_rank_1h';
        case '6h':
            return 'kol_token_rank_6h';
        case '12h':
            return 'kol_token_rank_12h';
        case 'daily':
        case '24h':
            return 'kol_token_rank_1d';
        case '7d':
            return 'kol_token_rank_7d';
        case '30d':
            return 'kol_token_rank_30d';
        default:
            logger.warn(`Unknown period: ${period}, defaulting to 1d.`);
            return 'kol_token_rank_1d';
    }
}

/**
 * Generate a cache key for token rankings
 * @param {Object} params - Parameters for the query
 * @returns {string} - Cache key
 */
function generateCacheKey(params) {
    const { viewName, limit = 100 } = params;
    return `${REDIS_KEY_PREFIX}${viewName}:limit-${limit}`;
}

/**
 * Get KOL token rankings from materialized views in PostgreSQL.
 * @param {Object} options - The options object
 * @param {string} options.viewName - PostgreSQL materialized view name
 * @param {number} [options.limit=100] - Maximum number of tokens to return
 * @returns {Promise<Array>} - Array of token ranking objects from PostgreSQL
 */
async function getKolsTokenRankingsFromMaterializedView(options) {
    try {
        const { viewName, limit = 100 } = options;

        if (!viewName) {
            throw new Error('Materialized view name is required');
        }

        logger.log(`Querying PostgreSQL materialized view: ${viewName}, limit: ${limit}`);

        // Query the materialized view directly based on documentation
        const result = await query(
            `SELECT * FROM ${viewName} 
             ORDER BY total_volume_usd DESC 
             LIMIT $1`,
            [limit]
        );

        return result.rows || [];
    } catch (error) {
        logger.error('Error fetching KOL token rankings from materialized view:', {
            message: error.message,
            options,
        });
        throw error;
    }
}

/**
 * Fetches token mint timestamps for a batch of tokens
 * @param {Array} tokens - Array of token objects with token_mint property
 * @returns {Promise<Object>} - Map of token addresses to their mint timestamps
 */
async function fetchTokenMintTimestamps(tokens) {
    const timestampMap = {};
    const uniqueTokenAddresses = [...new Set(tokens.map(token => token.token_mint).filter(Boolean))];
    // Process in batches to avoid overwhelming the API
    const batchSize = 10;
    for (let i = 0; i < uniqueTokenAddresses.length; i += batchSize) {
        const batch = uniqueTokenAddresses.slice(i, i + batchSize);
        const promises = batch.map(async (tokenAddress) => {
            try {
                const ageData = await getTokenAge(tokenAddress, { useCache: true });
                if (ageData && ageData.mintTimestamp) {
                    timestampMap[tokenAddress] = ageData.mintTimestamp;
                }
            } catch (error) {
                logger.error(`Error fetching mint timestamp for ${tokenAddress}:`, error.message);
            }
        });

        await Promise.all(promises);
    }

    return timestampMap;
}

/**
 * Fetches KOL token rankings from PostgreSQL materialized views with Redis caching.
 * @param {Object} params - Parameters
 * @param {string} params.period - Time period ('1h', '6h', '12h', 'daily', '7d', '30d')
 * @param {number} [params.limit=100] - Limit of records to return
 * @param {boolean} [params.useCache=true] - Whether to use the cache
 * @returns {Promise<{tokens: Array, timestamp: number, timeRange: string}>}
 */
export async function getTokenRankingsAction({ period, limit = 100, useCache = true }) {
    const viewName = mapPeriodToMaterializedView(period);
    let tokens = [];
    const requestTimestamp = Math.floor(Date.now() / 1000);

    // Generate cache key
    const cacheKey = generateCacheKey({ viewName, limit });

    try {
        // Try to get data from cache if not bypassing cache
        if (useCache && redis) {
            const cachedData = await redis.get(cacheKey);
            if (cachedData) {
                const parsedData = JSON.parse(cachedData);
                if (parsedData.success && parsedData.data) {
                    return {
                        tokens: parsedData.data.tokens || [],
                        timestamp: requestTimestamp,
                        timeRange: period,
                        fromCache: true
                    };
                }
            }
        }
        tokens = await getKolsTokenRankingsFromMaterializedView({
            viewName,
            limit,
        });

        // Fetch mint timestamps for all tokens in one batch
        if (tokens.length > 0) {
            const timestampMap = await fetchTokenMintTimestamps(tokens);
            // Add mint timestamps to tokens
            tokens = tokens.map(token => {
                if (token.token_mint && timestampMap[token.token_mint]) {
                    return {
                        ...token,
                        mintTimestamp: timestampMap[token.token_mint]
                    };
                }
                return token;
            });
        }

        // Prepare data for caching
        const responsePayload = JSON.stringify({
            success: true,
            data: {
                tokens,
                responseTimestamp: new Date().toISOString()
            },
            cacheInfo: {
                type: 'token_rankings',
                period: period,
                viewName: viewName,
                expiresIn: `${CACHE_TTL[viewName]} seconds`
            }
        });

        // Cache the response in Redis if available and we have meaningful data
        if (redis && tokens.length > 0) {
            const ttl = CACHE_TTL[viewName] || 3600; // Default to 1 hour if view not found in TTL map
            await redis.set(cacheKey, responsePayload, 'EX', ttl);
        }
    } catch (error) {
        logger.error(`Error fetching token rankings for period ${period}:`, error.message);
        tokens = []; // Default to empty array on error
    }

    // Revalidate the paths where this data is displayed
    revalidatePath('/top-kol-tokens');

    return {
        tokens: tokens,
        timestamp: requestTimestamp,
        timeRange: period, // The original requested period
        fromCache: false
    };
}

export { getKolsTokenRankingsFromMaterializedView };