# MongoDB Collections Structure

This document serves as a source of truth for the MongoDB collections structure used in the checkout and membership system.

## stripe.checkout_completed

Stores information about completed checkout sessions from Stripe.

### Fields

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| id | String | Stripe checkout session ID |
| status | String | Status of the checkout (e.g., "complete") |
| mode | String | Checkout mode (e.g., "subscription") |
| promotekitReferral | String | Referral ID if applicable |
| customer | String | Stripe customer ID |
| subscription | String | Stripe subscription ID |
| email | String | Customer email address |
| amountSubtotal | Number | Subtotal amount in cents |
| amountTotal | Number | Total amount in cents |
| currency | String | Currency code (e.g., "usd") |
| plan | String | Plan name (e.g., "PRO") |
| billingPeriodValue | Number | Length of billing period |
| billingPeriodUnit | String | Unit of billing period (e.g., "month") |
| billingInterval | String | Billing interval details |
| userId | String | User ID in our system |
| created | Date | When the checkout was created |
| expiresAt | Date | When the checkout session expires |
| startDate | Date | Subscription start date |
| expirationDate | Date | Subscription expiration date |
| roleId | Number | Role ID for the subscription |
| fullWebhook | Object | Complete webhook payload from Stripe |
| createdAt | Date | Document creation timestamp |
| updatedAt | Date | Document update timestamp |

### Example

```json
{
  "_id": {"$oid": "682c505ad4980661ee19f26a"},
  "id": "cs_test_b1bbv0Dy7irsogWQZxCcnbigLOVbTmmsvTFPHbulXSVJzThV5aDLliBeKt",
  "status": "complete",
  "mode": "subscription",
  "promotekitReferral": "8f740b2c-a617-41aa-b882-6859d415858c",
  "customer": "cus_SLTlOKJFKsomXB",
  "subscription": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
  "email": "<EMAIL>",
  "amountSubtotal": {"$numberInt": "79900"},
  "amountTotal": {"$numberInt": "79900"},
  "currency": "usd",
  "plan": "PRO",
  "billingPeriodValue": {"$numberInt": "1"},
  "billingPeriodUnit": "year",
  "billingInterval": "",
  "userId": "",
  "created": {"$date": {"$numberLong": "*************"}},
  "expiresAt": {"$date": {"$numberLong": "1747820983000"}},
  "startDate": {"$date": {"$numberLong": "**********000"}},
  "expirationDate": {"$date": {"$numberLong": "1779270611000"}},
  "roleId": {"$numberInt": "4"},
  "fullWebhook": {
    "id": "cs_test_b1bbv0Dy7irsogWQZxCcnbigLOVbTmmsvTFPHbulXSVJzThV5aDLliBeKt",
    "object": "checkout.session",
    "adaptive_pricing": null,
    "after_expiration": null,
    "allow_promotion_codes": true,
    "amount_subtotal": {"$numberInt": "79900"},
    "amount_total": {"$numberInt": "79900"},
    "automatic_tax": {"enabled": false, "liability": null, "provider": null, "status": null},
    "billing_address_collection": "auto",
    "cancel_url": "http://localhost:3000/",
    "client_reference_id": null,
    "client_secret": null,
    "collected_information": {"shipping_details": null},
    "consent": {"promotions": null, "terms_of_service": "accepted"},
    "consent_collection": {"payment_method_reuse_agreement": null, "promotions": "none", "terms_of_service": "required"},
    "created": {"$numberInt": "**********"},
    "currency": "usd",
    "currency_conversion": null,
    "custom_fields": [],
    "custom_text": {"after_submit": null, "shipping_address": null, "submit": null, "terms_of_service_acceptance": {"message": "I agree to the [Terms of Service](http://stalkchain.com/terms-conditions)"}},
    "customer": "cus_SLTlOKJFKsomXB",
    "customer_creation": "always",
    "customer_details": {"address": {"city": null, "country": "TH", "line1": null, "line2": null, "postal_code": null, "state": null}, "email": "<EMAIL>", "name": "Kevin Callens", "phone": null, "tax_exempt": "none", "tax_ids": []},
    "customer_email": null,
    "discounts": [],
    "expires_at": {"$numberInt": "1747820983"},
    "invoice": "in_1RQmoJBMwsd7cKYCkDDENo5w",
    "invoice_creation": null,
    "livemode": false,
    "locale": null,
    "metadata": {"billingPeriodValue": "1", "billingPeriodUnit": "year", "userId": "", "planName": "PRO"},
    "mode": "subscription",
    "payment_intent": null,
    "payment_link": null,
    "payment_method_collection": "always",
    "payment_method_configuration_details": null,
    "payment_method_options": {"card": {"request_three_d_secure": "automatic"}},
    "payment_method_types": ["card"],
    "payment_status": "paid",
    "permissions": null,
    "phone_number_collection": {"enabled": false},
    "recovered_from": null,
    "saved_payment_method_options": {"allow_redisplay_filters": ["always"], "payment_method_remove": "disabled", "payment_method_save": null},
    "setup_intent": null,
    "shipping_address_collection": null,
    "shipping_cost": null,
    "shipping_details": null,
    "shipping_options": [],
    "status": "complete",
    "submit_type": null,
    "subscription": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
    "success_url": "http://localhost:3000/checkout/success?session_id={CHECKOUT_SESSION_ID}",
    "total_details": {"amount_discount": {"$numberInt": "0"}, "amount_shipping": {"$numberInt": "0"}, "amount_tax": {"$numberInt": "0"}},
    "ui_mode": "hosted",
    "url": null,
    "wallet_options": null
  },
  "createdAt": {"$date": {"$numberLong": "*************"}},
  "updatedAt": {"$date": {"$numberLong": "*************"}}
}
```

## stripe.activation_tokens

Stores tokens for account activation after checkout completion.

### Fields

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| token | String | Activation token |
| sessionId | String | Stripe checkout session ID |
| subscriptionId | String | Stripe subscription ID |
| customerId | String | Stripe customer ID |
| email | String | Customer email address |
| planName | String | Plan name (e.g., "PRO") |
| billingPeriodValue | Number | Length of billing period |
| billingPeriodUnit | String | Unit of billing period (e.g., "month") |
| billingInterval | String | Billing interval details |
| createdAt | Date | Token creation timestamp |
| expiresAt | Date | Token expiration timestamp |
| used | Boolean | Whether token has been used |
| activatedAt | Date | When token was activated |
| activatedBy | String | User ID who activated the token |

### Example

```json
{
  "_id": {"$oid": "682c505bd4980661ee19f26b"},
  "token": "54a2f511ae992e2cd5777b1a727c5616de755f1c9acd0eb264e4e1f591c19a22",
  "sessionId": "cs_test_b1bbv0Dy7irsogWQZxCcnbigLOVbTmmsvTFPHbulXSVJzThV5aDLliBeKt",
  "subscriptionId": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
  "customerId": "cus_SLTlOKJFKsomXB",
  "email": "<EMAIL>",
  "planName": "PRO",
  "billingPeriodValue": {"$numberInt": "1"},
  "billingPeriodUnit": "year",
  "billingInterval": "",
  "createdAt": {"$date": {"$numberLong": "1747734619705"}},
  "expiresAt": {"$date": {"$numberLong": "1748339419705"}},
  "used": true,
  "activatedAt": {"$date": {"$numberLong": "1747734689500"}},
  "activatedBy": "33ef82ea-5414-4a8a-831f-5a95af667c0d"
}
```

## paymentdata.memberships

Links users to their subscription status and roles.

### Fields

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| idUser | String | User ID in our system |
| walletAddress | String | User's wallet address |
| idRole | Number | Role ID (permission level) |
| startDate | Date | Subscription start date |
| expirationDate | Date | Subscription expiration date |
| createdAt | Date | Document creation timestamp |
| updatedAt | Date | Document update timestamp |
| email | String | User's email address |
| stripeCustomerId | String | Stripe customer ID |
| stripeSubscriptionId | String | Stripe subscription ID |
| referredBy | String | User ID of referrer |

### Example

```json
{
  "_id": {"$oid": "682c50a0d4980661ee19f26c"},
  "idUser": "33ef82ea-5414-4a8a-831f-5a95af667c0d",
  "idRole": {"$numberInt": "4"},
  "stripeCustomerId": "cus_SLTlOKJFKsomXB",
  "stripeSubscriptionId": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
  "email": "<EMAIL>",
  "walletAddress": null,
  "referredBy": "8f740b2c-a617-41aa-b882-6859d415858c",
  "startDate": {"$date": {"$numberLong": "*************"}},
  "expirationDate": {"$date": {"$numberLong": "*************"}},
  "createdAt": {"$date": {"$numberLong": "*************"}},
  "updatedAt": {"$date": {"$numberLong": "*************"}}
}
```

## stalkreact.users

Main user collection with authentication and linked accounts.

### Fields

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| id | String | User ID in our system |
| privyId | String | Privy authentication ID |
| enabled | Boolean | Whether user is enabled |
| idRole | Number | Role ID (permission level) |
| linkedAccounts | Array | Array of linked accounts (email, wallet) |
| createdAt | Date | User creation timestamp |
| updatedAt | Date | User update timestamp |
| oldDbEmail | String | Email from previous database |
| oldDbWallet | String | Wallet from previous database |
| didWatchVideo | Boolean | Whether user watched intro video |
| expirationDate | Date | Subscription expiration date |
| stripeCustomerId | String | Stripe customer ID |

### Example

```json
{
  "_id": {"$oid": "682aab4487337a1161effafa"},
  "id": "33ef82ea-5414-4a8a-831f-5a95af667c0d",
  "privyId": "did:privy:cmaujvvi6014djr0nohwi8qak",
  "enabled": true,
  "idRole": {"$numberInt": "4"},
  "linkedAccounts": [
    {
      "type": "email",
      "address": "<EMAIL>",
      "latestVerifiedAt": {"$date": {"$numberLong": "*************"}}
    },
    {
      "type": "wallet",
      "address": "7W1Utn4ZMYkjGxgiWYyA2VYKGVPyEBfb6nbc8Zdd2jkG",
      "chainType": "solana",
      "walletClientType": "privy",
      "latestVerifiedAt": {"$date": {"$numberLong": "*************"}}
    }
  ],
  "createdAt": {"$date": {"$numberLong": "*************"}},
  "updatedAt": {"$date": {"$numberLong": "*************"}},
  "expirationDate": {"$date": {"$numberLong": "*************"}},
  "oldDbEmail": "<EMAIL>",
  "stripeCustomerId": "cus_SLTlOKJFKsomXB"
}
```

## stripe.log_customer

Logs customer subscription events and changes.

### Fields

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| createdAt | Date | Log creation timestamp |
| eventId | String | Stripe event ID |
| eventType | String | Type of Stripe event |
| customer | String | Stripe customer ID |
| idUser | String | User ID in our system |
| subscription | String | Stripe subscription ID |
| cancelAtPeriodEnd | Boolean | Whether subscription will cancel at period end |
| cancelAt | Date/null | When subscription is scheduled to cancel |
| canceledAt | Number | Timestamp when subscription was canceled |
| currentPeriodEnd | Number | Timestamp when current billing period ends |
| beforeChange | Object | Subscription state before change |
| afterChange | Object | Subscription state after change |
| isImmediateCancellation | Boolean | Whether cancellation was immediate |

### Example

```json
{
  "_id": {"$oid": "682c6c93ca93c5a901e5d52b"},
  "createdAt": {"$date": {"$numberLong": "1747741843647"}},
  "eventId": "evt_1RQogxBMwsd7cKYCZPrCter1",
  "eventType": "customer.subscription.deleted",
  "customer": "cus_SLUi0ntoscljD4",
  "idUser": "5c4fcc7b-261c-48ee-bf61-3bf2ad164a2e",
  "subscription": "sub_1RQnisBMwsd7cKYCdf5V2vi8",
  "cancelAtPeriodEnd": false,
  "cancelAt": null,
  "canceledAt": {"$numberInt": "1747741842"},
  "currentPeriodEnd": {"$numberInt": "1750416518"},
  "beforeChange": {
    "status": "active",
    "cancel_at": null,
    "cancel_at_period_end": false,
    "canceled_at": null,
    "ended_at": null,
    "idRole": {"$numberInt": "4"},
    "expirationDate": {"$date": {"$numberLong": "1747824508000"}}
  },
  "afterChange": {
    "status": "canceled",
    "canceled_at": {"$numberInt": "1747741842"},
    "ended_at": {"$numberInt": "1747741842"},
    "cancel_at": null,
    "cancel_at_period_end": false,
    "idRole": {"$numberInt": "8"},
    "expirationDate": {"$date": {"$numberLong": "1747741843647"}}
  },
  "isImmediateCancellation": true
}
```
