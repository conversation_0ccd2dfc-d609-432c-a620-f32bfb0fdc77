"use server";

import { getAuthStateData } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";

/**
 * Activate a user account using either a Stripe session ID or activation token
 * 
 * This function handles account activation after payment by:
 * 1. Verifying the user is logged in with Privy
 * 2. Checking if user exists in database
 * 3. Processing and validating activation token
 * 4. Verifying checkout data
 * 5. Determining membership role and terms
 * 6. Creating or updating membership records
 * 7. Marking tokens as used after successful activation
 * 
 * Flow:
 * - If user not logged in → Error
 * - If user not in database → Error (require Privy login first)
 * - Find activation token by sessionId or token
 * - If token not found → Error
 * - If token already used:
 *   - If customer already activated → "Account already activated" error
 *   - If customer not activated → "Token used, contact support" error
 * - If valid token → Continue with activation
 * - Determine membership details (role, expiration) from checkout data
 * - Extract start date from checkout creation timestamp
 * - If user already has membership → Update existing membership
 * - If new user → Create new membership with their non-privy Solana wallet (if available)
 * - Update user role and subscription details
 * - Mark token as used
 * 
 * @param {string|null} sessionId - The Stripe checkout session ID
 * @param {string|null} token - The activation token
 * @returns {Object} Response object with success status and relevant data/messages
 */
export const activateAccount = async (sessionId = null, token = null) => {
  // get the user data (privy)
  const authData = await getAuthStateData();

  // get the user id from the user data
  const idUser = authData?.id;

  // Check if user is logged in
  if (!authData || !idUser) {
    return {
      success: false,
      message: "You need to be logged in to activate your account."
    };
  }
  
  // Validate input parameters
  if (!sessionId && !token) {
    return {
      success: false,
      message: "No activation token provided. Please use the complete activation link from your email."
    };
  }

  const reactDb = await getDb("stalkreact");
  const paymentDb = await getDb("paymentdata");
  const stripeDb = await getDb("stripe");

  // Check if the user exists in users collection
  const existingUser = await reactDb.collection("users").findOne({
    id: idUser
  });

  if (!existingUser) {
    return {
      success: false,
      message: "User account not found. Please log in first to create your account before activating."
    };
  }
  
  // Find activation token by either direct token or session ID
  let activationToken = null;
  
  if (token) {
    activationToken = await stripeDb.collection("activation_tokens").findOne({
      token: token
    });
  } else if (sessionId) {
    activationToken = await stripeDb.collection("activation_tokens").findOne({
      sessionId: sessionId
    });
  }
  
  if (!activationToken) {
    return {
      success: false,
      message: "Invalid activation token. Please check your email for the correct activation link."
    };
  }
  
  // Check if token is already used
  if (activationToken.used) {
    // Check if this customer ID exists in memberships
    const membershipForCustomer = await paymentDb.collection("memberships").findOne({
      stripeCustomerId: activationToken.customerId
    });
    
    if (membershipForCustomer) {
      return {
        success: false,
        message: "This subscription has already been activated. If you need access, please contact support.",
        alreadyActivated: true,
        activatedBy: activationToken.activatedBy || null
      };
    } else {
      return {
        success: false,
        message: "This activation link has already been used. If you need a new activation link, please contact support.",
        tokenUsed: true,
        activatedBy: activationToken.activatedBy || null
      };
    }
  }
  
  // Extract subscription and customer IDs from the token
  const stripeSubscriptionId = activationToken.subscriptionId;
  const stripeCustomerId = activationToken.customerId;

  console.log(`Looking for checkout data with subscription ID: ${stripeSubscriptionId}`);
  const checkoutData = await stripeDb.collection("checkout_completed").findOne({
    subscription: stripeSubscriptionId
  });

  if (!checkoutData) {
    console.log(`Checkout data not found for subscription: ${stripeSubscriptionId}`);
    return {
      success: false,
      message: "We couldn't find your payment information. This may be due to an invalid activation link. Please contact support for assistance."
    };
  }

  console.log(`Found checkout data: ${checkoutData._id}`);

  if (checkoutData.customer !== stripeCustomerId) {
    console.log(`Customer ID mismatch. Expected: ${stripeCustomerId}, Found: ${checkoutData.customer}`);
    return {
      success: false,
      message: "Customer ID mismatch. Your activation link appears to be invalid. Please contact support."
    };
  }

  // Determine role based on plan name
  // Default to Pro (idRole 4) if plan name can't be determined
  let idRole = 4; // Pro membership by default
  let planName = '';
  
  if (checkoutData.plan) {
    planName = checkoutData.plan.toLowerCase();
  } else if (checkoutData.fullWebhook && checkoutData.fullWebhook.metadata && checkoutData.fullWebhook.metadata.planName) {
    planName = checkoutData.fullWebhook.metadata.planName.toLowerCase();
  } else if (activationToken.planName) {
    planName = activationToken.planName.toLowerCase();
  }
  
  if (planName.includes('legend')) {
    idRole = 3; // Legend membership
  } else if (planName.includes('pro')) {
    idRole = 4; // Pro membership
  }

  // Get user email from token or checkout data
  const email = activationToken.email || checkoutData.email || '';
  
  // Find the non-privy Solana wallet address if available
  const walletAddress = authData.linkedAccounts?.find(
    account => account.type === "wallet" && 
              account.chainType === "solana" && 
              account.walletClientType !== "privy"
  )?.address || null;
  
  // Get the affiliate/referral code from checkout data
  let referralCode = null;
  if (checkoutData.promotekitReferral) {
    referralCode = checkoutData.promotekitReferral;
  } else if (checkoutData.fullWebhook && checkoutData.fullWebhook.metadata && checkoutData.fullWebhook.metadata.promotekitReferral) {
    referralCode = checkoutData.fullWebhook.metadata.promotekitReferral;
  }

  // Get start date from checkout data instead of current date
  let startDate = new Date();
  
  // Try to get the start date from checkout data
  if (checkoutData.created) {
    startDate = new Date(checkoutData.created);
  } 
  // If not available, try getting it from the webhook payload
  else if (checkoutData.fullWebhook && checkoutData.fullWebhook.created) {
    startDate = new Date(checkoutData.fullWebhook.created * 1000); // Convert Unix timestamp to milliseconds
  }
  
  // Get current date for timestamps
  const now = new Date();
  
  // Get the expiration date from checkout data or token
  let expirationDate = null;
  
  // First try to get from checkout data - use expirationDate (subscription period), NOT expiresAt (session expiry)
  if (checkoutData.expirationDate) {
    expirationDate = new Date(checkoutData.expirationDate);
  } 
  // Then check if we can fetch it from Stripe API directly using subscription ID
  else if (stripeSubscriptionId) {
    try {
      const Stripe = require('stripe');
      const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
        apiVersion: '2024-09-30.acacia',
      });
      
      const subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId);
      if (subscription.current_period_end) {
        expirationDate = new Date(subscription.current_period_end * 1000);
      }
    } catch (error) {
      console.error('Error fetching subscription from Stripe:', error);
    }
  }
  // If all else fails, default to 30 days from now
  if (!expirationDate) {
    expirationDate = new Date(now);
    expirationDate.setDate(expirationDate.getDate() + 30);
    console.warn('Using 30-day fallback for expiration date - this should not happen');
  }

  // Check if the user already has a membership to update
  const existingUserMembership = await paymentDb.collection("memberships").findOne({
    idUser: idUser
  });

  // Check if the customer already exists in memberships under a different user
  const existingCustomerMembership = await paymentDb.collection("memberships").findOne({
    stripeCustomerId: stripeCustomerId,
    idUser: { $ne: idUser }
  });

  // If customer exists under a different user, that's a problem
  if (existingCustomerMembership) {
    return {
      success: false,
      message: "This Stripe customer is already associated with a different user. Please contact support.",
      existingMembership: true,
      idUser: existingCustomerMembership.idUser
    };
  }

  if (existingUserMembership) {
    // Update existing membership
    await paymentDb.collection("memberships").updateOne(
      { idUser: idUser },
      { 
        $set: {
          idRole: idRole,
          stripeCustomerId: stripeCustomerId,
          stripeSubscriptionId: stripeSubscriptionId,
          email: email,
          startDate: startDate,
          expirationDate: expirationDate,
          updatedAt: now
        }
      }
    );
  } else {
    // Create new membership for the user
    const newMembership = {
      idUser: idUser,
      idRole: idRole,
      stripeCustomerId: stripeCustomerId,
      stripeSubscriptionId: stripeSubscriptionId,
      email: email,
      walletAddress: walletAddress,
      referredBy: referralCode,
      startDate: startDate,
      expirationDate: expirationDate,
      createdAt: now,
      updatedAt: now
    };
    
    await paymentDb.collection("memberships").insertOne(newMembership);
  }

  // Prepare update fields for user
  const updateFields = {
    idRole,
    stripeCustomerId,
    expirationDate,
    updatedAt: now
  };

  // Add oldDbWallet and oldDbEmail if available
  if (walletAddress) updateFields.oldDbWallet = walletAddress;
  if (email) updateFields.oldDbEmail = email;

  // Update user's role and subscription details
  await reactDb.collection("users").updateOne(
    { id: idUser },
    {
      $set: updateFields
    }
  );

  // Mark token as used
  await stripeDb.collection("activation_tokens").updateOne(
    { _id: activationToken._id },
    { $set: { used: true, activatedAt: new Date(), activatedBy: idUser } }
  );
  
  // Serialize the result data to plain objects
  const serializedResult = {
    success: true,
    idUser,
    role: idRole,
    // Don't include full MongoDB objects, just the needed values
    userData: {
      id: authData.id,
      email: authData.linkedAccounts?.find(account => account.type === "email")?.address || email
    }
  };
  
  return serializedResult;
}