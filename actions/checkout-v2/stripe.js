"use server";

import <PERSON><PERSON> from 'stripe';
import { sanitize } from 'isomorphic-dompurify';

/**
 * IMPORTANT RULES FOR STRIPE CHECKOUT:
 * 1. client_reference_id should ONLY be promotekit_referral - NEVER use userId here
 * 2. userId should be included in metadata as user_id (this is the privy id)
 * 3. Do not collect billing address unless specifically requested
 * 4. Always use subscription mode for recurring payments
 * 5. Let <PERSON><PERSON> handle email collection - don't collect before checkout
 * 6. Always use billing_period_value and billing_period_unit for billing period information
 */

// Initialize Stripe with the private API key
const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
  apiVersion: '2024-09-30.acacia', // Using the latest API version
});

/**
 * Sanitizes input parameters to prevent XSS and injection attacks
 * @param {string|number} input - The input to sanitize
 * @returns {string} - The sanitized input
 */
const sanitizeInput = (input) => {
  if (input === null || input === undefined) return input;
  
  // Convert numbers to strings for sanitization
  const stringValue = typeof input === 'number' ? String(input) : input;
  
  // Return sanitized input
  return sanitize(stringValue);
};

/**
 * Retrieves and processes a Stripe checkout session
 * @param {string} sessionId - The Stripe checkout session ID
 * @returns {Promise<{success: boolean, data?: Object, error?: string}>}
 */
export async function getCheckoutSession(sessionId) {
  try {
    if (!sessionId) {
      return {
        success: false,
        error: 'No session ID provided'
      };
    }

    // Retrieve the session data from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    // Check if session status is complete
    if (session.status === 'complete') {
      // Initialize default checkout data
      const checkoutData = {
        customer: session.customer || '',
        subscription: session.subscription || '',
        status: session.status || '',
        email: session?.customer_details?.email || '',
        expiresAt: session.expires_at ? new Date(session.expires_at * 1000) : null,
        billingInterval: session?.metadata?.billingInterval || '',
        billingPeriodValue: parseInt(session?.metadata?.billing_period_value || session?.metadata?.billingPeriodValue) || 1,
        billingPeriodUnit: session?.metadata?.billing_period_unit || session?.metadata?.billingPeriodUnit || 'month',
        planName: session?.metadata?.plan_name || session?.metadata?.planName || 'PRO',
        userId: session?.metadata?.userId || '',
        sessionId: sessionId,
        // Financial data
        amountTotal: session.amount_total || 0,
        amountSubtotal: session.amount_subtotal || 0,
        currency: (session.currency || 'usd').toUpperCase()
      };
      
      // If we have a subscription ID and metadata is incomplete, fetch additional data from Stripe
      if (session.subscription && 
          (!session.metadata || 
           Object.keys(session.metadata).length === 0 || 
           !checkoutData.billingPeriodValue || 
           !checkoutData.billingPeriodUnit)) {
        
        try {
          console.log(`Metadata missing or incomplete for session ${sessionId}, fetching subscription details...`);
          
          // Fetch the subscription details from Stripe
          const subscription = await stripe.subscriptions.retrieve(session.subscription);
          
          if (subscription) {
            // Extract billing period details from the plan
            if (subscription.items?.data?.[0]?.plan) {
              const plan = subscription.items.data[0].plan;
              
              // Update billing period information
              checkoutData.billingPeriodValue = plan.interval_count || 1;
              checkoutData.billingPeriodUnit = plan.interval || 'month';
              
              console.log(`Updated billing period from subscription: ${checkoutData.billingPeriodValue} ${checkoutData.billingPeriodUnit}(s)`);
              
              // Get the product details for plan name
              if (plan.product) {
                try {
                  const product = await stripe.products.retrieve(plan.product);
                  if (product && product.name) {
                    // Extract plan name (PRO or LEGEND) from product name
                    const productName = product.name.toUpperCase();
                    if (productName.includes('PRO')) {
                      checkoutData.planName = 'PRO';
                    } else if (productName.includes('LEGEND')) {
                      checkoutData.planName = 'LEGEND';
                    }
                    console.log(`Updated plan name from product: ${checkoutData.planName}`);
                  }
                } catch (productError) {
                  console.warn(`Error fetching product details: ${productError.message}`);
                }
              }
            }
            
            // Set expiration date from subscription if not set from session
            if (subscription.current_period_end && !checkoutData.expiresAt) {
              checkoutData.expiresAt = new Date(subscription.current_period_end * 1000);
            }
          }
        } catch (subscriptionError) {
          console.warn(`Error fetching subscription details: ${subscriptionError.message}`);
          // Continue with whatever data we have from the session
        }
      }
      
      return {
        success: true,
        data: checkoutData,
        session: session
      };
    } else {
      return {
        success: false,
        error: `Session status is ${session.status}`,
        session: session
      };
    }
  } catch (error) {
    console.warn('Error retrieving Stripe checkout session:', error);
    return {
      success: false,
      error: error.message || 'Failed to retrieve checkout session',
    };
  }
}

/**
 * Creates a Stripe checkout session for subscription
 * @param {Object} params
 * @param {string} params.priceId - The Stripe price ID
 * @param {string} params.userId - User ID for tracking (optional)
 * @param {string} params.planName - Name of the plan (PRO or Legend)
 * @param {string} params.referralId - Referral ID from promotekit (optional)
 * @param {number} params.billingPeriodValue - The billing period value (e.g., 1, 3, 12)
 * @param {string} params.billingPeriodUnit - The billing period unit (day, week, month, year)
 * @param {number} params.trialDays - Number of days for trial period (default: 0 for no trial)
 * @returns {Promise<{success: boolean, url?: string, error?: string}>}
 */
export async function createStripeCheckout({ 
  priceId, 
  userId = null, 
  planName = 'PRO',
  referralId = null,
  billingPeriodValue = 1,
  billingPeriodUnit = 'month',
  trialDays = 0
}) {
  try {
    // Sanitize all inputs before using them
    const sanitizedPriceId = sanitizeInput(priceId);
    const sanitizedUserId = sanitizeInput(userId);
    const sanitizedPlanName = sanitizeInput(planName);
    const sanitizedReferralId = sanitizeInput(referralId);
    const sanitizedBillingPeriodValue = parseInt(sanitizeInput(billingPeriodValue)) || 1;
    const sanitizedBillingPeriodUnit = sanitizeInput(billingPeriodUnit);
    const sanitizedTrialDays = parseInt(sanitizeInput(trialDays)) || 0;
    
    if (!sanitizedPriceId) {
      return {
        success: false,
        error: 'No price ID provided'
      };
    }

    // Prepare metadata with sanitized values
    const metadata = {
      planName: sanitizedPlanName,
      billingPeriodValue: sanitizedBillingPeriodValue,
      billingPeriodUnit: sanitizedBillingPeriodUnit,
    };

    // Add trial days to metadata if provided
    if (sanitizedTrialDays > 0) {
      metadata.trialDays = sanitizedTrialDays;
    }

    // Add user ID to metadata if available
    if (sanitizedUserId) {
      metadata.userId = sanitizedUserId;
    }

    // Create checkout session options
    const sessionOptions = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: sanitizedPriceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.SITE_URL || 'https://stalkchain.com'}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.SITE_URL || 'https://stalkchain.com'}/`,
      metadata,
      // We don't collect customer email before checkout, let Stripe handle it
      customer_email: undefined,
      // ONLY use referralId (promotekit_referral) for client_reference_id
      client_reference_id: sanitizedReferralId || undefined,
      allow_promotion_codes: true,
      // Do not collect billing address
      billing_address_collection: 'auto',
      // Require terms of service acceptance
      consent_collection: {
        terms_of_service: 'required',
      },
      custom_text: {
        terms_of_service_acceptance: {
          message: 'I agree to the [Terms of Service](http://stalkchain.com/terms-conditions)',
        },
      },
    };

    // Add trial period if specified
    if (sanitizedTrialDays > 0) {
      sessionOptions.subscription_data = {
        trial_period_days: sanitizedTrialDays,
      };
    }

    // Create a checkout session with Stripe using sanitized inputs
    const session = await stripe.checkout.sessions.create(sessionOptions);

    return {
      success: true,
      url: session.url,
    };
  } catch (error) {
    console.error('Error creating Stripe checkout session:', error);
    return {
      success: false,
      error: error.message || 'Failed to create checkout session',
    };
  }
} 