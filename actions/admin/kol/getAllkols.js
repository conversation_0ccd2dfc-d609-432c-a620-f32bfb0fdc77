import redis from "@/utils/redis.server";
import { query, getClient } from "@/utils/postgres";

const CACHE_TTL = 600; // 10 min cache for KOL profiles

/**
 * Executes a database query with retry logic
 * @param {string} queryText - SQL query to execute
 * @param {Array} params - Query parameters
 * @param {number} maxRetries - Maximum number of retry attempts
 * @returns {Promise} Query result
 */
async function executeWithRetry(queryText, params = [], maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await query(queryText, params);
            return result;
        } catch (error) {
            lastError = error;
            console.error(`Query attempt ${attempt} failed:`, error.message);

            // Don't retry on the last attempt
            if (attempt === maxRetries) {
                break;
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.pow(2, attempt) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw lastError;
}

/**
 * Retrieves all KOL profiles from the database
 * @param {boolean} publicOnly - If true, only returns public KOL profiles
 * @returns {Promise<Array>} Array of KOL profile objects
 * @throws {Error} If database query fails
 */
export default async function getAllKols(publicOnly = false) {
    const tableName = 'ref_kols_profiles';

    // Generate cache key for this query
    const cacheKey = `admin:kol_profiles:${publicOnly ? 'public' : 'all'}`;

    try {
        // Check if we have cached results
        try {
            const cachedResults = await redis.get(cacheKey);
            if (cachedResults) {
                const parsed = JSON.parse(cachedResults);
                return parsed;
            }
        } catch (cacheError) {
            console.error("Cache read error:", cacheError);
        }

        // First set the isolation level to ensure we get the latest data
        await executeWithRetry('SET TRANSACTION ISOLATION LEVEL READ COMMITTED', []);

        // Build the query with conditional WHERE clause
        let queryText = `SELECT * FROM ${tableName}`;
        const queryParams = [];

        if (publicOnly) {
            queryText += ' WHERE is_public = $1';
            queryParams.push(true);
        }

        queryText += ' ORDER BY label ASC';

        // Execute the main query
        const result = await executeWithRetry(queryText, queryParams);

        // Cache the results
        try {
            await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(result.rows));
        } catch (cacheError) {
            console.error("Cache write error:", cacheError);
        }

        return result.rows;
    } catch (error) {
        console.error('Error fetching KOL profiles:', {
            message: error.message,
            code: error.code,
            publicOnly,
            stack: error.stack
        });
        throw error;
    }
}
