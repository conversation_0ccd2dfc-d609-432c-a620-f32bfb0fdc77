"use server";

import redis from "@/utils/redis.server";
import { query } from "@/utils/postgres";
import DOMPurify from "isomorphic-dompurify";
import getAllKols from "./getAllkols"

const CACHE_TTL = 600; // 10 min cache for KOL profiles

/**
 * Executes a database query with retry logic
 * @param {string} queryText - SQL query to execute
 * @param {Array} params - Query parameters
 * @param {number} maxRetries - Maximum number of retry attempts
 * @returns {Promise} Query result
 */
async function executeWithRetry(queryText, params = [], maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await query(queryText, params);
            return result;
        } catch (error) {
            lastError = error;
            console.error(`Query attempt ${attempt} failed:`, error.message);

            // Don't retry on the last attempt
            if (attempt === maxRetries) {
                break;
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.pow(2, attempt) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw lastError;
}

/**
 * Adds a new KOL profile to the database using fn_insert_kol_profile function
 * @param {Object} kolData - KOL data to add
 * @returns {Promise<Object>} Added KOL data
 * @throws {Error} If insertion fails or validation fails
 */
export default async function addKol(kolData) {
    if (!kolData || typeof kolData !== 'object') {
        throw new Error("Valid KOL data is required");
    }

    // Extract and validate the data
    const {
        wallet,
        label,
        type = 'sol',  // Default type
        avatar,
        is_public = false,  // Default to private
        socials,
        ...otherFields
    } = kolData;

    // Validate required fields
    if (!wallet) {
        throw new Error("KOL wallet address is required");
    }

    if (!label || typeof label !== 'string' || label.trim() === '') {
        throw new Error("Valid KOL label is required");
    }

    try {
        // Sanitize inputs
        const sanitizedWallet = DOMPurify.sanitize(wallet.trim());
        const sanitizedLabel = DOMPurify.sanitize(label.trim());
        const sanitizedType = type ? DOMPurify.sanitize(type.trim()) : 'sol';
        const sanitizedAvatar = avatar ? DOMPurify.sanitize(avatar.trim()) : null;

        // Handle socials data - ensure it's properly formatted as JSON string for PostgreSQL
        let sanitizedSocials = null;
        if (socials) {
            try {
                // If it's a string, parse it first to validate
                const socialsData = typeof socials === 'string' ? JSON.parse(socials) : socials;
                // Then stringify it again for PostgreSQL jsonb parameter
                sanitizedSocials = JSON.stringify(socialsData);
            } catch (error) {
                console.error('Error processing socials data:', error);
                throw new Error('Invalid socials data format');
            }
        }

        const isPublic = Boolean(is_public);

        // Call the database function fn_insert_kol_profile
        const insertQuery = `
            SELECT fn_insert_kol_profile($1, $2, $3, $4, $5, $6)
        `;

        await executeWithRetry(insertQuery, [
            sanitizedWallet,     // p_wallet
            sanitizedLabel,      // p_label
            sanitizedType,       // p_type
            sanitizedAvatar,     // p_avatar
            sanitizedSocials,    // p_socials
            isPublic            // p_is_public
        ]);

        // Fetch the newly created KOL data
        const selectQuery = `
            SELECT 
                wallet,
                label,
                type,
                avatar,
                socials,
                is_public,
                created_at,
                updated_at
            FROM ref_kols_profiles 
            WHERE wallet = $1
        `;

        const selectResult = await executeWithRetry(selectQuery, [sanitizedWallet]);

        if (selectResult.rows.length === 0) {
            throw new Error(`KOL with wallet ${sanitizedWallet} not found after insertion`);
        }

        updateKolFeedSubscription(sanitizedWallet);

        const newKol = selectResult.rows[0];

        // Invalidate related caches
        try {
            const cacheKeys = [
                'admin:kol_profiles:all',
                'admin:kol_profiles:public',
                `admin:kol_profile:${sanitizedWallet}`
            ];

            await Promise.all(
                cacheKeys.map(key => redis.del(key).catch(err =>
                    console.warn(`Failed to delete cache key ${key}:`, err)
                ))
            );
        } catch (cacheError) {
            console.warn('Failed to invalidate some cache keys:', cacheError);
            // Don't throw - cache invalidation failure shouldn't fail the operation
        }

        return {
            success: true,
            data: newKol
        };

    } catch (error) {
        console.error(`Error adding KOL profile:`, {
            message: error.message,
            code: error.code,
            wallet: wallet,
            stack: error.stack
        });
        throw error;
    }
}

async function updateKolFeedSubscription(newKolAddress) {
    try {
        const baseURL = process.env.NEXT_PUBLIC_STALKCHAIN_CENTRAL_API_URL;
        const apiURL = `${baseURL}/feed/admin/addKol`;
        // const allKols = await getAllKols();
        
        // // Extract wallet addresses from remaining KOLs
        // const updatedList = allKols.map(kol => kol.wallet).filter(address => address);
        // updatedList.push(newKolAddress)
        await fetch(apiURL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'Api-Key': `${process.env.KOL_FEED_API_KEY}` },
            body: JSON.stringify({ walletAddresses: [newKolAddress] })
        });
    } catch (error) {
        console.error(`Failed to updateKolFeedSubscription: `, error?.message || error);
    }
}