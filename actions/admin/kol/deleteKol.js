import redis from "@/utils/redis.server";
import { query, getClient } from "@/utils/postgres";
import getAllKols from "./getAllkols"
import DOMPurify from "isomorphic-dompurify";

/**
 * Executes a database query with retry logic
 * @param {string} queryText - SQL query to execute
 * @param {Array} params - Query parameters
 * @param {number} maxRetries - Maximum number of retry attempts
 * @returns {Promise} Query result
 */
async function executeWithRetry(queryText, params = [], maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await query(queryText, params);
            return result;
        } catch (error) {
            lastError = error;
            console.error(`Query attempt ${attempt} failed:`, error.message);

            // Don't retry on the last attempt
            if (attempt === maxRetries) {
                break;
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.pow(2, attempt) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw lastError;
}

/**
 * Delete KOL profile(s) by wallet address(es)
 * Handles both single wallet and multiple wallets (combined KOLs)
 * @param {string|Array|Object} walletData - The wallet address(es) or KOL object to delete
 * @returns {Promise<Object>} Returns deletion summary
 * @throws {Error} If deletion fails or wallet address is invalid
 */
export default async function deleteKol(walletData) {
    console.log('deleteKol called with:', { 
        type: typeof walletData, 
        isArray: Array.isArray(walletData),
        data: walletData 
    });

    if (!walletData) {
        throw new Error("Valid wallet data is required for deletion");
    }

    // Determine which wallets to delete
    let walletsToDelete = [];
    
    if (typeof walletData === 'string') {
        // Single wallet address (legacy support)
        console.log('Processing single wallet address:', walletData);
        walletsToDelete = [walletData];
    } else if (Array.isArray(walletData)) {
        // Array of wallet addresses
        console.log('Processing array of wallet addresses:', walletData);
        walletsToDelete = walletData.filter(Boolean);
    } else if (walletData && typeof walletData === 'object') {
        // KOL object with wallets array
        console.log('Processing KOL object:', { 
            hasWallets: !!walletData.wallets, 
            walletsLength: walletData.wallets?.length,
            hasSingleWallet: !!walletData.wallet,
            label: walletData.label 
        });

        if (walletData.wallets && Array.isArray(walletData.wallets) && walletData.wallets.length > 0) {
            // KOL with multiple wallets array
            walletsToDelete = walletData.wallets.map(w => w.wallet || w).filter(Boolean);
            console.log('Extracted wallets from wallets array:', walletsToDelete);
        } else if (walletData.wallet) {
            // KOL with single wallet property
            walletsToDelete = [walletData.wallet];
            console.log('Using single wallet property:', walletData.wallet);
        } else {
            console.error('No valid wallet addresses found in KOL data:', walletData);
            throw new Error("No valid wallet addresses found in KOL data");
        }
    } else {
        throw new Error("Invalid wallet data format");
    }

    if (walletsToDelete.length === 0) {
        console.error('No valid wallet addresses found for deletion');
        throw new Error("No valid wallet addresses found for deletion");
    }

    console.log('Final wallets to delete:', walletsToDelete);

    try {
        // Sanitize wallet addresses
        const sanitizedWallets = walletsToDelete.map((wallet, index) => {
            if (typeof wallet !== 'string' || wallet.trim() === '') {
                console.error(`Invalid wallet address at index ${index}:`, wallet);
                throw new Error(`Invalid wallet address: ${wallet}`);
            }
            const sanitized = DOMPurify.sanitize(wallet.trim());
            console.log(`Sanitized wallet ${index}:`, sanitized);
            return sanitized;
        });

        const deleteResults = [];
        const errors = [];

        console.log(`Starting deletion of ${sanitizedWallets.length} wallet(s)`);

        // Delete each wallet's KOL profile
        for (const walletAddress of sanitizedWallets) {
            try {
                console.log(`Attempting to delete wallet: ${walletAddress}`);
                
                // Call the database function fn_delete_kol_profile for this wallet
                const result = await executeWithRetry(
                    `SELECT fn_delete_kol_profile($1) as deleted`,
                    [walletAddress]
                );

                console.log(`Database function result for ${walletAddress}:`, result.rows);

                deleteResults.push({
                    wallet: walletAddress,
                    success: true
                });

                console.log(`Successfully deleted KOL profile for wallet: ${walletAddress}`);
            } catch (deleteError) {
                console.error(`Error deleting wallet ${walletAddress}:`, deleteError);
                errors.push({
                    wallet: walletAddress,
                    error: deleteError.message
                });
            }
        }

        console.log('Deletion results:', { 
            successful: deleteResults.length, 
            failed: errors.length,
            results: deleteResults,
            errors: errors 
        });

        // If all deletions failed, throw an error
        if (deleteResults.length === 0) {
            const errorMessage = `All wallet deletions failed: ${errors.map(e => `${e.wallet}: ${e.error}`).join(', ')}`;
            console.error(errorMessage);
            throw new Error(errorMessage);
        }

        // Update KOL feed subscription
        try {
            console.log('Updating KOL feed subscription with deleted wallets:', sanitizedWallets);
            await updateKolFeedSubscription(sanitizedWallets);
        } catch (feedError) {
            console.warn('Failed to update KOL feed subscription:', feedError);
            // Don't throw - feed update failure shouldn't fail the deletion
        }

        // Invalidate related caches
        try {
            const cacheKeys = [
                'admin:kol_profiles:all',
                'admin:kol_profiles:public',
                ...sanitizedWallets.map(w => `admin:kol_profile:${w}`)
            ];

            await Promise.all(
                cacheKeys.map(key => redis.del(key).catch(err =>
                    console.warn(`Failed to delete cache key ${key}:`, err)
                ))
            );
        } catch (cacheError) {
            console.warn('Failed to invalidate some cache keys:', cacheError);
            // Don't throw - cache invalidation failure shouldn't fail the operation
        }

        const summary = {
            totalWallets: sanitizedWallets.length,
            successfulDeletions: deleteResults.length,
            failedDeletions: errors.length,
            deletedWallets: deleteResults.map(r => r.wallet),
            errors: errors.length > 0 ? errors : undefined
        };
        return {
            success: true,
            summary: summary
        };

    } catch (error) {
        console.error(`Error deleting KOL profile(s):`, {
            message: error.message,
            code: error.code,
            wallets: walletsToDelete,
            stack: error.stack
        });
        throw error;
    }
}

async function updateKolFeedSubscription(wallets) {
    try {
        // Get all remaining KOLs and extract wallet addresses
        // const allKols = await getAllKols();

        // Extract wallet addresses from remaining KOLs
        // const updatedList = allKols.map(kol => kol.wallet).filter(address => address);
        const baseURL = process.env.NEXT_PUBLIC_STALKCHAIN_CENTRAL_API_URL;
        const apiURL = `${baseURL}/feed/admin/removeKol`;
        await fetch(apiURL, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'Api-Key': `${process.env.KOL_FEED_API_KEY}` },
            body: JSON.stringify({ updatedList: wallets })
        });
    } catch (error) {
        console.error(`Failed to updateKolFeedSubscription: `, error?.message || error);
    }
}
