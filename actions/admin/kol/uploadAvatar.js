"use server";

import { S3Client, PutObjectCommand } from "@aws-sdk/client-s3";
import DOMPurify from "isomorphic-dompurify";
import crypto from "crypto";

const REGION = "nyc3";
const AVATARS_PATH = "assets/images/avatars/kol/";
const ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ACL_TYPE = "public-read";
const OPTIMIZED_WIDTH = 200;
const OPTIMIZED_HEIGHT = 200;
const JPEG_QUALITY = 80;

const getS3Client = () => {
  const endpoint = process.env.DO_SPACES_ASSETS_ENDPOINT_URL;
  const accessKeyId = process.env.DO_SPACES_ASSETS_KEY;
  const secretAccessKey = process.env.DO_SPACES_ASSETS_SECRET;
  const bucketName = process.env.DO_SPACES_ASSETS_BUCKET_NAME;

  if (!endpoint || !accessKeyId || !secretAccessKey || !bucketName) {
    throw new Error("Missing required S3 configuration");
  }

  return new S3Client({
    endpoint,
    region: REGION,
    credentials: {
      accessKeyId,
      secretAccessKey,
    },
  });
};

/**
 * Optimizes image using sharp
 * Resizes to 200x200 and converts to JPEG with optimization
 * @param {Buffer} imageBuffer - Original image buffer
 * @returns {Promise<Buffer>} - Optimized image buffer
 */
const optimizeImage = async (imageBuffer) => {
  try {
    // Try to import sharp dynamically
    const sharp = await import('sharp').catch(() => null);
    
    if (!sharp) {
      console.warn('Sharp not available, returning original image buffer');
      return imageBuffer;
    }

    return await sharp.default(imageBuffer)
      .resize(OPTIMIZED_WIDTH, OPTIMIZED_HEIGHT, {
        fit: 'cover',
        position: 'center'
      })
      .jpeg({
        quality: JPEG_QUALITY,
        mozjpeg: true
      })
      .toBuffer();
  } catch (error) {
    console.error('Error optimizing image:', error);
    // Fallback to original image if optimization fails
    return imageBuffer;
  }
};

/**
 * Generates unique filename for avatar
 * @param {string} originalName - Original filename
 * @param {string} kolLabel - KOL label for naming
 * @returns {string} - Generated filename
 */
const generateAvatarFilename = (originalName, kolLabel) => {
  const timestamp = Date.now();
  const randomSuffix = crypto.randomBytes(4).toString('hex');
  const sanitizedLabel = DOMPurify.sanitize(kolLabel)
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '')
    .substring(0, 20);
  
  const extension = originalName.split('.').pop()?.toLowerCase() || 'jpg';
  const finalExtension = ['jpeg', 'jpg', 'png', 'gif', 'webp'].includes(extension) ? 'jpg' : 'jpg';
  
  return `${sanitizedLabel}-${timestamp}-${randomSuffix}.${finalExtension}`;
};

/**
 * Uploads avatar image to S3 with optimization
 * @param {FormData} formData - Form data containing the image file and KOL info
 * @returns {Promise<Object>} - Upload result with public URL
 */
export default async function uploadAvatar(formData) {
  let s3Client = null;
  let fileBuffer = null;
  
  try {
    // Extract file and metadata from FormData
    const file = formData.get('avatar');
    const kolLabel = DOMPurify.sanitize(formData.get('kolLabel') || '');
    const kolId = DOMPurify.sanitize(formData.get('kolId') || '');
    
    // Validate required fields
    if (!file) {
      throw new Error("No avatar file provided");
    }
    
    if (!kolLabel.trim()) {
      throw new Error("KOL label is required");
    }
    
    // Validate file type
    if (!ALLOWED_IMAGE_TYPES.includes(file.type)) {
      throw new Error(`Invalid file type. Allowed types: ${ALLOWED_IMAGE_TYPES.join(", ")}`);
    }
    
    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      throw new Error(`File size too large. Maximum size: ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }
    
    // Initialize S3 client
    s3Client = getS3Client();
    
    // Convert file to buffer
    fileBuffer = Buffer.from(await file.arrayBuffer());
    
    // Optimize image
    const optimizedBuffer = await optimizeImage(fileBuffer);
    
    // Generate unique filename
    const filename = generateAvatarFilename(file.name, kolLabel);
    
    // Create S3 key (path)
    const s3Key = `${AVATARS_PATH}${filename}`;
    
    // Upload file to S3
    const command = new PutObjectCommand({
      Bucket: process.env.DO_SPACES_ASSETS_BUCKET_NAME,
      Key: s3Key,
      Body: optimizedBuffer,
      ContentType: 'image/jpeg', // Always JPEG after optimization
      ACL: ACL_TYPE,
      CacheControl: "public, max-age=31536000", // 1 year cache for avatars
      Metadata: {
        originalName: file.name,
        kolLabel: kolLabel,
        kolId: kolId,
        uploadedAt: new Date().toISOString(),
        optimized: 'true',
        dimensions: `${OPTIMIZED_WIDTH}x${OPTIMIZED_HEIGHT}`,
      },
    });
    
    await s3Client.send(command);
    
    // Generate public URL
    const bucketName = process.env.DO_SPACES_ASSETS_BUCKET_NAME;
    const publicUrl = `https://${bucketName}.nyc3.cdn.digitaloceanspaces.com/${s3Key}`;
    
    return {
      success: true,
      data: {
        avatarUrl: publicUrl,
        filename: filename,
        s3Key: s3Key,
        fileSize: optimizedBuffer.length,
        originalSize: file.size,
        contentType: 'image/jpeg',
        dimensions: {
          width: OPTIMIZED_WIDTH,
          height: OPTIMIZED_HEIGHT
        },
        metadata: {
          kolLabel: kolLabel,
          kolId: kolId,
          uploadedAt: new Date().toISOString()
        }
      },
    };
  } catch (error) {
    console.error("Error in uploadAvatar:", error);
    throw new Error(`Failed to upload avatar: ${error.message}`);
  } finally {
    // Clean up
    s3Client = null;
    fileBuffer = null;
  }
} 