"use server";

import redis from "@/utils/redis.server";
import { query } from "@/utils/postgres";
import DOMPurify from "isomorphic-dompurify";
import addKol from "./addKol";

const CACHE_TTL = 600; // 10 min cache for KOL profiles

/**
 * Executes a database query with retry logic
 * @param {string} queryText - SQL query to execute
 * @param {Array} params - Query parameters
 * @param {number} maxRetries - Maximum number of retry attempts
 * @returns {Promise} Query result
 */
async function executeWithRetry(queryText, params = [], maxRetries = 3) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const result = await query(queryText, params);
            return result;
        } catch (error) {
            lastError = error;
            console.error(`Query attempt ${attempt} failed:`, error.message);

            // Don't retry on the last attempt
            if (attempt === maxRetries) {
                break;
            }

            // Wait before retrying (exponential backoff)
            const delay = Math.pow(2, attempt) * 1000;
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    throw lastError;
}

/**
 * Gets existing wallet addresses from the database
 * @param {Array} walletAddresses - Array of wallet addresses to check
 * @returns {Promise<Array>} Array of existing wallet addresses
 */
async function getExistingWallets(walletAddresses) {
    try {
        if (!walletAddresses || walletAddresses.length === 0) {
            return [];
        }

        const query = `
            SELECT wallet 
            FROM ref_kols_profiles 
            WHERE wallet = ANY($1)
        `;
        
        const result = await executeWithRetry(query, [walletAddresses]);
        return result.rows.map(row => row.wallet);
    } catch (error) {
        console.error('Error fetching existing wallets:', error);
        return [];
    }
}

/**
 * Updates KOL profile(s) in the database using fn_update_kol_profile function
 * Handles both single wallet and multiple wallets (combined KOLs)
 * Also creates new KOL records for newly added wallets
 * @param {Object} kolData - KOL data to update
 * @returns {Promise<Object>} Updated KOL data
 * @throws {Error} If update fails or validation fails
 */
export default async function updateKol(kolData) {
    if (!kolData || typeof kolData !== 'object') {
        throw new Error("Valid KOL data is required");
    }

    // Extract and sanitize the data
    const {
        wallet,      // Single wallet (legacy support)
        wallets,     // Multiple wallets array (frontend combined KOLs)
        label,
        type,
        avatar,
        is_public,
        socials,
        ...otherFields
    } = kolData;

    // Determine which wallets to process
    let walletsToProcess = [];
    
    if (wallets && Array.isArray(wallets) && wallets.length > 0) {
        // Frontend sends combined KOL with multiple wallets
        walletsToProcess = wallets.map(w => w.wallet || w).filter(Boolean);
    } else if (wallet) {
        // Legacy single wallet support
        walletsToProcess = [wallet];
    } else {
        throw new Error("At least one wallet address is required for update");
    }

    if (walletsToProcess.length === 0) {
        throw new Error("No valid wallet addresses found");
    }

    // Check which wallets already exist in the database (regardless of label)
    const existingWallets = await getExistingWallets(walletsToProcess);

    const newWallets = walletsToProcess.filter(walletAddr => 
        !existingWallets.includes(walletAddr)
    );
    const existingWalletsToUpdate = walletsToProcess.filter(walletAddr => 
        existingWallets.includes(walletAddr)
    );

    try {
        // Sanitize inputs (same for all wallets)
        const sanitizedLabel = label ? DOMPurify.sanitize(label.trim()) : null;
        const sanitizedType = type ? DOMPurify.sanitize(type.trim()) : null;
        const sanitizedAvatar = avatar ? DOMPurify.sanitize(avatar.trim()) : null;
        
        // Handle socials data - ensure it's properly formatted as JSON string for PostgreSQL
        let sanitizedSocials = null;
        if (socials) {
            try {
                // If it's a string, parse it first to validate
                const socialsData = typeof socials === 'string' ? JSON.parse(socials) : socials;
                // Then stringify it again for PostgreSQL jsonb parameter
                sanitizedSocials = JSON.stringify(socialsData);
            } catch (error) {
                console.error('Error processing socials data:', error);
                throw new Error('Invalid socials data format');
            }
        }
        
        const isPublic = is_public !== undefined ? Boolean(is_public) : null;

        const updateResults = [];
        const addResults = [];
        const errors = [];

        // First, create new KOL records for new wallets
        for (const newWalletAddress of newWallets) {
            try {
                const addResult = await addKol({
                    wallet: newWalletAddress,
                    label: sanitizedLabel,
                    type: sanitizedType || 'sol',
                    avatar: sanitizedAvatar,
                    is_public: isPublic !== null ? isPublic : false,
                    socials: sanitizedSocials ? JSON.parse(sanitizedSocials) : null
                });

                if (addResult.success) {
                    addResults.push({
                        wallet: newWalletAddress,
                        success: true,
                        action: 'created'
                    });
                } else {
                    throw new Error('Failed to create new KOL record');
                }
            } catch (addError) {
                console.error(`Error creating new KOL for wallet ${newWalletAddress}:`, addError.message);
                errors.push({
                    wallet: newWalletAddress,
                    error: addError.message,
                    action: 'create'
                });
            }
        }

        // Then, update existing wallet records
        for (const existingWallet of existingWalletsToUpdate) {
            try {
                const sanitizedWallet = DOMPurify.sanitize(existingWallet.trim());
                
                // Call the database function fn_update_kol_profile for this wallet
                const updateQuery = `
                    SELECT fn_update_kol_profile($1, $2, $3, $4, $5, $6)
                `;

                await executeWithRetry(updateQuery, [
                    sanitizedWallet,     // p_wallet
                    sanitizedLabel,      // p_label
                    sanitizedType,       // p_type
                    sanitizedAvatar,     // p_avatar
                    sanitizedSocials,    // p_socials
                    isPublic            // p_is_public
                ]);

                updateResults.push({
                    wallet: sanitizedWallet,
                    success: true,
                    action: 'updated'
                });

            } catch (updateError) {
                console.error(`Error updating wallet ${existingWallet}:`, updateError.message);
                errors.push({
                    wallet: existingWallet,
                    error: updateError.message,
                    action: 'update'
                });
            }
        }

        // If all operations failed, throw an error
        const totalSuccessful = updateResults.length + addResults.length;
        if (totalSuccessful === 0) {
            throw new Error(`All operations failed: ${errors.map(e => `${e.wallet} (${e.action}): ${e.error}`).join(', ')}`);
        }

        // Fetch updated KOL data - get the first successful wallet as representative
        const primaryWallet = updateResults.length > 0 ? updateResults[0].wallet : addResults[0].wallet;
        const selectQuery = `
            SELECT 
                wallet,
                label,
                type,
                avatar,
                socials,
                is_public,
                created_at,
                updated_at
            FROM ref_kols_profiles 
            WHERE wallet = $1
        `;

        const selectResult = await executeWithRetry(selectQuery, [primaryWallet]);

        if (selectResult.rows.length === 0) {
            throw new Error(`Primary wallet ${primaryWallet} not found after operations`);
        }

        const representativeKol = selectResult.rows[0];

        // Fetch all wallets for this KOL to return complete data
        const allWalletsQuery = `
            SELECT 
                wallet,
                created_at,
                updated_at
            FROM ref_kols_profiles 
            WHERE wallet = ANY($1)
            ORDER BY created_at ASC
        `;

        const allWalletsResult = await executeWithRetry(allWalletsQuery, [walletsToProcess]);
        
        representativeKol.wallets = allWalletsResult.rows.map(row => ({
            wallet: row.wallet,
            created_at: row.created_at,
            updated_at: row.updated_at
        }));

        // Invalidate related caches for all processed wallets
        try {
            const cacheKeys = [
                'admin:kol_profiles:all',
                'admin:kol_profiles:public',
                ...walletsToProcess.map(w => `admin:kol_profile:${w}`)
            ];

            await Promise.all(
                cacheKeys.map(key => redis.del(key).catch(err =>
                    console.warn(`Failed to delete cache key ${key}:`, err)
                ))
            );
        } catch (cacheError) {
            console.warn('Failed to invalidate some cache keys:', cacheError);
            // Don't throw - cache invalidation failure shouldn't fail the operation
        }

        return {
            success: true,
            data: representativeKol,
            summary: {
                totalWallets: walletsToProcess.length,
                newWallets: addResults.length,
                updatedWallets: updateResults.length,
                successfulOperations: totalSuccessful,
                failedOperations: errors.length,
                operations: [...updateResults, ...addResults],
                errors: errors.length > 0 ? errors : undefined
            }
        };

    } catch (error) {
        console.error(`Error processing KOL profile(s):`, {
            message: error.message,
            code: error.code,
            wallets: walletsToProcess,
            stack: error.stack
        });
        throw error;
    }
}