"use server";

/**
 * Send a message to users via the messages API
 * @param {Object} messageData - The message data
 * @param {string} messageData.title - Message title (required)
 * @param {string} messageData.message - Message content (required)
 * @param {string} [messageData.shortDescription] - Optional short description
 * @param {boolean} [messageData.isGlobal=true] - Whether to send to all users or specific user
 * @param {string} [messageData.userId] - User ID for non-global messages
 * @param {string} [messageData.pagePath] - Optional page path for redirect action
 * @returns {Promise<{success: boolean, data?: any, error?: string}>}
 * 
 * @example
 * // Send a global message
 * const result = await sendMessage({
 *   title: "System Maintenance",
 *   message: "We'll be performing maintenance tonight.\\nExpected downtime: 2 hours",
 *   isGlobal: true
 * });
 * 
 * @example
 * // Send a personal message with redirect action
 * const result = await sendMessage({
 *   title: "Welcome!",
 *   message: "Welcome to StalkChain!\\nClick to explore the KOL feed.",
 *   shortDescription: "New user welcome message",
 *   isGlobal: false,
 *   userId: "user123",
 *   pagePath: "/kol-feed"
 * });
 * 
 * @example
 * // Usage in a component
 * import sendMessage from "@/actions/admin/messages/sendMessage";
 * 
 * const handleWelcomeUser = async (userId) => {
 *   const result = await sendMessage({
 *     title: "Account Activated",
 *     message: "Your account has been successfully activated!",
 *     isGlobal: false,
 *     userId: userId,
 *     pagePath: "/dashboard"
 *   });
 *   
 *   if (result.success) {
 *     console.log("Welcome message sent!");
 *   } else {
 *     console.error("Failed to send message:", result.error);
 *   }
 * };
 */
export default async function sendMessage(messageData) {
  try {
    // Validate required fields
    if (!messageData.title?.trim()) {
      return {
        success: false,
        error: "Title is required"
      };
    }

    if (!messageData.message?.trim()) {
      return {
        success: false,
        error: "Message content is required"
      };
    }

    if (!messageData.isGlobal && !messageData.userId?.trim()) {
      return {
        success: false,
        error: "User ID is required for non-global messages"
      };
    }

    // Get the API key from environment variables
    const ADMIN_API_KEY = process.env.ADMIN_MESSAGESS_API_KEY;
    
    if (!ADMIN_API_KEY) {
      return {
        success: false,
        error: "Admin API key not configured"
      };
    }

    // Prepare the payload
    const payload = {
      title: messageData.title.trim(),
      message: messageData.message.trim(),
      shortDescription: messageData.shortDescription?.trim() || undefined,
      isGlobal: messageData.isGlobal ?? true,
      userId: messageData.isGlobal ? undefined : messageData.userId?.trim()
    };

    // Add actions if page path is provided
    if (messageData.pagePath?.trim()) {
      payload.actions = {
        action: 'pageRedirect',
        path: messageData.pagePath.trim()
      };
    }

    // Make the API call
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/messages/send`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': ADMIN_API_KEY
      },
      body: JSON.stringify(payload)
    });

    const result = await response.json();

    if (response.ok && result.success) {
      return {
        success: true,
        data: {
          messageId: result.messageId,
          ...payload
        }
      };
    } else {
      return {
        success: false,
        error: result.error || "Failed to send message"
      };
    }

  } catch (error) {
    console.error('Error in sendMessage action:', error);
    return {
      success: false,
      error: `Failed to send message: ${error.message}`
    };
  }
} 