"use server";

import Stripe from 'stripe';
import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import DOMPurify from "isomorphic-dompurify";

const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
  apiVersion: '2024-09-30.acacia',
});

const STALKDB = process.env.STALKCHAIN_DB || "stalkreact";
const STRIPEDB = process.env.STRIPE_DB || "stripe";
const PAYMENTDB = process.env.PAYMENT_DB || "paymentdata";

// Helper function to clean up resources
const cleanupResources = async (resources) => {
    try {
        for (const [key, value] of Object.entries(resources)) {
            if (value?.close) {
                await value.close();
            } else if (Array.isArray(value)) {
                value.length = 0;
            } else if (typeof value === 'object' && value !== null) {
                for (const objKey in value) {
                    delete value[objKey];
                }
            }
            resources[key] = null;
        }
    } catch (error) {
        console.error("Cleanup error:", error);
    }
};

// Helper function to serialize MongoDB documents
function serializeDocument(doc) {
    if (!doc) return null;

    const serialized = { ...doc };

    if (doc._id) {
        serialized._id = doc._id.toString();
    }

    Object.keys(serialized).forEach(key => {
        const value = serialized[key];
        if (value?._bsontype === 'ObjectID') {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        }
    });

    return serialized;
}

/**
 * Admin action to get user payment and refund information
 * 
 * @param {string} userId - The user ID to get refund info for
 * @returns {Promise<Object>} - User payment and refund information
 */
export default async function getUserRefundInfo(userId) {
    const resources = {
        stalkDb: null,
        stripeDb: null,
        paymentDb: null,
        userAccess: null
    };

    try {
        // Check authorization
        resources.userAccess = await getUserPermissions();
        if (!resources.userAccess["route:admin"]) {
            throw new Error("Not authorized to access user refund information");
        }

        // Sanitize input
        const sanitizedUserId = DOMPurify.sanitize(userId?.toString() || '');
        
        if (!sanitizedUserId) {
            throw new Error("User ID is required");
        }

        console.log("Getting refund info for user:", sanitizedUserId);

        // Get database connections
        resources.stalkDb = await getDb(STALKDB);
        resources.stripeDb = await getDb(STRIPEDB);
        resources.paymentDb = await getDb(PAYMENTDB);

        // Find user record
        const user = await resources.stalkDb.collection("users").findOne({
            id: sanitizedUserId
        });

        if (!user) {
            throw new Error("User not found");
        }

        // Find user's membership record
        const membership = await resources.paymentDb.collection("memberships").findOne({
            idUser: sanitizedUserId
        });

        // Get existing refund logs
        const existingRefunds = await resources.stripeDb.collection("log_refunds")
            .find({
                userId: sanitizedUserId
            })
            .sort({ createdAt: -1 })
            .toArray();

        // Find payment information
        let paymentInfo = null;
        let stripeCharges = [];

        // Check checkout_completed collection first
        const checkoutData = await resources.stripeDb.collection("checkout_completed").findOne({
            $or: [
                { userId: sanitizedUserId },
                { customer: membership?.stripeCustomerId }
            ]
        }, { sort: { createdAt: -1 } });

        if (checkoutData && checkoutData.subscription) {
            try {
                const subscription = await stripe.subscriptions.retrieve(checkoutData.subscription);
                if (subscription.latest_invoice) {
                    const invoice = await stripe.invoices.retrieve(subscription.latest_invoice);
                    if (invoice.charge) {
                        const charge = await stripe.charges.retrieve(invoice.charge);
                        stripeCharges.push(charge);
                        paymentInfo = {
                            source: 'checkout_completed',
                            data: checkoutData,
                            subscription: subscription,
                            invoice: invoice,
                            charge: charge
                        };
                    }
                }
            } catch (stripeError) {
                console.log("Could not retrieve subscription details:", stripeError.message);
            }
        }

        // Check legacy payments if no modern payment found
        if (!paymentInfo) {
            const legacyPayment = await resources.paymentDb.collection("payments_stripe").findOne({
                idUser: sanitizedUserId,
                paymentStatus: "succeeded"
            }, { sort: { createdAt: -1 } });

            if (legacyPayment && legacyPayment.paymentIntent) {
                try {
                    const paymentIntent = await stripe.paymentIntents.retrieve(legacyPayment.paymentIntent);
                    if (paymentIntent.charges && paymentIntent.charges.data.length > 0) {
                        stripeCharges = paymentIntent.charges.data;
                        paymentInfo = {
                            source: 'payments_stripe',
                            data: legacyPayment,
                            paymentIntent: paymentIntent,
                            charges: stripeCharges
                        };
                    }
                } catch (stripeError) {
                    console.log("Could not retrieve payment intent:", stripeError.message);
                }
            }
        }

        // Calculate refund eligibility
        let refundEligibility = {
            canRefund: false,
            reason: "No refundable charges found",
            totalPaid: 0,
            totalRefunded: 0,
            availableForRefund: 0,
            currency: "usd"
        };

        if (stripeCharges.length > 0) {
            const primaryCharge = stripeCharges[0];
            const totalPaid = primaryCharge.amount;
            const totalRefunded = primaryCharge.amount_refunded || 0;
            const availableForRefund = totalPaid - totalRefunded;

            refundEligibility = {
                canRefund: !user.isRefunded && !primaryCharge.refunded && availableForRefund > 0,
                reason: user.isRefunded 
                    ? "User already marked as refunded" 
                    : primaryCharge.refunded 
                        ? "Charge already fully refunded" 
                        : availableForRefund <= 0 
                            ? "No amount available for refund"
                            : "Eligible for refund",
                totalPaid: totalPaid,
                totalRefunded: totalRefunded,
                availableForRefund: availableForRefund,
                currency: primaryCharge.currency,
                chargeId: primaryCharge.id
            };
        }

        const response = {
            success: true,
            data: {
                user: serializeDocument(user),
                membership: serializeDocument(membership),
                paymentInfo: paymentInfo ? {
                    source: paymentInfo.source,
                    data: serializeDocument(paymentInfo.data),
                    hasStripeData: true
                } : null,
                refundEligibility: refundEligibility,
                existingRefunds: existingRefunds.map(refund => serializeDocument(refund)),
                stripeCharges: stripeCharges.map(charge => ({
                    id: charge.id,
                    amount: charge.amount,
                    amount_refunded: charge.amount_refunded,
                    currency: charge.currency,
                    refunded: charge.refunded,
                    status: charge.status,
                    created: charge.created,
                    receipt_url: charge.receipt_url
                }))
            },
            timestamp: new Date().toISOString()
        };

        return response;

    } catch (error) {
        console.error("Error getting user refund info:", {
            error: error.message,
            stack: error.stack,
            userId
        });

        return {
            success: false,
            error: error.message || "Failed to get user refund information",
            data: null,
            timestamp: new Date().toISOString()
        };
    } finally {
        await cleanupResources(resources);
    }
} 