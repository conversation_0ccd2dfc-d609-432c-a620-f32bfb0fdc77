"use server";

import Stripe from 'stripe';
import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import DOMPurify from "isomorphic-dompurify";
import { invalidateRefundedUsersCache } from "@/actions/admin/users/getRefundedUsers";

const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
  apiVersion: '2024-09-30.acacia',
});

const STALKDB = process.env.STALKCHAIN_DB || "stalkreact";
const STRIPEDB = process.env.STRIPE_DB || "stripe";
const PAYMENTDB = process.env.PAYMENT_DB || "paymentdata";

// Helper function to clean up resources
const cleanupResources = async (resources) => {
    try {
        for (const [key, value] of Object.entries(resources)) {
            if (value?.close) {
                await value.close();
            } else if (Array.isArray(value)) {
                value.length = 0;
            } else if (typeof value === 'object' && value !== null) {
                for (const objKey in value) {
                    delete value[objKey];
                }
            }
            resources[key] = null;
        }
    } catch (error) {
        console.error("Cleanup error:", error);
    }
};

/**
 * Admin action to manually refund a user's payment and update their account status
 * 
 * @param {Object} refundData - The refund data
 * @param {string} refundData.userId - The user ID to refund
 * @param {string} [refundData.reason] - Stripe refund reason (duplicate, fraudulent, requested_by_customer)
 * @param {number} [refundData.amount] - Amount to refund in cents (null for full refund)
 * @param {string} [refundData.refundReason] - Internal reason for the refund
 * @param {string} [refundData.adminNotes] - Additional admin notes
 * @returns {Promise<Object>} - Result of the refund operation
 */
export default async function createRefund({
    userId,
    reason = "requested_by_customer",
    amount = null,
    refundReason = "Manual refund processed by admin",
    adminNotes = ""
}) {
    const resources = {
        stalkDb: null,
        stripeDb: null,
        paymentDb: null,
        userAccess: null
    };

    try {
        // Check authorization
        resources.userAccess = await getUserPermissions();
        if (!resources.userAccess["route:admin"]) {
            throw new Error("Not authorized to process refunds");
        }

        // Sanitize inputs
        const sanitizedUserId = DOMPurify.sanitize(userId?.toString() || '');
        const sanitizedReason = DOMPurify.sanitize(reason?.toString() || 'requested_by_customer');
        const sanitizedRefundReason = DOMPurify.sanitize(refundReason?.toString() || '');
        const sanitizedAdminNotes = DOMPurify.sanitize(adminNotes?.toString() || '');
        const sanitizedAmount = amount && !isNaN(amount) ? Math.abs(parseInt(amount)) : null;

        if (!sanitizedUserId) {
            throw new Error("User ID is required");
        }

        // Valid Stripe refund reasons
        const validReasons = ['duplicate', 'fraudulent', 'requested_by_customer'];
        if (!validReasons.includes(sanitizedReason)) {
            throw new Error("Invalid refund reason");
        }

        console.log("Processing manual refund for user:", sanitizedUserId);

        // Get database connections
        resources.stalkDb = await getDb(STALKDB);
        resources.stripeDb = await getDb(STRIPEDB);
        resources.paymentDb = await getDb(PAYMENTDB);

        // Find user record
        const user = await resources.stalkDb.collection("users").findOne({
            id: sanitizedUserId
        });

        if (!user) {
            throw new Error("User not found");
        }

        // Check if user is already refunded
        if (user.isRefunded) {
            throw new Error("User has already been refunded");
        }

        console.log("Found user:", user.id, user.oldDbEmail || "no email");

        // Find user's membership record
        const membership = await resources.paymentDb.collection("memberships").findOne({
            idUser: sanitizedUserId
        });

        if (!membership) {
            throw new Error("No membership record found for user");
        }

        console.log("Found membership:", membership.stripeCustomerId, membership.stripeSubscriptionId);

        // Find the most recent successful payment for this user
        let paymentToRefund = null;
        let chargeId = null;

        // First, try to find payments in the new checkout_completed collection
        const checkoutData = await resources.stripeDb.collection("checkout_completed").findOne({
            $or: [
                { userId: sanitizedUserId },
                { customer: membership.stripeCustomerId }
            ]
        }, { sort: { createdAt: -1 } });

        if (checkoutData && checkoutData.subscription) {
            // Get the subscription to find the latest charge
            try {
                const subscription = await stripe.subscriptions.retrieve(checkoutData.subscription);
                if (subscription.latest_invoice) {
                    const invoice = await stripe.invoices.retrieve(subscription.latest_invoice);
                    if (invoice.charge) {
                        chargeId = invoice.charge;
                        paymentToRefund = checkoutData;
                        console.log("Found charge from subscription:", chargeId);
                    }
                }
            } catch (stripeError) {
                console.log("Could not retrieve subscription/invoice from Stripe:", stripeError.message);
            }
        }

        // If no charge found from subscription, try legacy payments collection
        if (!chargeId) {
            const legacyPayment = await resources.paymentDb.collection("payments_stripe").findOne({
                idUser: sanitizedUserId,
                paymentStatus: "succeeded"
            }, { sort: { createdAt: -1 } });

            if (legacyPayment && legacyPayment.paymentIntent) {
                try {
                    const paymentIntent = await stripe.paymentIntents.retrieve(legacyPayment.paymentIntent);
                    if (paymentIntent.charges && paymentIntent.charges.data.length > 0) {
                        chargeId = paymentIntent.charges.data[0].id;
                        paymentToRefund = legacyPayment;
                        console.log("Found charge from legacy payment intent:", chargeId);
                    }
                } catch (stripeError) {
                    console.log("Could not retrieve payment intent from Stripe:", stripeError.message);
                }
            }
        }

        if (!chargeId) {
            throw new Error("No refundable charges found for this user");
        }

        // Retrieve the charge to validate it
        const charge = await stripe.charges.retrieve(chargeId);
        
        if (!charge || charge.refunded) {
            throw new Error("Charge is not available for refund");
        }

        // Calculate refund amount
        const refundAmount = sanitizedAmount || charge.amount;
        
        if (refundAmount > charge.amount) {
            throw new Error("Refund amount cannot exceed the original charge amount");
        }

        console.log(`Processing refund: ${refundAmount / 100} ${charge.currency.toUpperCase()} for charge ${chargeId}`);

        // Create the refund in Stripe
        const refund = await stripe.refunds.create({
            charge: chargeId,
            amount: refundAmount,
            reason: sanitizedReason,
            metadata: {
                userId: sanitizedUserId,
                adminProcessed: 'true',
                refundReason: sanitizedRefundReason,
                adminNotes: sanitizedAdminNotes,
                processedAt: new Date().toISOString()
            }
        });

        console.log("Stripe refund created:", refund.id);

        // Update user record with refund information
        const userUpdateResult = await resources.stalkDb.collection("users").updateOne(
            { id: sanitizedUserId },
            {
                $set: {
                    isRefunded: true,
                    refundReason: sanitizedRefundReason,
                    refundAmount: refundAmount,
                    refundCurrency: charge.currency,
                    refundDate: new Date(),
                    refundId: refund.id,
                    idRole: 8, // Set to free tier
                    enabled: true, // Keep user enabled but with free role
                    updatedAt: new Date(),
                    adminNotes: sanitizedAdminNotes
                }
            }
        );

        console.log("User update result:", userUpdateResult.modifiedCount);

        // Update membership record
        const membershipUpdateResult = await resources.paymentDb.collection("memberships").updateOne(
            { idUser: sanitizedUserId },
            {
                $set: {
                    idRole: 8, // Set to free tier
                    expirationDate: new Date(), // Expire immediately
                    isRefunded: true,
                    refundDate: new Date(),
                    refundId: refund.id,
                    updatedAt: new Date()
                }
            }
        );

        console.log("Membership update result:", membershipUpdateResult.modifiedCount);

        // Log the refund event
        await resources.stripeDb.collection("log_refunds").insertOne({
            createdAt: new Date(),
            refundId: refund.id,
            chargeId: chargeId,
            userId: sanitizedUserId,
            customerId: membership.stripeCustomerId,
            subscriptionId: membership.stripeSubscriptionId,
            refundAmount: refundAmount,
            refundCurrency: charge.currency,
            reason: sanitizedReason,
            refundReason: sanitizedRefundReason,
            adminNotes: sanitizedAdminNotes,
            processedBy: 'admin_manual',
            beforeChange: {
                idRole: user.idRole,
                isRefunded: user.isRefunded || false,
                enabled: user.enabled
            },
            afterChange: {
                idRole: 8,
                isRefunded: true,
                enabled: true
            },
            stripeRefundData: refund
        });

        // Invalidate the refunded users cache
        await invalidateRefundedUsersCache();

        const response = {
            success: true,
            data: {
                refundId: refund.id,
                chargeId: chargeId,
                amount: refundAmount,
                currency: charge.currency,
                userId: sanitizedUserId,
                userEmail: user.oldDbEmail || membership.email,
                refundStatus: refund.status,
                refundReason: sanitizedRefundReason
            },
            message: `Refund of ${refundAmount / 100} ${charge.currency.toUpperCase()} processed successfully`,
            timestamp: new Date().toISOString()
        };

        console.log("Manual refund completed successfully:", refund.id);
        return response;

    } catch (error) {
        console.error("Error processing manual refund:", {
            error: error.message,
            stack: error.stack,
            userId
        });

        return {
            success: false,
            error: error.message || "Failed to process refund",
            data: null,
            timestamp: new Date().toISOString()
        };
    } finally {
        await cleanupResources(resources);
    }
} 