"use server";

import { cookies } from 'next/headers'
import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb"
import DOMPurify from "isomorphic-dompurify";
import redis from "@/utils/redis.server";

const STALKCHAIN_DB = process.env.STALKCHAIN_DB ?? "stalkreact";

// Constants
const PERMISSIONS_CACHE_PREFIX = 'permissions:';
const CACHE_TTL = 3600; // 1 hour
const BATCH_SIZE = 100;

// Helper function to validate permission data
const validatePermissionData = (permission) => {
  if (!permission?.name || typeof permission.name !== 'string') {
    throw new Error('Invalid permission name');
  }
  if (!permission?.type || !['bool', 'number'].includes(permission.type)) {
    throw new Error('Invalid permission type');
  }
  if (permission.type === 'number' && (isNaN(permission.default) || permission.default === null)) {
    throw new Error('Invalid default value for number type');
  }
  if (permission.type === 'bool' && typeof permission.default !== 'boolean') {
    throw new Error('Invalid default value for boolean type');
  }
};

// Helper function to validate role ID
const validateRoleId = (roleId) => {
  const id = parseInt(roleId);
  if (isNaN(id) || id < 1) {
    throw new Error('Invalid role ID');
  }
  return id;
};

// Helper function to clean up resources
const cleanupResources = async (resources) => {
  try {
    for (const [key, value] of Object.entries(resources)) {
      if (value?.close) {
        await value.close();
      } else if (Array.isArray(value)) {
        value.length = 0;
      } else if (typeof value === 'object' && value !== null) {
        for (const objKey in value) {
          delete value[objKey];
        }
      }
      resources[key] = null;
    }
  } catch (error) {
    console.error("Cleanup error:", error);
  }
};

// Helper function to invalidate Redis cache for a role
async function invalidateRoleCache(roleId) {
  try {
    const pattern = `${PERMISSIONS_CACHE_PREFIX}${roleId}*`;
    await redis.clearCache(pattern);
    console.info(`Invalidated Redis cache for role ${roleId}`);
  } catch (error) {
    console.error(`Error invalidating Redis cache for role ${roleId}:`, error);
  }
}

// Helper function to invalidate Redis cache for multiple roles
async function invalidateRolesCache(roleIds) {
  try {
    if (!Array.isArray(roleIds) || roleIds.length === 0) return;
    
    // Use the clearCache method with pattern matching
    const pattern = `${PERMISSIONS_CACHE_PREFIX}*(${roleIds.join('|')})*`;
    await redis.clearCache(pattern);
    console.info(`Invalidated Redis cache for roles: ${roleIds.join(', ')}`);
  } catch (error) {
    console.error(`Error invalidating Redis cache for roles:`, error);
  }
}

// Improve the serialization helper
function serializeDocument(doc) {
  if (!doc) return null;
  
  const serialized = { ...doc };
  
  // Handle ObjectId
  if (doc._id) {
    serialized._id = doc._id.toString();
  }
  
  // Handle nested objects and dates
  Object.keys(serialized).forEach(key => {
    const value = serialized[key];
    if (value?._bsontype === 'ObjectID') {
      serialized[key] = value.toString();
    } else if (value instanceof Date) {
      serialized[key] = value.toISOString();
    }
  });
  
  return serialized;
}

// Convert GET to default export function
export default async function getPermissions() {
  const resources = {
    db: null,
    permissions: [],
    userAccess: null
  };

  try {
    const cookiesStore = cookies();
    resources.userAccess = await getUserPermissions();
    if (!resources.userAccess["route:admin"]) {
      throw new Error("Not authorized");
    }

    resources.db = await getDb(STALKCHAIN_DB);

    // Create index if it doesn't exist
    await resources.db.collection("permissions").createIndex(
      { idRole: 1, name: 1 },
      { background: true }
    );

    resources.permissions = await resources.db.collection("permissions")
      .find({})
      .sort({ idRole: 1, idFeature: 1 })
      .batchSize(BATCH_SIZE)
      .toArray();

    return resources.permissions.map(p => serializeDocument(p)) || [];
  } catch (error) {
    console.error("Error in getPermissions:", error);
    return [];
  } finally {
    await cleanupResources(resources);
  }
}

// Convert POST to createPermission
export async function createPermission(permission) {
  const resources = {
    db: null,
    userAccess: null,
    roles: [],
    insertedPermission: null,
    rolePermissions: []
  };

  try {
    const cookiesStore = cookies();
    resources.userAccess = await getUserPermissions();
    if (!resources.userAccess["route:admin"]) {
      throw new Error("Not authorized");
    }

    // Validate permission data
    validatePermissionData(permission);

    resources.db = await getDb(STALKCHAIN_DB);
    
    const basePermission = {
      name: DOMPurify.sanitize(permission.name),
      type: DOMPurify.sanitize(permission.type),
      default: permission.type === 'bool' ? Boolean(permission.default) : 
               permission.type === 'number' ? Number(permission.default) : null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const baseResult = await resources.db.collection("permissions").insertOne(basePermission);
    resources.insertedPermission = serializeDocument(
      await resources.db.collection("permissions").findOne({ _id: baseResult.insertedId })
    );

    resources.roles = await resources.db.collection("roles").find({}).toArray();
    
    resources.rolePermissions = resources.roles.map(role => ({
      idRole: role.id,
      name: DOMPurify.sanitize(permission.name),
      type: DOMPurify.sanitize(permission.type),
      value: permission.type === 'bool' ? Boolean(permission.default) : 
             permission.type === 'number' ? Number(permission.default) : null,
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    let roleInsertResult = null;
    if (resources.rolePermissions.length > 0) {
      roleInsertResult = await resources.db.collection("permissions").insertMany(resources.rolePermissions);
    }

    // Invalidate Redis cache for all roles
    const roleIds = resources.roles.map(role => role.id);
    await invalidateRolesCache(roleIds);

    return {
      permission: resources.insertedPermission,
      rolePermissions: resources.rolePermissions.map(p => serializeDocument(p)),
      insertedRoleCount: roleInsertResult?.insertedCount || 0
    };
  } catch (error) {
    console.error("Error creating permission:", error);
    throw error;
  } finally {
    await cleanupResources(resources);
  }
}

// Convert PUT to setPermissions
export const setPermissions = async (update) => {
  const resources = {
    db: null,
    result: null
  };

  try {
    if (!update?.roleId || !update?.name) {
      return { success: false, error: "Missing required fields" };
    }

    const roleId = validateRoleId(update.roleId);
    resources.db = await getDb(STALKCHAIN_DB);

    // Get existing permission to validate type
    const existingPermission = await resources.db.collection("permissions").findOne({
      idRole: roleId,
      name: DOMPurify.sanitize(update.name)
    });

    // Validate and convert value based on permission type
    let sanitizedValue = update.value;
    if (existingPermission) {
      if (existingPermission.type === 'bool') {
        sanitizedValue = Boolean(update.value);
      } else if (existingPermission.type === 'number') {
        sanitizedValue = Number(update.value);
        if (isNaN(sanitizedValue)) {
          throw new Error('Invalid number value');
        }
      }
    }

    // Perform the update
    resources.result = await resources.db.collection("permissions").findOneAndUpdate(
      { 
        idRole: roleId, 
        name: DOMPurify.sanitize(update.name) 
      },
      { 
        $set: { 
          value: sanitizedValue,
          updatedAt: new Date()
        } 
      },
      { 
        upsert: true,
        returnDocument: 'after'
      }
    );

    // Invalidate Redis cache for this role
    await invalidateRoleCache(roleId);

    return {
      success: true,
      data: serializeDocument(resources.result)
    };
  } catch (error) {
    console.error("[Server setPermissions] Error:", error);
    return { success: false, error: error.message };
  } finally {
    await cleanupResources(resources);
  }
};

// Add new bulk update function
export const bulkUpdatePermissions = async (updates) => {
  const resources = {
    db: null,
    result: null,
    operations: []
  };

  try {
    if (!Array.isArray(updates) || updates.length === 0) {
      return { success: false, error: "No updates provided" };
    }

    resources.db = await getDb(STALKCHAIN_DB);

    // Validate all updates first
    const validatedUpdates = await Promise.all(updates.map(async update => {
      const roleId = validateRoleId(update.roleId);
      
      // Get existing permission to validate type
      const existingPermission = await resources.db.collection("permissions").findOne({
        idRole: roleId,
        name: DOMPurify.sanitize(update.name)
      });

      let sanitizedValue = update.value;
      if (existingPermission) {
        if (existingPermission.type === 'bool') {
          sanitizedValue = Boolean(update.value);
        } else if (existingPermission.type === 'number') {
          sanitizedValue = Number(update.value);
          if (isNaN(sanitizedValue)) {
            throw new Error(`Invalid number value for permission ${update.name}`);
          }
        }
      }

      return {
        roleId,
        name: DOMPurify.sanitize(update.name),
        value: sanitizedValue
      };
    }));

    resources.operations = validatedUpdates.map(update => ({
      updateOne: {
        filter: { 
          idRole: update.roleId, 
          name: update.name 
        },
        update: { 
          $set: { 
            value: update.value,
            updatedAt: new Date()
          } 
        },
        upsert: true
      }
    }));

    resources.result = await resources.db.collection("permissions").bulkWrite(
      resources.operations,
      { ordered: false }
    );

    // Get unique roleIds that were updated
    const uniqueRoleIds = [...new Set(validatedUpdates.map(u => u.roleId))];
    
    // Invalidate Redis cache for all affected roles
    await invalidateRolesCache(uniqueRoleIds);

    return {
      success: true,
      data: {
        matchedCount: resources.result.matchedCount,
        modifiedCount: resources.result.modifiedCount,
        upsertedCount: resources.result.upsertedCount
      }
    };
  } catch (error) {
    console.error("[Server bulkUpdatePermissions] Error:", error);
    return { success: false, error: error.message };
  } finally {
    await cleanupResources(resources);
  }
};

// Add delete permission function
export async function deletePermission(permissionName) {
  const resources = {
    db: null,
    userAccess: null,
    result: null
  };

  try {
    console.log("[deletePermission] Starting deletion for:", permissionName);
    
    const cookiesStore = cookies();
    resources.userAccess = await getUserPermissions();
    
    console.log("[deletePermission] User permissions:", resources.userAccess);
    
    if (!resources.userAccess["route:admin"]) {
      console.log("[deletePermission] Authorization failed");
      throw new Error("Not authorized");
    }

    if (!permissionName || typeof permissionName !== 'string') {
      console.log("[deletePermission] Invalid permission name:", permissionName);
      throw new Error("Invalid permission name");
    }

    resources.db = await getDb(STALKCHAIN_DB);
    
    const sanitizedName = DOMPurify.sanitize(permissionName);
    console.log("[deletePermission] Sanitized name:", sanitizedName);
    
    // Check what exists before deletion
    const existingPermissions = await resources.db.collection("permissions").find({
      name: sanitizedName
    }).toArray();
    console.log("[deletePermission] Found existing permissions:", existingPermissions.length);
    
    // Delete both base permission and all role-specific instances
    resources.result = await resources.db.collection("permissions").deleteMany({
      name: sanitizedName
    });

    console.log("[deletePermission] Delete result:", resources.result);

    // Get all roles for cache invalidation
    const roles = await resources.db.collection("roles").find({}).toArray();
    const roleIds = roles.map(role => role.id);
    
    // Invalidate Redis cache for all roles
    await invalidateRolesCache(roleIds);

    console.log("[deletePermission] Successfully deleted", resources.result.deletedCount, "permissions");

    return {
      success: true,
      deletedCount: resources.result.deletedCount
    };
  } catch (error) {
    console.error("[deletePermission] Error:", error);
    return { success: false, error: error.message };
  } finally {
    await cleanupResources(resources);
  }
}

