"use server";

import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import redis from "@/utils/redis.server";
import DOMPurify from "isomorphic-dompurify";

const STALKDB = process.env.STALKCHAIN_DB || "stalkreact";
const USERS_BY_MEMBERSHIP_CACHE_PREFIX = 'admin:users_by_membership:';
const CACHE_TTL = 3600; // 1 hour
const BATCH_SIZE = 100;

// Helper function to clean up resources
const cleanupResources = async (resources) => {
  try {
    for (const [key, value] of Object.entries(resources)) {
      if (value?.close) {
        await value.close();
      } else if (Array.isArray(value)) {
        value.length = 0;
      } else if (typeof value === 'object' && value !== null) {
        for (const objKey in value) {
          delete value[objKey];
        }
      }
      resources[key] = null;
    }
  } catch (error) {
    console.error("Cleanup error:", error);
  }
};

// Helper function to serialize MongoDB documents
function serializeDocument(doc) {
  if (!doc) return null;
  
  const serialized = { ...doc };
  
  if (doc._id) {
    serialized._id = doc._id.toString();
  }
  
  Object.keys(serialized).forEach(key => {
    const value = serialized[key];
    if (value?._bsontype === 'ObjectID') {
      serialized[key] = value.toString();
    } else if (value instanceof Date) {
      serialized[key] = value.toISOString();
    }
  });
  
  return serialized;
}

// Helper function to validate membership type (idRole)
const validateMembershipType = (membershipType) => {
  const id = parseInt(membershipType);
  if (isNaN(id) || id < 1) {
    throw new Error('Invalid membership type');
  }
  return id;
};

// Helper function to invalidate cache for membership type
export async function invalidateUsersByMembershipCache(membershipType = null) {
  try {
    if (membershipType) {
      await redis.del(`${USERS_BY_MEMBERSHIP_CACHE_PREFIX}${membershipType}`);
      console.log(`Users by membership cache invalidated for type ${membershipType}`);
    } else {
      // Clear all membership type caches
      const pattern = `${USERS_BY_MEMBERSHIP_CACHE_PREFIX}*`;
      await redis.clearCache(pattern);
      console.log('All users by membership caches invalidated');
    }
  } catch (error) {
    console.error('Error invalidating users by membership cache:', error);
  }
}

// Main function to get users by membership type
export default async function getUsersByMembershipType(membershipType) {
  const resources = {
    db: null,
    userAccess: null,
    users: []
  };

  try {
    // Sanitize and validate membership type
    const sanitizedMembershipType = DOMPurify.sanitize(membershipType?.toString().trim() || '');
    
    if (!sanitizedMembershipType) {
      return {
        success: false,
        error: "Membership type is required",
        data: []
      };
    }

    const validatedMembershipType = validateMembershipType(sanitizedMembershipType);

    // Check authorization
    resources.userAccess = await getUserPermissions();
    if (!resources.userAccess["route:admin"]) {
      throw new Error("Not authorized to access user membership data");
    }

    // Check Redis cache first
    const cacheKey = `${USERS_BY_MEMBERSHIP_CACHE_PREFIX}${validatedMembershipType}`;
    try {
      const cachedResults = await redis.get(cacheKey);
      if (cachedResults) {
        return JSON.parse(cachedResults);
      }
    } catch (cacheError) {
      console.error("Cache read error:", cacheError);
    }

    // Get database connection (only need users database)
    resources.db = await getDb(STALKDB);

    // Create index for users collection
    await resources.db.collection("users").createIndex(
      { idRole: 1 },
      { background: true }
    );

    resources.users = await resources.db.collection("users")
      .find({ idRole: validatedMembershipType })
      .sort({ createdAt: -1 })
      .batchSize(BATCH_SIZE)
      .toArray();

    // Serialize the results
    const serializedUsers = resources.users.map(user => ({
      ...serializeDocument(user),
      hasUserRecord: true,
      source: 'users'
    }));

    // Prepare response
    const response = {
      success: true,
      data: serializedUsers,
      membershipType: validatedMembershipType,
      totalResults: serializedUsers.length,
      sources: {
        users: resources.users.length,
        total: serializedUsers.length
      },
      timestamp: new Date().toISOString()
    };

    // Cache the results
    try {
      await redis.setex(cacheKey, CACHE_TTL, JSON.stringify(response));
    } catch (cacheError) {
      console.error("Cache write error:", cacheError);
    }

    return response;

  } catch (error) {
    console.error("Error fetching users by membership type:", {
      error: error.message,
      stack: error.stack,
      membershipType
    });
    
    return {
      success: false,
      error: error.message || "Failed to fetch users by membership type",
      data: [],
      timestamp: new Date().toISOString()
    };
  } finally {
    await cleanupResources(resources);
  }
}

// Function to get membership type statistics
export async function getMembershipTypeStats() {
  const resources = {
    db: null,
    userAccess: null
  };

  try {
    // Check authorization
    resources.userAccess = await getUserPermissions();
    if (!resources.userAccess["route:admin"]) {
      throw new Error("Not authorized to access membership statistics");
    }

    // Check cache first
    const cacheKey = 'admin:membership_stats';
    try {
      const cachedStats = await redis.get(cacheKey);
      if (cachedStats) {
        return JSON.parse(cachedStats);
      }
    } catch (cacheError) {
      console.error("Cache read error:", cacheError);
    }

    // Get database connection (only need users database)
    resources.db = await getDb(STALKDB);

    // Get membership statistics from users collection
    const membershipStats = await resources.db.collection("users")
      .aggregate([
        {
          $group: {
            _id: "$idRole",
            count: { $sum: 1 },
            latestCreated: { $max: "$createdAt" }
          }
        },
        {
          $sort: { _id: 1 }
        }
      ])
      .toArray();

    // Prepare response
    const response = {
      success: true,
      membershipStats: membershipStats.map(stat => serializeDocument(stat)),
      timestamp: new Date().toISOString()
    };

    // Cache the results for 30 minutes
    try {
      await redis.setex(cacheKey, 1800, JSON.stringify(response));
    } catch (cacheError) {
      console.error("Cache write error:", cacheError);
    }

    return response;

  } catch (error) {
    console.error("Error fetching membership statistics:", {
      error: error.message,
      stack: error.stack
    });
    
    return {
      success: false,
      error: error.message || "Failed to fetch membership statistics",
      data: [],
      timestamp: new Date().toISOString()
    };
  } finally {
    await cleanupResources(resources);
  }
} 