/**
 * Admin User Search Action
 * 
 * This server action provides comprehensive user search functionality for admin users.
 * It searches MongoDB collections to find users by various identifiers using the most current data.
 * 
 * WHAT IT DOES:
 * - Searches for users across MongoDB collections only (no cache to avoid stale data)
 * - Supports search by: wallet addresses, emails, user IDs, Privy IDs, and referral codes
 * - Returns deduplicated results with source attribution
 * - Caches search results for 5 minutes to improve performance (results only, not user data)
 * 
 * WHERE IT SEARCHES:
 * 1. MongoDB Collections:
 *    - `users` collection (STALKDB) - Main user records
 *    - `memberships` collection (PAYMENTDB) - Payment/subscription data
 * 
 * HOW IT WORKS:
 * 1. Validates admin permissions and sanitizes input
 * 2. Checks for cached search results first (search results, not user data)
 * 3. Searches MongoDB collections using regex patterns for current data
 * 4. Combines and deduplicates results from all database sources
 * 5. Caches the final search results for future identical queries
 * 6. Returns structured response with source attribution
 * 
 * SEARCH FIELDS:
 * - User ID (internal system identifier)
 * - Privy ID (authentication provider identifier)
 * - Wallet addresses (from linkedAccounts and memberships)
 * - Email addresses (from linkedAccounts)
 * - Referral codes (from memberships)
 * 
 * USAGE:
 * - Import and call from admin components
 * - Requires admin route permissions
 * - Minimum 2 character search query
 * - Returns success/error status with data array
 * 
 * NOTE: This function intentionally avoids searching Redis user cache to ensure
 * admins always get the most current user data from the database.
 */

"use server";

import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import redis from "@/utils/redis.server";
import DOMPurify from "isomorphic-dompurify";

const STALKDB = process.env.STALKCHAIN_DB || "stalkreact";
const PAYMENTDB = process.env.PAYMENT_DB || "paymentdata";
const SEARCH_CACHE_TTL = 300; // 5 minutes cache for search results only
const BATCH_SIZE = 50;

// Helper function to clean up resources
const cleanupResources = async (resources) => {
    try {
        for (const [key, value] of Object.entries(resources)) {
            if (value?.close) {
                await value.close();
            } else if (Array.isArray(value)) {
                value.length = 0;
            } else if (typeof value === 'object' && value !== null) {
                for (const objKey in value) {
                    delete value[objKey];
                }
            }
            resources[key] = null;
        }
    } catch (error) {
        console.error("Cleanup error:", error);
    }
};

// Helper function to serialize MongoDB documents
function serializeDocument(doc) {
    if (!doc) return null;

    const serialized = { ...doc };

    if (doc._id) {
        serialized._id = doc._id.toString();
    }

    Object.keys(serialized).forEach(key => {
        const value = serialized[key];
        if (value?._bsontype === 'ObjectID') {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        }
    });

    return serialized;
}

// Main search function
export default async function searchUsers(searchQuery) {
    const resources = {
        db: null,
        paymentDb: null,
        userAccess: null,
        searchResults: []
    };

    try {
        // Sanitize search query
        const sanitizedQuery = DOMPurify.sanitize(searchQuery?.trim() || '');

        if (!sanitizedQuery || sanitizedQuery.length < 2) {
            return {
                success: false,
                error: "Search query must be at least 2 characters long",
                data: []
            };
        }

        // Check authorization
        resources.userAccess = await getUserPermissions();
        if (!resources.userAccess["route:admin"]) {
            throw new Error("Not authorized to search users");
        }

        // Generate cache key for this search (caching search results, not user data)
        const cacheKey = `admin:user_search:${sanitizedQuery.toLowerCase()}`;

        // Check if we have cached search results
        try {
            const cachedResults = await redis.get(cacheKey);
            if (cachedResults) {
                return JSON.parse(cachedResults);
            }
        } catch (cacheError) {
            console.error("Cache read error:", cacheError);
        }

        // Search MongoDB collections for current user data
        resources.db = await getDb(STALKDB);
        resources.paymentDb = await getDb(PAYMENTDB);

        const searchRegex = new RegExp(sanitizedQuery, 'i');
        // Search in users collection
        const dbUsers = await resources.db.collection("users")
            .find({
                $or: [
                    { id: searchRegex },
                    { privyId: searchRegex },
                    { "linkedAccounts.address": searchRegex },
                    { "linkedAccounts.email": searchRegex }
                ]
            })
            .limit(BATCH_SIZE)
            .toArray();

        // Search in memberships collection for additional user data
        const memberships = await resources.paymentDb.collection("memberships")
            .find({
                $or: [
                    { idUser: searchRegex },
                    { walletAddress: searchRegex },
                    { referredBy: searchRegex },
                    { stripeCustomerId: searchRegex },
                    { stripeSubscriptionId: searchRegex }
                ]
            })
            .limit(BATCH_SIZE)
            .toArray();

        // Search in payments_stripe collection for Stripe payment data
        const stripePayments = await resources.paymentDb.collection("payments_stripe")
            .find({
                $or: [
                    { paymentIntent: searchRegex },
                    { customerId: searchRegex },
                    { subscriptionId: searchRegex },
                    { "metadata.userId": searchRegex }, // Stripe user ID in metadata
                    { idUser: searchRegex } // App user ID if present
                ]
            })
            .limit(BATCH_SIZE)
            .toArray();

        // Search in payments collection for crypto payment data
        const cryptoPayments = await resources.paymentDb.collection("payments")
            .find({
                $or: [
                    { idUser: searchRegex },
                    { walletAddress: searchRegex },
                    { customerWallet: searchRegex },
                    { transactionSignature: searchRegex },
                    { helioTransactionId: searchRegex }
                ]
            })
            .limit(BATCH_SIZE)
            .toArray();

        // Search in referral_codes collection for referral code data
        const referralCodes = await resources.paymentDb.collection("referral_codes")
            .find({
                $or: [
                    { referralCode: searchRegex },
                    { idUser: searchRegex },
                    { walletAddress: searchRegex }
                ]
            })
            .limit(BATCH_SIZE)
            .toArray();

        // Combine and deduplicate results
        const allResults = [];
        const seenIds = new Set();

        // Add database users
        dbUsers.forEach(user => {
            if (user.id && !seenIds.has(user.id)) {
                seenIds.add(user.id);
                allResults.push({
                    ...serializeDocument(user),
                    source: 'database_users'
                });
            }
        });

        // For memberships found by Stripe IDs, fetch full user details from users collection
        const stripeFoundUserIds = [];
        memberships.forEach(membership => {
            if (membership.idUser && !seenIds.has(membership.idUser)) {
                // Check if this membership was found by Stripe ID search
                const foundByStripeCustomerId = membership.stripeCustomerId &&
                    membership.stripeCustomerId.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundByStripeSubscriptionId = membership.stripeSubscriptionId &&
                    membership.stripeSubscriptionId.toLowerCase().includes(sanitizedQuery.toLowerCase());

                if (foundByStripeCustomerId || foundByStripeSubscriptionId) {
                    stripeFoundUserIds.push(membership.idUser);
                }

                seenIds.add(membership.idUser);
                allResults.push({
                    id: membership.idUser,
                    walletAddress: membership.walletAddress,
                    idRole: membership.idRole,
                    referredBy: membership.referredBy,
                    stripeCustomerId: membership.stripeCustomerId,
                    stripeSubscriptionId: membership.stripeSubscriptionId,
                    createdAt: membership.createdAt,
                    updatedAt: membership.updatedAt,
                    source: foundByStripeCustomerId || foundByStripeSubscriptionId ? 'stripe_search' : 'database_memberships'
                });
            }
        });

        // For stripe payments found, fetch full user details from users collection
        const stripePaymentUserIds = [];
        stripePayments.forEach(payment => {
            // Use idUser if available, otherwise use metadata.userId (but note it's Stripe user ID)
            const userId = payment.idUser || payment.metadata?.userId;

            if (userId && !seenIds.has(userId)) {
                // Check if this payment was found by Stripe-specific fields
                const foundByPaymentIntent = payment.paymentIntent &&
                    payment.paymentIntent.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundByCustomerId = payment.customerId &&
                    payment.customerId.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundBySubscriptionId = payment.subscriptionId &&
                    payment.subscriptionId.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundByMetadataUserId = payment.metadata?.userId &&
                    payment.metadata.userId.toLowerCase().includes(sanitizedQuery.toLowerCase());

                const foundByStripeFields = foundByPaymentIntent || foundByCustomerId ||
                    foundBySubscriptionId || foundByMetadataUserId;

                if (foundByStripeFields && payment.idUser) {
                    // Only add to fetch list if we have an app user ID
                    stripePaymentUserIds.push(payment.idUser);
                }

                seenIds.add(userId);
                allResults.push({
                    id: userId,
                    email: payment.email,
                    paymentIntent: payment.paymentIntent,
                    customerId: payment.customerId,
                    subscriptionId: payment.subscriptionId,
                    paymentStatus: payment.paymentStatus,
                    paymentAmount: payment.paymentAmount,
                    paymentCurrency: payment.paymentCurrency,
                    paymentDate: payment.paymentDate,
                    productName: payment.productName,
                    billingInterval: payment.billingInterval,
                    createdAt: payment.createdAt,
                    updatedAt: payment.updatedAt,
                    source: foundByStripeFields ? 'stripe_payments' : 'database_payments',
                    isStripeUserId: !payment.idUser && payment.metadata?.userId // Flag if this is Stripe user ID
                });
            }
        });

        // For crypto payments found, fetch full user details from users collection
        const cryptoPaymentUserIds = [];
        cryptoPayments.forEach(payment => {
            if (payment.idUser && !seenIds.has(payment.idUser)) {
                // Check if this payment was found by crypto-specific fields
                const foundByTransactionSignature = payment.transactionSignature &&
                    payment.transactionSignature.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundByCustomerWallet = payment.customerWallet &&
                    payment.customerWallet.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundByHelioTransactionId = payment.helioTransactionId &&
                    payment.helioTransactionId.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundByWalletAddress = payment.walletAddress &&
                    payment.walletAddress.toLowerCase().includes(sanitizedQuery.toLowerCase());

                const foundByCryptoFields = foundByTransactionSignature || foundByCustomerWallet ||
                    foundByHelioTransactionId || foundByWalletAddress;

                if (foundByCryptoFields) {
                    cryptoPaymentUserIds.push(payment.idUser);
                }

                seenIds.add(payment.idUser);
                allResults.push({
                    id: payment.idUser,
                    email: payment.email,
                    walletAddress: payment.walletAddress,
                    customerWallet: payment.customerWallet,
                    transactionSignature: payment.transactionSignature,
                    helioTransactionId: payment.helioTransactionId,
                    transactionStatus: payment.transactionStatus,
                    amount: payment.amount,
                    currency: payment.currency,
                    membershipDuration: payment.membershipDuration,
                    paymentDate: payment.paymentDate,
                    createdAt: payment.createdAt,
                    updatedAt: payment.updatedAt,
                    source: foundByCryptoFields ? 'crypto_payments' : 'database_crypto_payments'
                });
            }
        });

        // For referral codes found, fetch full user details from users collection
        const referralCodeUserIds = [];
        referralCodes.forEach(referral => {
            if (referral.idUser && !seenIds.has(referral.idUser)) {
                // Check if this referral was found by referral code specific fields
                const foundByReferralCode = referral.referralCode &&
                    referral.referralCode.toLowerCase().includes(sanitizedQuery.toLowerCase());
                const foundByWalletAddress = referral.walletAddress &&
                    referral.walletAddress.toLowerCase().includes(sanitizedQuery.toLowerCase());

                const foundByReferralFields = foundByReferralCode || foundByWalletAddress;

                if (foundByReferralFields) {
                    referralCodeUserIds.push(referral.idUser);
                }

                seenIds.add(referral.idUser);
                allResults.push({
                    id: referral.idUser,
                    referralCode: referral.referralCode,
                    walletAddress: referral.walletAddress,
                    rewardWallet: referral.rewardWallet,
                    commissionRate: referral.commissionRate,
                    commissionType: referral.commissionType,
                    generatedAt: referral.generatedAt,
                    source: foundByReferralFields ? 'referral_codes' : 'database_referral_codes'
                });
            }
        });

        // Fetch full user details for users found by Stripe IDs in memberships
        if (stripeFoundUserIds.length > 0) {
            const stripeUsers = await resources.db.collection("users")
                .find({ id: { $in: stripeFoundUserIds } })
                .limit(BATCH_SIZE)
                .toArray();

            // Replace membership-only records with full user details for Stripe-found users
            stripeUsers.forEach(fullUser => {
                const existingIndex = allResults.findIndex(result => result.id === fullUser.id);
                if (existingIndex !== -1) {
                    // Replace with full user data but keep the source indicator
                    allResults[existingIndex] = {
                        ...serializeDocument(fullUser),
                        source: 'stripe_search'
                    };
                }
            });
        }

        // Fetch full user details for users found by Stripe payments
        if (stripePaymentUserIds.length > 0) {
            const stripePaymentUsers = await resources.db.collection("users")
                .find({ id: { $in: stripePaymentUserIds } })
                .limit(BATCH_SIZE)
                .toArray();

            // Replace payment-only records with full user details for Stripe payment-found users
            stripePaymentUsers.forEach(fullUser => {
                const existingIndex = allResults.findIndex(result => result.id === fullUser.id);
                if (existingIndex !== -1) {
                    // Replace with full user data but keep the source indicator
                    allResults[existingIndex] = {
                        ...serializeDocument(fullUser),
                        source: 'stripe_payments'
                    };
                }
            });
        }

        // Fetch full user details for users found by crypto payments
        if (cryptoPaymentUserIds.length > 0) {
            const cryptoPaymentUsers = await resources.db.collection("users")
                .find({ id: { $in: cryptoPaymentUserIds } })
                .limit(BATCH_SIZE)
                .toArray();

            // Replace payment-only records with full user details for crypto payment-found users
            cryptoPaymentUsers.forEach(fullUser => {
                const existingIndex = allResults.findIndex(result => result.id === fullUser.id);
                if (existingIndex !== -1) {
                    // Replace with full user data but keep the source indicator
                    allResults[existingIndex] = {
                        ...serializeDocument(fullUser),
                        source: 'crypto_payments'
                    };
                }
            });
        }

        // Fetch full user details for users found by referral codes
        if (referralCodeUserIds.length > 0) {
            const referralCodeUsers = await resources.db.collection("users")
                .find({ id: { $in: referralCodeUserIds } })
                .limit(BATCH_SIZE)
                .toArray();

            // Replace referral-only records with full user details for referral code-found users
            referralCodeUsers.forEach(fullUser => {
                const existingIndex = allResults.findIndex(result => result.id === fullUser.id);
                if (existingIndex !== -1) {
                    // Replace with full user data but keep the source indicator
                    allResults[existingIndex] = {
                        ...serializeDocument(fullUser),
                        source: 'referral_codes'
                    };
                }
            });
        }

        // Prepare response
        const response = {
            success: true,
            data: allResults,
            searchQuery: sanitizedQuery,
            totalResults: allResults.length,
            sources: {
                database_users: dbUsers.length,
                database_memberships: memberships.filter(m => {
                    const foundByStripe = (m.stripeCustomerId && m.stripeCustomerId.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (m.stripeSubscriptionId && m.stripeSubscriptionId.toLowerCase().includes(sanitizedQuery.toLowerCase()));
                    return !foundByStripe;
                }).length,
                stripe_search: stripeFoundUserIds.length,
                stripe_payments: stripePayments.filter(p => {
                    const foundByStripeFields = (p.paymentIntent && p.paymentIntent.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (p.customerId && p.customerId.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (p.subscriptionId && p.subscriptionId.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (p.metadata?.userId && p.metadata.userId.toLowerCase().includes(sanitizedQuery.toLowerCase()));
                    return foundByStripeFields;
                }).length,
                crypto_payments: cryptoPayments.filter(p => {
                    const foundByCryptoFields = (p.transactionSignature && p.transactionSignature.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (p.customerWallet && p.customerWallet.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (p.helioTransactionId && p.helioTransactionId.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (p.walletAddress && p.walletAddress.toLowerCase().includes(sanitizedQuery.toLowerCase()));
                    return foundByCryptoFields;
                }).length,
                referral_codes: referralCodes.filter(r => {
                    const foundByReferralFields = (r.referralCode && r.referralCode.toLowerCase().includes(sanitizedQuery.toLowerCase())) ||
                        (r.walletAddress && r.walletAddress.toLowerCase().includes(sanitizedQuery.toLowerCase()));
                    return foundByReferralFields;
                }).length
            },
            timestamp: new Date().toISOString()
        };

        // Cache the search results (not the user data itself)
        try {
            // Only cache if there are actual search results
            if (response.totalResults > 0) {
                await redis.setex(cacheKey, SEARCH_CACHE_TTL, JSON.stringify(response));
            }
        } catch (cacheError) {
            console.error("Cache write error:", cacheError);
        }

        return response;

    } catch (error) {
        console.error("Error searching users:", {
            error: error.message,
            stack: error.stack,
            searchQuery
        });

        return {
            success: false,
            error: error.message || "Failed to search users",
            data: [],
            timestamp: new Date().toISOString()
        };
    } finally {
        await cleanupResources(resources);
    }
} 