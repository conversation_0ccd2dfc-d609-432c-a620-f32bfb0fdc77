"use server";

import { getUserPermissions } from "@/utils/authState";
import DOMPurify from "isomorphic-dompurify";

// Main function to get user referrals
export default async function getUserReferrals(userId) {
    try {
        // Check authorization
        const userAccess = await getUserPermissions();
        if (!userAccess["route:admin"]) {
            throw new Error("Not authorized to access user referrals data");
        }

        // Sanitize input
        const sanitizedUserId = DOMPurify.sanitize(userId?.toString() || "");
        
        if (!sanitizedUserId) {
            return {
                success: false,
                error: "User ID is required",
                data: [],
                source: 'promotekit',
                timestamp: new Date().toISOString()
            };
        }

        // Return message indicating referrals are handled by PromoteKit
        return {
            success: true,
            data: [],
            totalResults: 0,
            statistics: {
                totalReferrals: 0,
                totalReferralAmount: 0,
                totalCommissionEarned: 0,
                conversionRate: 0
            },
            message: "Referral tracking is now handled by PromoteKit. Please use the PromoteKit dashboard for referral analytics.",
            source: 'promotekit',
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error("Error in getUserReferrals:", error.message);

        return {
            success: false,
            error: error.message || "Failed to fetch user referrals",
            data: [],
            source: 'promotekit',
            timestamp: new Date().toISOString()
        };
    }
} 