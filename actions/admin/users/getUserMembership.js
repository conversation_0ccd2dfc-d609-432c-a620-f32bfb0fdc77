/**
 * Admin User Membership Data Retrieval Action
 * 
 * This server action retrieves user membership data by user ID for admin users.
 * It searches the MongoDB payment database to find membership information.
 * 
 * WHAT IT DOES:
 * - Retrieves membership data from MongoDB payment database by user ID
 * - Returns complete membership information with proper serialization
 * - Provides admin-only access with proper authorization
 * 
 * WHERE IT SEARCHES:
 * - `memberships` collection (PAYMENTDB) - Primary membership records
 * 
 * HOW IT WORKS:
 * 1. Validates admin permissions and sanitizes user ID
 * 2. Searches MongoDB memberships collection by exact user ID match
 * 3. Serializes MongoDB documents for safe JSON transport
 * 4. Returns structured response with membership data
 * 
 * USAGE:
 * - Import and call from admin components
 * - Requires admin route permissions
 * - Pass user ID as parameter
 * - Returns success/error status with membership data
 */

"use server";

import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import DOMPurify from "isomorphic-dompurify";

const PAYMENTDB = process.env.PAYMENT_DB || "paymentdata";

// Helper function to clean up resources
const cleanupResources = async (resources) => {
    try {
        for (const [key, value] of Object.entries(resources)) {
            if (value?.close) {
                await value.close();
            } else if (Array.isArray(value)) {
                value.length = 0;
            } else if (typeof value === 'object' && value !== null) {
                for (const objKey in value) {
                    delete value[objKey];
                }
            }
            resources[key] = null;
        }
    } catch (error) {
        console.error("Cleanup error:", error);
    }
};

// Helper function to serialize MongoDB documents
function serializeDocument(doc) {
    if (!doc) return null;

    const serialized = { ...doc };

    if (doc._id) {
        serialized._id = doc._id.toString();
    }

    Object.keys(serialized).forEach(key => {
        const value = serialized[key];
        if (value?._bsontype === 'ObjectID') {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        }
    });

    return serialized;
}

// Main membership retrieval function
export default async function getUserMembership(userId) {
    const resources = {
        paymentDb: null,
        userAccess: null
    };

    try {
        // Sanitize user ID
        const sanitizedUserId = DOMPurify.sanitize(userId?.trim() || '');
        if (!sanitizedUserId) {
            return {
                success: false,
                error: "User ID is required",
                data: null
            };
        }

        // Check authorization
        resources.userAccess = await getUserPermissions();
        if (!resources.userAccess["route:admin"]) {
            throw new Error("Not authorized to access membership data");
        }

        // Search MongoDB for membership data
        resources.paymentDb = await getDb(PAYMENTDB);
        
        // Check if collection exists and has data
        const collections = await resources.paymentDb.listCollections().toArray();
        
        const membershipCount = await resources.paymentDb.collection("memberships").countDocuments();
        
        // Try to find a few sample documents to see the structure
        const membership = await resources.paymentDb.collection("memberships")
          .findOne({ idUser: sanitizedUserId });
        
        if (!membership) {
          // Try alternative searches to see if the user ID format is different
          // Try case-insensitive search
          return {
            success: false,
            error: "No membership found for this user",
            data: null
          };
        }

        // Serialize the membership document
        const serializedMembership = serializeDocument(membership);

        return {
            success: true,
            data: serializedMembership,
            userId: sanitizedUserId,
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error("Error retrieving membership by user ID:", {
            error: error.message,
            stack: error.stack,
            userId
        });

        return {
            success: false,
            error: error.message || "Failed to retrieve membership data",
            data: null,
            timestamp: new Date().toISOString()
        };
    } finally {
        await cleanupResources(resources);
    }
} 