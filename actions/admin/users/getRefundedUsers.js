"use server";

import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import redis from "@/utils/redis.server";
import DOMPurify from "isomorphic-dompurify";

const STALKDB = process.env.STALKCHAIN_DB || "stalkreact";
const STRIPEDB = process.env.STRIPE_DB || "stripe";
const REFUNDED_USERS_CACHE_KEY = 'admin:refunded_users';
const CACHE_TTL = 3600; // 1 hour
const BATCH_SIZE = 100;

// Helper function to clean up resources
const cleanupResources = async (resources) => {
    try {
        for (const [key, value] of Object.entries(resources)) {
            if (value?.close) {
                await value.close();
            } else if (Array.isArray(value)) {
                value.length = 0;
            } else if (typeof value === 'object' && value !== null) {
                for (const objKey in value) {
                    delete value[objKey];
                }
            }
            resources[key] = null;
        }
    } catch (error) {
        console.error("Cleanup error:", error);
    }
};

// Helper function to serialize MongoDB documents
function serializeDocument(doc) {
    if (!doc) return null;

    const serialized = { ...doc };

    if (doc._id) {
        serialized._id = doc._id.toString();
    }

    Object.keys(serialized).forEach(key => {
        const value = serialized[key];
        if (value?._bsontype === 'ObjectID') {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        }
    });

    return serialized;
}

// Helper function to invalidate cache for refunded users
export async function invalidateRefundedUsersCache() {
    try {
        await redis.del(REFUNDED_USERS_CACHE_KEY);
        console.log('Refunded users cache invalidated');
    } catch (error) {
        console.error('Error invalidating refunded users cache:', error);
    }
}

// Main function to get refunded users
export default async function getRefundedUsers() {
    const resources = {
        stalkDb: null,
        stripeDb: null,
        userAccess: null,
        users: [],
        stripeRefunds: []
    };

    try {
        // Check authorization
        resources.userAccess = await getUserPermissions();
        if (!resources.userAccess["route:admin"]) {
            throw new Error("Not authorized to access refunded users data");
        }

        // Check Redis cache first
        try {
            const cachedResults = await redis.get(REFUNDED_USERS_CACHE_KEY);
            if (cachedResults) {
                return JSON.parse(cachedResults);
            }
        } catch (cacheError) {
            console.error("Cache read error:", cacheError);
        }

        // Get database connections
        resources.stalkDb = await getDb(STALKDB);
        resources.stripeDb = await getDb(STRIPEDB);

        // Create indexes for users collection
        await resources.stalkDb.collection("users").createIndex(
            { isRefunded: 1 },
            { background: true }
        );
        await resources.stalkDb.collection("users").createIndex(
            { enabled: 1 },
            { background: true }
        );

        // Find users where isRefunded is true OR enabled is false (existing logic)
        resources.users = await resources.stalkDb.collection("users")
            .find({
                $or: [
                    { isRefunded: true },
                    { enabled: false }
                ]
            })
            .sort({ createdAt: -1 })
            .batchSize(BATCH_SIZE)
            .toArray();

        // Search for immediate cancellations/refunds in Stripe database
        resources.stripeRefunds = await resources.stripeDb.collection("log_customer")
            .find({
                eventType: "customer.subscription.deleted",
                isImmediateCancellation: true
            })
            .sort({ createdAt: -1 })
            .batchSize(BATCH_SIZE)
            .toArray();

        // Create a map of existing user IDs for quick lookup
        const existingUserIds = new Set(resources.users.map(user => user.id));

        // Process Stripe refund events and get user details
        const stripeRefundUserIds = resources.stripeRefunds
            .map(refund => refund.idUser)
            .filter(idUser => idUser && !existingUserIds.has(idUser)); // Only get users not already in the list

        // Get user details for Stripe refunded users
        let stripeRefundedUsers = [];
        if (stripeRefundUserIds.length > 0) {
            stripeRefundedUsers = await resources.stalkDb.collection("users")
                .find({ id: { $in: stripeRefundUserIds } })
                .toArray();
        }

        // Serialize existing refunded users
        const serializedExistingUsers = resources.users.map(user => ({
            ...serializeDocument(user),
            hasUserRecord: true,
            source: 'users_collection',
            refundReason: user.refundReason ? user.refundReason : (user.enabled === false ? 'Disabled' : 'Unknown'),
            refundMethod: user.isRefunded ? 'manual' : 'disabled'
        }));

        // Serialize Stripe refunded users with refund event details
        const serializedStripeRefunds = stripeRefundedUsers.map(user => {
            // Find the corresponding refund event for this user
            const refundEvent = resources.stripeRefunds.find(refund => refund.idUser === user.id);
            
            return {
                ...serializeDocument(user),
                hasUserRecord: true,
                source: 'stripe_refunds',
                refundReason: 'Immediate cancellation/refund via Stripe',
                refundMethod: 'stripe_immediate',
                refundEvent: refundEvent ? {
                    eventId: refundEvent.eventId,
                    createdAt: refundEvent.createdAt,
                    customer: refundEvent.customer,
                    subscription: refundEvent.subscription,
                    canceledAt: refundEvent.canceledAt,
                    beforeChange: refundEvent.beforeChange,
                    afterChange: refundEvent.afterChange
                } : null
            };
        });

        // Handle Stripe refunds where user record doesn't exist
        const orphanedStripeRefunds = resources.stripeRefunds
            .filter(refund => refund.idUser && !stripeRefundedUsers.some(user => user.id === refund.idUser))
            .map(refund => ({
                id: refund.idUser,
                hasUserRecord: false,
                source: 'stripe_refunds_orphaned',
                refundReason: 'Immediate cancellation/refund via Stripe (no user record)',
                refundMethod: 'stripe_immediate',
                customer: refund.customer,
                subscription: refund.subscription,
                refundEvent: {
                    eventId: refund.eventId,
                    createdAt: refund.createdAt,
                    customer: refund.customer,
                    subscription: refund.subscription,
                    canceledAt: refund.canceledAt,
                    beforeChange: refund.beforeChange,
                    afterChange: refund.afterChange
                }
            }));

        // Combine all results
        const allRefundedUsers = [
            ...serializedExistingUsers,
            ...serializedStripeRefunds,
            ...orphanedStripeRefunds
        ];

        // Remove duplicates based on user ID
        const uniqueRefundedUsers = allRefundedUsers.reduce((acc, user) => {
            const existingUser = acc.find(u => u.id === user.id);
            if (!existingUser) {
                acc.push(user);
            } else {
                // If duplicate, prefer the one with more refund details
                if (user.refundEvent && !existingUser.refundEvent) {
                    acc[acc.indexOf(existingUser)] = user;
                }
            }
            return acc;
        }, []);

        // Sort by most recent refund date
        uniqueRefundedUsers.sort((a, b) => {
            const aDate = new Date(a.refundEvent?.createdAt || a.createdAt || 0);
            const bDate = new Date(b.refundEvent?.createdAt || b.createdAt || 0);
            return bDate - aDate;
        });

        // Prepare response
        const response = {
            success: true,
            data: uniqueRefundedUsers,
            totalResults: uniqueRefundedUsers.length,
            sources: {
                users_collection: resources.users.length,
                stripe_refunds: stripeRefundedUsers.length,
                stripe_orphaned: orphanedStripeRefunds.length,
                total: uniqueRefundedUsers.length
            },
            breakdown: {
                manual_refunds: uniqueRefundedUsers.filter(u => u.refundMethod === 'manual').length,
                disabled_users: uniqueRefundedUsers.filter(u => u.refundMethod === 'disabled').length,
                stripe_immediate: uniqueRefundedUsers.filter(u => u.refundMethod === 'stripe_immediate').length
            },
            timestamp: new Date().toISOString()
        };

        // Cache the results
        try {
            await redis.setex(REFUNDED_USERS_CACHE_KEY, CACHE_TTL, JSON.stringify(response));
        } catch (cacheError) {
            console.error("Cache write error:", cacheError);
        }

        return response;

    } catch (error) {
        console.error("Error fetching refunded users:", {
            error: error.message,
            stack: error.stack
        });

        return {
            success: false,
            error: error.message || "Failed to fetch refunded users",
            data: [],
            timestamp: new Date().toISOString()
        };
    } finally {
        await cleanupResources(resources);
    }
} 