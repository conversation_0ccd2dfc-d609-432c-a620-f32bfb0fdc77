const { getDb } = require('@/utils/mongodb');
const redis = require('@/utils/redis.server').default;

/**
 * Deletes a user from both the users (stalkreact) and memberships (paymentdata) collections.
 * Also clears any related Redis cache entries.
 *
 * @param {string} userId - The UUID of the user (maps to idUser in memberships, id in users).
 * @returns {Promise<{success: boolean, message: string, data?: any}>} - The result of the delete operation.
 */
async function deleteUser(userId) {
    if (!userId || typeof userId !== 'string') {
        return { success: false, message: "User ID is required and must be a string." };
    }

    let membershipDeleteResult, userDeleteResult;

    try {
        // 1. Delete from paymentdata.memberships
        const paymentDb = await getDb('paymentdata');
        const membershipsCollection = paymentDb.collection('memberships');

        membershipDeleteResult = await membershipsCollection.deleteOne({
            idUser: userId // idUser in memberships collection
        });

        // 2. Delete from stalkreact.users
        const reactDb = await getDb('stalkreact');
        const usersCollection = reactDb.collection('users');

        userDeleteResult = await usersCollection.deleteOne({
            id: userId // id in users collection
        });

        // 3. Invalidate Redis cache for user searches and user-specific data
        try {
            // Clear search cache entries that might contain this user
            const searchCachePattern = 'admin:user_search:*';
            const keys = await redis.keys(searchCachePattern);
            if (keys.length > 0) {
                await redis.del(...keys);
            }

            // Clear any user-specific cache if it exists
            const userCacheKey = `user:${userId}`;
            await redis.del(userCacheKey);

            // Clear any user membership cache
            const membershipCacheKey = `membership:${userId}`;
            await redis.del(membershipCacheKey);

            // Clear any other user-related cache patterns
            const userCachePattern = `*${userId}*`;
            const userKeys = await redis.keys(userCachePattern);
            if (userKeys.length > 0) {
                await redis.del(...userKeys);
            }
        } catch (cacheError) {
            console.error('Cache invalidation error (non-fatal):', cacheError);
            // Don't fail the entire operation due to cache issues
        }

        // Construct response message based on results
        let message = "";
        let deletedItems = [];

        if (userDeleteResult.deletedCount > 0) {
            deletedItems.push("user record");
        }

        if (membershipDeleteResult.deletedCount > 0) {
            deletedItems.push("membership record");
        }

        if (deletedItems.length > 0) {
            message = `Successfully deleted ${deletedItems.join(' and ')} for user ID: ${userId}.`;
        } else {
            message = `No records found to delete for user ID: ${userId}. User may not exist.`;
        }

        // Determine if operation was successful
        const isSuccessful = userDeleteResult.deletedCount > 0 || membershipDeleteResult.deletedCount > 0;

        return {
            success: isSuccessful,
            message: message,
            data: {
                membershipResult: {
                    acknowledged: membershipDeleteResult.acknowledged,
                    deletedCount: membershipDeleteResult.deletedCount
                },
                userResult: {
                    acknowledged: userDeleteResult.acknowledged,
                    deletedCount: userDeleteResult.deletedCount
                },
                totalDeleted: userDeleteResult.deletedCount + membershipDeleteResult.deletedCount
            },
        };

    } catch (error) {
        console.error('Error deleting user and membership records:', error);
        return {
            success: false,
            message: `Failed to delete records for user ID: ${userId}. Error: ${error.message}`,
        };
    }
}

module.exports = {
    deleteUser,
}; 