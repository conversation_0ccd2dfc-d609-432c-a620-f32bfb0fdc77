/**
 * Admin User Payment Transactions Retrieval Action
 * 
 * This server action retrieves all payment transactions for a user from both
 * legacy and new Stripe payment collections, plus crypto payments.
 * 
 * WHAT IT DOES:
 * - Retrieves legacy Stripe payments from paymentdata.payments_stripe collection
 * - Retrieves new Stripe payments from stripe.checkout_completed collection
 * - Retrieves Stripe cancellation/refund events from stripe.log_customer collection
 * - Retrieves crypto payment transactions from payments collection
 * - Returns combined results organized by payment type
 * - Provides admin-only access with proper authorization
 * 
 * WHERE IT SEARCHES:
 * - `payments_stripe` collection (PAYMENTDB) - Legacy Stripe payment records
 * - `checkout_completed` collection (STRIPE DB) - New Stripe payment records
 * - `log_customer` collection (STRIPE DB) - Stripe cancellation/refund events
 * - `payments` collection (PAYMENTDB) - Crypto payment records
 * 
 * HOW IT WORKS:
 * 1. Validates admin permissions and sanitizes user ID
 * 2. Searches both legacy and new Stripe database collections by user ID
 * 3. Searches crypto payment collection by user ID
 * 4. Serializes MongoDB documents for safe JSON transport
 * 5. Returns structured response with payments organized by type
 * 
 * SEARCH FIELDS:
 * - Legacy Stripe payments: idUser field in payments_stripe
 * - New Stripe payments: userId field in checkout_completed
 * - Stripe events: idUser field in log_customer
 * - Crypto payments: idUser field
 * 
 * USAGE:
 * - Import and call from admin components
 * - Requires admin route permissions
 * - Pass user ID as parameter
 * - Returns success/error status with organized payment data
 */

"use server";

import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import DOMPurify from "isomorphic-dompurify";

const PAYMENTDB = process.env.PAYMENT_DB || "paymentdata";
const STRIPEDB = process.env.STRIPE_DB || "stripe";
const BATCH_SIZE = 100; // Limit results to prevent memory issues

// Helper function to clean up resources
const cleanupResources = async (resources) => {
    try {
        for (const [key, value] of Object.entries(resources)) {
            if (value?.close) {
                await value.close();
            } else if (Array.isArray(value)) {
                value.length = 0;
            } else if (typeof value === 'object' && value !== null) {
                for (const objKey in value) {
                    delete value[objKey];
                }
            }
            resources[key] = null;
        }
    } catch (error) {
        console.error("Cleanup error:", error);
    }
};

// Helper function to serialize MongoDB documents
function serializeDocument(doc) {
    if (!doc) return null;

    const serialized = { ...doc };

    if (doc._id) {
        serialized._id = doc._id.toString();
    }

    Object.keys(serialized).forEach(key => {
        const value = serialized[key];
        if (value?._bsontype === 'ObjectID') {
            serialized[key] = value.toString();
        } else if (value instanceof Date) {
            serialized[key] = value.toISOString();
        }
    });

    return serialized;
}

// Main payment transactions retrieval function
export default async function getUserPayments(user) {
    const resources = {
        paymentDb: null,
        stripeDb: null,
        userAccess: null
    };

    try {
        // Extract and sanitize user IDs
        const userId = user?.id || user;
        const privyId = user?.privyId;
        
        const sanitizedUserId = DOMPurify.sanitize(userId?.trim() || '');
        const sanitizedPrivyId = DOMPurify.sanitize(privyId?.trim() || '');
        
        if (!sanitizedUserId) {
            return {
                success: false,
                error: "User ID is required",
                data: {
                    stripeLegacy: [],
                    stripeNew: [],
                    stripeEvents: [],
                    crypto: []
                }
            };
        }

        // Check authorization
        resources.userAccess = await getUserPermissions();
        if (!resources.userAccess["route:admin"]) {
            throw new Error("Not authorized to access payment data");
        }

        // Connect to both databases
        resources.paymentDb = await getDb(PAYMENTDB);
        resources.stripeDb = await getDb(STRIPEDB);

        // Search for LEGACY Stripe payments in paymentdata.payments_stripe by idUser
        let stripeLegacyPayments = await resources.paymentDb.collection("payments_stripe")
            .find({ idUser: sanitizedUserId })
            .sort({ paymentDate: -1 }) // Most recent first
            .limit(BATCH_SIZE)
            .toArray();

        // If no legacy Stripe payments found and we have a privyId, try searching by privyId
        if (stripeLegacyPayments.length === 0 && sanitizedPrivyId) {
            stripeLegacyPayments = await resources.paymentDb.collection("payments_stripe")
                .find({ idUser: sanitizedPrivyId })
                .sort({ paymentDate: -1 })
                .limit(BATCH_SIZE)
                .toArray();
        }

        // Search for NEW Stripe payments in stripe.checkout_completed by userId
        let stripeNewPayments = [];
        try {
            stripeNewPayments = await resources.stripeDb.collection("checkout_completed")
                .find({ userId: sanitizedUserId })
                .sort({ createdAt: -1 }) // Most recent first
                .limit(BATCH_SIZE)
                .toArray();

            // If no new Stripe payments found and we have a privyId, try searching by privyId
            if (stripeNewPayments.length === 0 && sanitizedPrivyId) {
                stripeNewPayments = await resources.stripeDb.collection("checkout_completed")
                    .find({ userId: sanitizedPrivyId })
                    .sort({ createdAt: -1 })
                    .limit(BATCH_SIZE)
                    .toArray();
            }

            // Also try searching by customer field if no results found
            if (stripeNewPayments.length === 0) {
                stripeNewPayments = await resources.stripeDb.collection("checkout_completed")
                    .find({ 
                        $or: [
                            { customer: { $regex: sanitizedUserId, $options: 'i' } },
                            ...(sanitizedPrivyId ? [{ customer: { $regex: sanitizedPrivyId, $options: 'i' } }] : [])
                        ]
                    })
                    .sort({ createdAt: -1 })
                    .limit(BATCH_SIZE)
                    .toArray();
            }
        } catch (stripeDbError) {
            console.warn("Could not access stripe database for checkout_completed:", stripeDbError.message);
        }

        // Search for Stripe cancellation/refund events in stripe.log_customer
        let stripeEvents = [];
        try {
            stripeEvents = await resources.stripeDb.collection("log_customer")
                .find({ 
                    idUser: sanitizedUserId,
                    eventType: "customer.subscription.deleted"
                })
                .sort({ createdAt: -1 })
                .limit(BATCH_SIZE)
                .toArray();

            // Try with privyId if no events found
            if (stripeEvents.length === 0 && sanitizedPrivyId) {
                stripeEvents = await resources.stripeDb.collection("log_customer")
                    .find({ 
                        idUser: sanitizedPrivyId,
                        eventType: "customer.subscription.deleted"
                    })
                    .sort({ createdAt: -1 })
                    .limit(BATCH_SIZE)
                    .toArray();
            }

            // Also try searching by customer field in log_customer
            if (stripeEvents.length === 0) {
                stripeEvents = await resources.stripeDb.collection("log_customer")
                    .find({ 
                        eventType: "customer.subscription.deleted",
                        $or: [
                            { customer: { $regex: sanitizedUserId, $options: 'i' } },
                            ...(sanitizedPrivyId ? [{ customer: { $regex: sanitizedPrivyId, $options: 'i' } }] : [])
                        ]
                    })
                    .sort({ createdAt: -1 })
                    .limit(BATCH_SIZE)
                    .toArray();
            }
        } catch (stripeDbError) {
            console.warn("Could not access stripe database for log_customer:", stripeDbError.message);
        }

        // Search for crypto payments in paymentdata.payments
        let cryptoPayments = await resources.paymentDb.collection("payments")
            .find({ idUser: sanitizedUserId })
            .sort({ paymentDate: -1 }) // Most recent first
            .limit(BATCH_SIZE)
            .toArray();

        // If no crypto payments found and we have a privyId, try searching by privyId
        if (cryptoPayments.length === 0 && sanitizedPrivyId) {
            cryptoPayments = await resources.paymentDb.collection("payments")
                .find({ idUser: sanitizedPrivyId })
                .sort({ paymentDate: -1 })
                .limit(BATCH_SIZE)
                .toArray();
        }

        // Serialize the payment documents
        const serializedStripeLegacy = stripeLegacyPayments.map(payment => serializeDocument(payment));
        const serializedStripeNew = stripeNewPayments.map(payment => serializeDocument(payment));
        const serializedStripeEvents = stripeEvents.map(event => serializeDocument(event));
        const serializedCryptoPayments = cryptoPayments.map(payment => serializeDocument(payment));

        // Combine legacy and new stripe payments for backward compatibility
        const combinedStripePayments = [...serializedStripeLegacy, ...serializedStripeNew];

        return {
            success: true,
            data: {
                stripe: combinedStripePayments, // Combined for backward compatibility
                stripeLegacy: serializedStripeLegacy,
                stripeNew: serializedStripeNew,
                stripeEvents: serializedStripeEvents,
                crypto: serializedCryptoPayments
            },
            userId: sanitizedUserId,
            privyId: sanitizedPrivyId,
            searchStrategy: {
                stripeLegacyFoundWith: stripeLegacyPayments.length > 0 ? 
                    (stripeLegacyPayments[0]?.idUser === sanitizedUserId ? 'userId' : 'privyId') : 'none',
                stripeNewFoundWith: stripeNewPayments.length > 0 ? 
                    (stripeNewPayments[0]?.userId === sanitizedUserId ? 'userId' : 
                     stripeNewPayments[0]?.userId === sanitizedPrivyId ? 'privyId' : 'customer') : 'none',
                stripeEventsFoundWith: stripeEvents.length > 0 ? 
                    (stripeEvents[0]?.idUser === sanitizedUserId ? 'userId' : 
                     stripeEvents[0]?.idUser === sanitizedPrivyId ? 'privyId' : 'customer') : 'none',
                cryptoFoundWith: cryptoPayments.length > 0 ? 
                    (cryptoPayments[0]?.idUser === sanitizedUserId ? 'userId' : 'privyId') : 'none'
            },
            totalTransactions: {
                stripeLegacy: serializedStripeLegacy.length,
                stripeNew: serializedStripeNew.length,
                stripe: combinedStripePayments.length,
                stripeEvents: serializedStripeEvents.length,
                crypto: serializedCryptoPayments.length,
                total: combinedStripePayments.length + serializedStripeEvents.length + serializedCryptoPayments.length
            },
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error("Error retrieving payment transactions:", {
            error: error.message,
            stack: error.stack,
            user
        });

        return {
            success: false,
            error: error.message || "Failed to retrieve payment transactions",
            data: {
                stripe: [],
                stripeLegacy: [],
                stripeNew: [],
                stripeEvents: [],
                crypto: []
            },
            timestamp: new Date().toISOString()
        };
    } finally {
        await cleanupResources(resources);
    }
} 