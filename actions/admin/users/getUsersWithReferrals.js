"use server";

import { getUserPermissions } from "@/utils/authState";
//TO DO?
//Referral tracking is now handled by PromoteKit

// Main function to get users with referrals
export default async function getUsersWithReferrals() {
    try {
        // Check authorization
        const userAccess = await getUserPermissions();
        if (!userAccess["route:admin"]) {
            throw new Error("Not authorized to access users with referrals data");
        }

        // Return message indicating referrals are handled by PromoteKit
        return {
            success: true,
            data: [],
            totalResults: 0,
            statistics: {
                totalUsersWithReferrals: 0,
                totalReferrals: 0,
                totalReferralAmount: 0,
                averageReferralsPerUser: 0,
                usersWithUserRecords: 0
            },
            message: "Referral tracking is now handled by PromoteKit. Please use the PromoteKit dashboard for referral analytics.",
            source: 'promotekit',
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error("Error in getUsersWithReferrals:", error.message);

        return {
            success: false,
            error: error.message || "Failed to fetch users with referrals",
            data: [],
            source: 'promotekit',
            timestamp: new Date().toISOString()
        };
    }
} 