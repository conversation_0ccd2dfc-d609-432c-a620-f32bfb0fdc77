/**
 * Admin User Activation Status Retrieval Action
 * 
 * This server action retrieves activation token status for a user from the
 * Stripe database to help admins understand activation issues.
 * 
 * WHAT IT DOES:
 * - Retrieves activation tokens associated with a user's email or customer ID
 * - Checks token validity, expiration, and usage status
 * - Returns comprehensive activation status information
 * - Provides admin-only access with proper authorization
 * 
 * WHERE IT SEARCHES:
 * - `activation_tokens` collection (STRIPE DB) - Activation token records
 * - `checkout_completed` collection (STRIPE DB) - Related checkout data
 * 
 * HOW IT WORKS:
 * 1. Validates admin permissions and sanitizes user data
 * 2. Searches activation tokens by email and customer ID
 * 3. Checks token validity, expiration, and usage status
 * 4. Returns structured response with activation status
 * 
 * USAGE:
 * - Import and call from admin components
 * - Requires admin route permissions
 * - Pass user object with email/stripeCustomerId
 * - Returns success/error status with activation data
 */

"use server";

import { getUserPermissions } from "@/utils/authState";
import { getDb } from "@/utils/mongodb";
import DOMPurify from "isomorphic-dompurify";

const STRIPEDB = process.env.STRIPE_DB || "stripe";
const BATCH_SIZE = 50;

// Helper function to serialize MongoDB documents
const serializeDocument = (doc) => {
    if (!doc) return null;
    
    return JSON.parse(JSON.stringify(doc, (key, value) => {
        if (value && typeof value === 'object' && value.constructor && value.constructor.name === 'ObjectId') {
            return value.toString();
        }
        return value;
    }));
};

// Helper function to clean up resources
const cleanupResources = async (resources) => {
    try {
        for (const [key, value] of Object.entries(resources)) {
            if (value?.close) {
                await value.close();
            } else if (Array.isArray(value)) {
                value.length = 0;
            } else if (typeof value === 'object' && value !== null) {
                for (const objKey in value) {
                    delete value[objKey];
                }
            }
            resources[key] = null;
        }
    } catch (error) {
        console.error("Cleanup error:", error);
    }
};

// Helper function to check token status
const getTokenStatus = (token) => {
    if (!token) return 'invalid';
    
    const now = new Date();
    const expiresAt = token.expiresAt ? new Date(token.expiresAt) : null;
    
    if (token.used) {
        return 'used';
    } else if (expiresAt && now > expiresAt) {
        return 'expired';
    } else {
        return 'valid';
    }
};

// Main activation status retrieval function
export default async function getUserActivationStatus(user) {
    const resources = {
        stripeDb: null,
        userAccess: null
    };

    try {
        // Extract and sanitize user data
        const email = user?.email || user?.oldDbEmail;
        const stripeCustomerId = user?.stripeCustomerId;
        const userId = user?.id || user?.idUser;
        
        const sanitizedEmail = email ? DOMPurify.sanitize(email.trim()) : null;
        const sanitizedCustomerId = stripeCustomerId ? DOMPurify.sanitize(stripeCustomerId.trim()) : null;
        const sanitizedUserId = userId ? DOMPurify.sanitize(userId.trim()) : null;
        
        if (!sanitizedEmail && !sanitizedCustomerId) {
            return {
                success: false,
                error: "Email or Stripe Customer ID is required",
                data: {
                    tokens: [],
                    summary: {
                        totalTokens: 0,
                        validTokens: 0,
                        usedTokens: 0,
                        expiredTokens: 0
                    }
                }
            };
        }

        // Check authorization
        resources.userAccess = await getUserPermissions();
        if (!resources.userAccess["route:admin"]) {
            throw new Error("Not authorized to access activation status data");
        }

        // Connect to stripe database
        resources.stripeDb = await getDb(STRIPEDB);

        // Build search query
        const searchQuery = { $or: [] };
        
        if (sanitizedEmail) {
            searchQuery.$or.push({ email: sanitizedEmail });
        }
        
        if (sanitizedCustomerId) {
            searchQuery.$or.push({ customerId: sanitizedCustomerId });
        }

        // Search for activation tokens
        const activationTokens = await resources.stripeDb.collection("activation_tokens")
            .find(searchQuery)
            .sort({ createdAt: -1 }) // Most recent first
            .limit(BATCH_SIZE)
            .toArray();

        // Process tokens and get status
        const processedTokens = activationTokens.map(token => {
            const status = getTokenStatus(token);
            const serializedToken = serializeDocument(token);
            
            return {
                ...serializedToken,
                status,
                isValid: status === 'valid',
                isExpired: status === 'expired',
                isUsed: status === 'used',
                daysUntilExpiry: token.expiresAt ? 
                    Math.ceil((new Date(token.expiresAt) - new Date()) / (1000 * 60 * 60 * 24)) : null
            };
        });

        // Calculate summary statistics
        const summary = {
            totalTokens: processedTokens.length,
            validTokens: processedTokens.filter(t => t.status === 'valid').length,
            usedTokens: processedTokens.filter(t => t.status === 'used').length,
            expiredTokens: processedTokens.filter(t => t.status === 'expired').length,
            hasValidToken: processedTokens.some(t => t.status === 'valid'),
            mostRecentToken: processedTokens.length > 0 ? processedTokens[0] : null
        };

        // Get related checkout data for context
        const checkoutData = [];
        if (processedTokens.length > 0) {
            const subscriptionIds = [...new Set(processedTokens.map(t => t.subscriptionId).filter(Boolean))];
            
            if (subscriptionIds.length > 0) {
                const checkouts = await resources.stripeDb.collection("checkout_completed")
                    .find({ subscription: { $in: subscriptionIds } })
                    .sort({ createdAt: -1 })
                    .limit(BATCH_SIZE)
                    .toArray();
                
                checkoutData.push(...checkouts.map(checkout => serializeDocument(checkout)));
            }
        }

        return {
            success: true,
            data: {
                tokens: processedTokens,
                checkouts: checkoutData,
                summary,
                searchCriteria: {
                    email: sanitizedEmail,
                    customerId: sanitizedCustomerId,
                    userId: sanitizedUserId
                }
            },
            timestamp: new Date().toISOString()
        };

    } catch (error) {
        console.error("Error retrieving activation status:", {
            error: error.message,
            stack: error.stack,
            user: user?.id || user?.email
        });

        return {
            success: false,
            error: error.message || "Failed to retrieve activation status",
            data: {
                tokens: [],
                summary: {
                    totalTokens: 0,
                    validTokens: 0,
                    usedTokens: 0,
                    expiredTokens: 0
                }
            },
            timestamp: new Date().toISOString()
        };
    } finally {
        await cleanupResources(resources);
    }
} 