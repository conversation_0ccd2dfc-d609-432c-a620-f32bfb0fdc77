const { getDb } = require('@/utils/mongodb');
const redis = require('@/utils/redis.server').default;

/**
 * Updates the role and expiration date for a user in the memberships (paymentdata)
 * and users (stalkreact) collections. If no record exists, a new minimal one will be created in both.
 *
 * @param {string} userId - The UUID of the user (maps to idUser in memberships, id in users).
 * @param {number} newRoleId - The new role ID to set for the user.
 * @param {Date | string} newExpirationDate - The new expiration date for the membership.
 * @returns {Promise<{success: boolean, message: string, data?: any}>} - The result of the update operation.
 */
async function updateUserMembershipRole(userId, newRoleId, newExpirationDate) {
    if (!userId || typeof newRoleId !== 'number' || !newExpirationDate) {
        return { success: false, message: "User ID, a numeric new Role ID, and a new Expiration Date are required." };
    }

    let parsedExpirationDate;
    try {
        parsedExpirationDate = new Date(newExpirationDate);
        if (isNaN(parsedExpirationDate.getTime())) {
            throw new Error('Invalid date format for newExpirationDate.');
        }
    } catch (e) {
        return { success: false, message: `Invalid newExpirationDate: ${e.message}` };
    }

    const now = new Date();
    let membershipUpdateResult, userUpdateResult;

    try {
        // 1. Update/Create in paymentdata.memberships
        const paymentDb = await getDb('paymentdata');
        const membershipsCollection = paymentDb.collection('memberships');

        const membershipUpdateOperation = {
            $set: {
                idRole: newRoleId,
                expirationDate: parsedExpirationDate,
                updatedAt: now,
            },
            $setOnInsert: {
                // idUser will be set by the query filter if it's an insert
                createdAt: now,
            }
        };

        membershipUpdateResult = await membershipsCollection.updateOne(
            { idUser: userId }, // idUser in memberships collection
            membershipUpdateOperation,
            { upsert: true }
        );

        // 2. Update/Create in stalkreact.users
        const reactDb = await getDb('stalkreact');
        const usersCollection = reactDb.collection('users');

        const userUpdateOperation = {
            $set: {
                idRole: newRoleId,
                expirationDate: parsedExpirationDate,
                updatedAt: now,
            }
        };

        userUpdateResult = await usersCollection.updateOne(
            { id: userId }, // id in users collection
            userUpdateOperation
        );

        // 3. Invalidate Redis cache for user searches
        try {
            // Clear search cache entries that might contain this user
            const searchCachePattern = 'admin:user_search:*';
            const keys = await redis.keys(searchCachePattern);
            if (keys.length > 0) {
                await redis.del(...keys);
            }

            // Also clear any user-specific cache if it exists
            const userCacheKey = `user:${userId}`;
            await redis.del(userCacheKey);
        } catch (cacheError) {
            console.error('Cache invalidation error (non-fatal):', cacheError);
            // Don't fail the entire operation due to cache issues
        }

        // Construct success message based on results
        let message = "";
        if (membershipUpdateResult.upsertedId) {
            message += `Successfully created new membership `;
        } else if (membershipUpdateResult.modifiedCount > 0 || membershipUpdateResult.matchedCount > 0) {
            message += `Successfully updated membership `;
        }

        if (userUpdateResult.modifiedCount > 0 || userUpdateResult.matchedCount > 0) {
            message += `Successfully updated user record`;
        }

        if ((membershipUpdateResult.modifiedCount === 0 && !membershipUpdateResult.upsertedId && membershipUpdateResult.matchedCount > 0) &&
            (userUpdateResult.modifiedCount === 0 && userUpdateResult.matchedCount > 0)) {
            message = `Already up to date. No update performed.`;
        } else {
            message += `Role set to ${newRoleId}, expiration to ${parsedExpirationDate.toISOString()}.`;
        }

        return {
            success: true,
            message: message.trim(),
            data: {
                membershipResult: { acknowledged: membershipUpdateResult.acknowledged, upsertedId: membershipUpdateResult.upsertedId, matchedCount: membershipUpdateResult.matchedCount, modifiedCount: membershipUpdateResult.modifiedCount },
                userResult: { acknowledged: userUpdateResult.acknowledged, matchedCount: userUpdateResult.matchedCount, modifiedCount: userUpdateResult.modifiedCount },
            },
        };

    } catch (error) {
        console.error('Error updating or creating user membership and/or user record:', error);
        // Basic error reporting. Could be enhanced to indicate which part failed.
        return {
            success: false,
            message: `Failed to update/create records for user ID: ${userId}. Error: ${error.message}`,
        };
    }
}

module.exports = {
    updateUserMembershipRole,
}; 