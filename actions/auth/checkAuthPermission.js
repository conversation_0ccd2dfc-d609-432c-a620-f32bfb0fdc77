"use server"

import { headers } from 'next/headers';
import { dashboardNavItems, dashboardNavItemPaths } from "@/config/dashboard";
import { getAuthStateData } from "@/utils/authState";
import LEVELS from "@/static/levels.json";

/*
 * Available User Levels:
 * - NOLOGIN: 0 (not logged in users)
 * - FREE: 10 (default for logged in users)
 * - EXPIRED: 10 (expired subscription)
 * - TRIAL: 50 (trial users)
 * - PRO: 60 (pro subscription)
 * - KOL: 70 (key opinion leader)
 * - LEGEND: 80 (legend tier)
 * - TEAM: 90 (team members)
 * - ADMIN: 99 (administrators)
 * 
 * Permission Types:
 * - "nologin": Accessible to everyone (including non-logged in users)
 * - "loggedin": Requires user to be logged in
 * - "specific-permission": Requires specific permission access
 */

export async function checkAuthPermission() {
  try {
    const headersList = await headers();
    let pathname = headersList.get('x-path');

    if(pathname === null) {
      return true;
    }
    // console.log("pathname", pathname);

    // Check for specific nested paths first (like /dashboard/design/*)
    // This is for testing and staging components - only accessible in development
    const isDesignPath = pathname && pathname.startsWith('/dashboard/design');
    if (isDesignPath && process.env.NODE_ENV === 'development') {
      // Design pages are handled by the special path in dashboardNavItemPaths
      const designPathConfig = dashboardNavItemPaths.find(item => item.path === '/dashboard/design');
      if (designPathConfig && designPathConfig.permission === "nologin") {
        return true;
      }
    }

    // Normalize the pathname to handle nested routes
    if(pathname) {
      const pathParts = pathname.split('/');
      if (pathParts.length >= 2) {
        pathname = `/${pathParts[1]}`;
      }
    }

    const allNavItems = dashboardNavItems.flatMap(section => section.items);

    // Check in regular nav items
    const thisPathPermission = allNavItems.find(item => item.href === pathname)?.permission;
    
    // If not found in regular nav items, check in special paths
    const specialPathConfig = dashboardNavItemPaths.find(item => item.path === pathname);
    const specialPathPermission = specialPathConfig?.permission;
    
    // Combine both permission sources
    const effectivePermission = thisPathPermission || specialPathPermission;
    
    const userPermissions = await getAuthStateData();

    // If no specific permission is required for this path
    if (!effectivePermission) {
      return true;
    }

    // Handle login-only routes
    if(effectivePermission === "loggedin") {
      return userPermissions?.isLoggedin ?? false;
    }

    // Handle no-login routes (accessible to everyone)
    if(effectivePermission === "nologin") {
      return true;
    }

    // Check specific permission
    return userPermissions?.access?.[effectivePermission] ?? false;
  } catch (error) {
    console.error("[checkAuthPermission] Error:", error);
    return false;
  }
}

/**
 * Check if user meets minimum level requirement for component access
 * @param {string} minimumLevel - Required level (e.g., 'PRO', 'LEGEND', 'KOL')
 * @returns {Promise<boolean>} - True if user meets or exceeds the minimum level
 * 
 * @example
 * // In a component
 * const hasAccess = await checkMinimumLevel('PRO');
 * if (!hasAccess) return <UpgradePrompt />;
 * 
 * @example
 * // Check for legend access
 * const isLegend = await checkMinimumLevel('LEGEND');
 */
export async function checkMinimumLevel(minimumLevel) {
  try {
    // Get user's current level
    const userLevel = await getAuthStateData('level');
    
    // If user has no level or is not logged in, default to NOLOGIN level
    const currentLevel = userLevel ?? LEVELS.NOLOGIN;
    
    // Get the numeric value for the required minimum level
    const requiredLevelValue = LEVELS[minimumLevel.toUpperCase()];
    
    // If the required level doesn't exist, deny access
    if (requiredLevelValue === undefined) {
      console.warn(`[checkMinimumLevel] Unknown level: ${minimumLevel}`);
      return false;
    }
    
    // Check if user's level meets or exceeds the requirement
    return currentLevel >= requiredLevelValue;
  } catch (error) {
    console.error("[checkMinimumLevel] Error:", error);
    return false;
  }
}

/**
 * Get user's current level name
 * @returns {Promise<string|null>} - User's level name (e.g., 'PRO', 'LEGEND') or null if not found
 */
export async function getUserLevelName() {
  try {
    const userLevel = await getAuthStateData('level');
    
    if (!userLevel) return null;
    
    // Find the level name by matching the numeric value
    const levelEntry = Object.entries(LEVELS).find(([name, value]) => value === userLevel);
    return levelEntry ? levelEntry[0] : null;
  } catch (error) {
    console.error("[getUserLevelName] Error:", error);
    return null;
  }
}