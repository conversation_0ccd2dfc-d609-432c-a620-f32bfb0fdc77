"use server";

import { query } from "@/utils/postgres";
import redis from '@/utils/redis.server';

/**
 * PostgreSQL Table Schema:
 * 
 * CREATE TABLE "public"."wallets_cex" (
 *   "address" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
 *   "cex" varchar(255) COLLATE "pg_catalog"."default",
 *   "created_at" timestamptz(6) NOT NULL,
 *   "updated_at" timestamptz(6) NOT NULL
 * )
 * 
 * Note: Our code uses "address" field as the wallet address and "cex" field as the CEX name
 */

/**
 * Fetches all CEX wallets with full data from Redis cache
 * If cache is empty, syncs from PostgreSQL first
 * Also triggers background sync every hour to keep data fresh
 * 
 * @param {Object} options - Query options (currently only used for response structure)
 * @returns {Promise<Object>} Result object with full CEX wallet data
 */
export async function getCex(options = {}) {
  try {
    // Import cache utilities
    const { getAllCexWalletsWithData, syncCexWallets } = await import('@/utils/cache/cexWallets');

    // Try to get CEX wallets with full data from cache first
    let cacheResult = await getAllCexWalletsWithData();
    
    // If cache is empty or failed, sync from PostgreSQL (blocking)
    if (!cacheResult.success || cacheResult.count === 0) {
      console.log('CEX wallets cache is empty, syncing from PostgreSQL...');
      
      const syncResult = await syncCexWallets();
      if (!syncResult.success) {
        console.error('Failed to sync CEX wallets:', syncResult.error);
        return {
          success: false,
          data: [],
          error: syncResult.error
        };
      }
      
      console.log(`Successfully synced ${syncResult.walletsAdded} CEX wallets to cache`);
      
      // Try to get from cache again after sync
      cacheResult = await getAllCexWalletsWithData();
    } else {
      // Cache has data, trigger background sync if cooldown has passed
      triggerBackgroundSync();
    }

    if (!cacheResult.success) {
      return {
        success: false,
        data: [],
        error: cacheResult.error
      };
    }

    // Transform data to match the expected format (address, cex as name)
    const transformedData = cacheResult.wallets.map(wallet => ({
      address: wallet.address,
      name: wallet.name
    }));

    return {
      success: true,
      data: transformedData,
      count: cacheResult.count,
      fromCache: true,
      cacheTtl: cacheResult.ttl
    };

  } catch (error) {
    console.error('Error fetching CEX wallets:', error);
    console.error('Error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      code: error.code || 'NO_CODE'
    });
    
    return {
      success: false,
      data: [],
      error: error.message || 'Unknown error occurred while fetching CEX wallets'
    };
  }
}

/**
 * Triggers a background sync if cooldown period has passed
 * Non-blocking operation with 1-hour cooldown
 */
async function triggerBackgroundSync() {
  try {
    const { syncCexWallets } = await import('@/utils/cache/cexWallets');
    const SYNC_COOLDOWN_KEY = 'stalkchain:cex:wallets:last_sync';
    const COOLDOWN_SECONDS = 60 * 60; // 1 hour
    
    // Check when last sync occurred
    const lastSync = await redis.get(SYNC_COOLDOWN_KEY);
    const now = Math.floor(Date.now() / 1000);
    
    if (!lastSync || (now - parseInt(lastSync)) > COOLDOWN_SECONDS) {
      // Set the sync timestamp immediately to prevent multiple syncs
      await redis.set(SYNC_COOLDOWN_KEY, now.toString(), 'EX', COOLDOWN_SECONDS * 2);
      
      // Trigger sync in background (non-blocking)
      setImmediate(async () => {
        try {
          console.log('Starting background CEX wallets sync...');
          const syncResult = await syncCexWallets();
          if (syncResult.success) {
            console.log(`Background sync completed: ${syncResult.walletsAdded} CEX wallets synced`);
          } else {
            console.error('Background sync failed:', syncResult.error);
          }
        } catch (error) {
          console.error('Background sync error:', error);
        }
      });
    }
  } catch (error) {
    console.error('Error checking sync cooldown:', error);
    // Don't throw - this is a background operation
  }
}
