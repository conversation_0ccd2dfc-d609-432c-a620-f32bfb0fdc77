---
description: 
globs: 
alwaysApply: true
---
# <PERSON><PERSON><PERSON><PERSON>n Next.js Project Guidelines

## Project Structure and Naming Conventions

### Directory Structure
```
stalkchain-last-react/
├── app/                    # Next.js 13+ App Router directory
│   ├── (auth)/            # Authentication related routes
│   ├── (dashboard)/       # Dashboard related routes
│   ├── api/               # API routes
│   └── layout.js          # Root layout
├── components/            # Reusable React components
│   ├── ui/               # Basic UI components
│   ├── forms/            # Form-related components
│   ├── layouts/          # Layout components
│   └── icons/            # SVG and icon components
├── content/              # Page-specific content components
├── context/              # React Context providers
├── hooks/               # Custom React hooks
├── utils/               # Utility functions and helpers
├── providers/           # Service providers and configurations
├── public/              # Static assets
│   ├── images/          # Image assets
│   ├── fonts/           # Font files
│   ├── favicon/         # Favicon and related icons
│   └── locales/         # Internationalization files
├── styles/              # Global styles and CSS modules
└── tests/               # Test files
```

### Naming Conventions
// ... existing code ...
```

### Naming Conventions

1. **Files and Directories**
   - Use kebab-case for directory names: `user-profile/`
   - Use PascalCase for component files: `UserProfile.jsx`
   - Use camelCase for utility files: `formatDate.js`
   - Test files should match their implementation file: `UserProfile.test.js`

2. **Components**
   - Use PascalCase for component names: `UserProfile`
   - Use .jsx extension for components with JSX
   - Use .js extension for plain JavaScript files
   - Place components in logical subdirectories under `components/`

3. **Hooks**
   - Prefix with 'use': `useAuth`, `useForm`
   - Place in `hooks/` directory with .js extension
   - One hook per file

4. **Context**
   - Suffix with 'Context': `AuthContext`
   - Place provider components in `providers/`
   - Export both context and provider from same file

## Coding Standards

### JavaScript
- Use ES6+ features appropriately
- Use PropTypes for component prop validation
- Implement proper JSDoc comments for documentation
- Use proper module imports/exports

### React Best Practices
1. **Components**
   - Keep components focused and single-responsibility
   - Use functional components with hooks
   - Implement proper error boundaries
   - Use PropTypes for prop validation
   - Use defaultProps when appropriate

2. **State Management**
   - Use React Context for global state
   - Prefer local state when possible
   - Implement proper state initialization
   - Use appropriate caching strategies

3. **Performance**
   - Implement proper memoization (useMemo, useCallback)
   - Lazy load components when appropriate
   - Optimize re-renders
   - Use proper key props in lists

### API and Data Handling
1. **API Calls**
   - Use Next.js API routes for backend communication
   - Implement proper error handling
   - Use appropriate caching strategies
   - Handle loading and error states

2. **Data Management**
   - Use proper data validation
   - Implement proper error handling
   - Handle edge cases and null values
   - Use appropriate data transformation utilities

## Security Guidelines

1. **Data Protection**
   - Sanitize user inputs
   - Implement proper XSS protection
   - Use secure headers
   - Follow OWASP guidelines

## Performance Guidelines

1. **Optimization**
   - Implement proper code splitting
   - Use image optimization
   - Implement proper caching strategies
   - Monitor and optimize bundle size
   - Split as much as possible into smaller blocks of code in different files
   - It's better to split JSX into multiple reusable components instead of one big component

2. **Loading**
   - Implement proper loading states
   - Use skeleton loading where appropriate
   - Optimize First Contentful Paint
   - Implement proper error fallbacks

## Documentation

1. **Code Documentation**
   - Use JSDoc for function documentation
   - Document component props using PropTypes
   - Document complex logic
   - Keep documentation up-to-date

2. **Project Documentation**
   - Maintain README.md
   - Document setup procedures
   - Document deployment process
   - Keep architecture decisions records
   - Make {ComponentName}.md files for each component inside `components/`

## Git Guidelines

1. **Branching**
   - Use feature branches
   - Follow semantic versioning
   - Use proper commit messages
   - Implement proper PR reviews

2. **Commits**
   - Use conventional commits
   - Keep commits focused
   - Write meaningful commit messages
   - Reference issues in commits

## Development Workflow

1. **Local Development**
   - Use proper environment variables
   - Follow proper debugging practices
   - Use proper linting and formatting
   - Implement proper hot reloading

2. **Code Review**
   - Follow code review checklist
   - Use proper PR templates
   - Implement proper CI/CD
   - Maintain code quality standards

## Accessibility Guidelines

1. **Standards**
   - Follow WCAG 2.1 guidelines
   - Implement proper ARIA labels
   - Use semantic HTML
   - Test with screen readers

2. **Implementation**
   - Use proper color contrast keeping the general theme layout
   - Use proper focus management
   - Implement proper error announcements

## Error Handling

1. **Client-Side**
   - Implement proper error boundaries
   - Use proper error logging
   - Implement user-friendly error messages

2. **Server-Side**
   - Implement proper error handling
   - Use proper logging
   - Implement proper status codes
   - Handle edge cases



