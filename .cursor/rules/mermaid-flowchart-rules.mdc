---
description: flowcharts, mermaid charts, when asked or wanting to create a flowchart
globs: 
alwaysApply: false
---
## FLOWCHART RULES:

1. Stick to "Visual Explanations" by <PERSON> methodology
2. Use conceptual grouping judiciously - Group related elements into subgraphs only when complexity requires it; prefer a flat structure for simpler flows.
3. Establish clear visual hierarchy - Apply subtle color differences to distinguish process types; limit palette to 3-5 colors with consistent meaning.
4. Maintain directional clarity - Design flow in one primary direction (top-to-bottom or left-to-right) to minimize line crossings.
5. Balance information density - Include only essential details relevant to the current level of abstraction; create separate diagrams for deeper dives.
6. Apply consistent symbolism - Use standard shapes purposefully (diamonds for decisions, rectangles for processes) across all related diagrams.

## STYLING NODES:

The proper way to apply styles in Mermaid is to define the styles in a `classDef` and then apply the class to nodes using the `class` keyword:

```mermaid
flowchart TD
    A("Client Process")
    B("Server Process")
    C("Database Operation")
    D{"Decision"}
    
    A --> B --> C
    B --> D
    
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#f8f9fa,stroke:#455a64,color:#455a64
    
    class A clientProcess
    class B serverProcess
    class C databaseProcess
    class D decisionPoint
```

**Code:**
```
flowchart TD
    A("Client Process")
    B("Server Process")
    C("Database Operation")
    D{"Decision"}
    
    A --> B --> C
    B --> D
    
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#f8f9fa,stroke:#455a64,color:#455a64
    
    class A clientProcess
    class B serverProcess
    class C databaseProcess
    class D decisionPoint
```

## STALKCHAIN STANDARD COLORS:

For consistency across all diagrams, use these color combinations:

| Process Type | Fill | Stroke | Text |
|-------------|------|--------|------|
| Client-side | #e1f5fe | #01579b | #01579b |
| Server-side | #e8f5e9 | #2e7d32 | #2e7d32 |
| Stripe/External | #fff3e0 | #e65100 | #e65100 |
| Database | #f3e5f5 | #6a1b9a | #6a1b9a |
| Decision | #f8f9fa | #455a64 | #455a64 |


## BEST PRACTICES:

1. **Maintain consistent direction** (top-down or left-right) throughout each diagram
2. **Limit color palette** to 3-5 colors with consistent meaning
3. **Keep node text concise** (5-7 words maximum)
4. **Use ID references** (A, B, C) rather than long node names in connections
5. **Add comments** with `%%` to document complex sections
6. **Organize nodes** spatially to minimize crossing lines
7. **Group related elements** with subgraphs only when necessary
8. **Use standard shapes** consistently for similar functions
9. **Apply styling at the end** of the diagram

## SUBGRAPH USAGE GUIDELINES:

### When to Use Subgraphs ✅
- **Complex flows with 8+ nodes** that can be logically grouped
- **Multi-system interactions** (Frontend ↔ Backend ↔ Database)
- **Sequential phases** in a process (Authentication → Verification → Completion)
- **Parallel workflows** that need visual separation
- **Clear organizational boundaries** (User Actions vs System Processing)

### When NOT to Use Subgraphs ❌
- **Simple linear flows** with 5 or fewer nodes
- **Single responsibility processes** that don't have natural groupings
- **When grouping creates more visual clutter** than clarity
- **Forced categorization** just to use subgraphs

### Subgraph Best Practices:
1. **Limit to 3-4 subgraphs** per diagram maximum
2. **Use descriptive titles** that explain the grouping purpose
3. **Keep subgraph boundaries clean** - avoid excessive connections between groups
4. **Balance subgraph sizes** - avoid one huge group with several tiny ones
5. **Consider diagram splitting** if subgraphs become too complex

### Examples:

**✅ Good Subgraph Usage:**
```mermaid
flowchart TD
    subgraph "User Layer"
        A("Click Login")
        B("Enter Credentials")
    end
    
    subgraph "Auth Service"
        C("Validate Token")
        D("Check Database")
    end
    
    subgraph "Response"
        E("Set Session")
        F("Redirect User")
    end
    
    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
```

**❌ Overuse of Subgraphs:**
```mermaid
flowchart TD
    subgraph "Single Action"
        A("Click Button")
    end
    
    subgraph "Another Single Action"
        B("Process Click")
    end
    
    subgraph "Final Action"
        C("Show Result")
    end
    
    A --> B --> C
```

## Key Decision Criteria:

Add this simple decision tree to the rules:

**Ask yourself:**
1. **Do I have 7+ nodes?** → Consider subgraphs
2. **Are there natural process boundaries?** → Good candidate for subgraphs
3. **Would removing subgraphs make it clearer?** → Don't use them
4. **Am I grouping just to group?** → Probably unnecessary

**Golden Rule:** If you're unsure whether to use subgraphs, start without them. Add subgraphs only when the flat structure becomes difficult to follow.

This approach ensures subgraphs enhance clarity rather than add unnecessary complexity, maintaining the balance between organization and simplicity that makes diagrams truly effective.

## COMMON SYNTAX ERRORS AND SOLUTIONS:

### Error: STYLE_SEPARATOR with :::

**❌ Incorrect:**
```
A("Process") :::clientProcess
```

**✅ Correct:**
```
A("Process")
class A clientProcess
```

### Error: Missing Quotes in Node Text with Spaces

**❌ Incorrect:**
```
A[Process with spaces]
```

**✅ Correct:**
```
A["Process with spaces"]
A("Process with spaces")
```

### Error: Incorrect ID References

**❌ Incorrect:**
```
Process One --> Process Two
```

**✅ Correct:**
```
A("Process One") --> B("Process Two")
```