---
description: 
globs: 
alwaysApply: true
---
 # Next.js Specific Guidelines

## App Router
- Place page components in app/ directory
- Use layout.js for shared layouts
- Use proper metadata in layout files

## API Routes
- Place API routes in app/api/ directory
- Implement proper error handling
- Return proper status codes
- Validate request body/query params
- Always sanitize params

## Actions
- Place actions in the actions directory
- Prefer to use actions over API routes when possible
- Use API routes for async loading scenarios, third-party integrations, webhooks, streaming responses, or public APIs
- Always use 'use server' directive
- Always sanitize inputs with isomorphic-dompurify before database queries
- Return structured responses for expected errors: `{ success: true }` or `{ error: 'message' }`
- Never throw errors for expected validation failures - return error objects instead
- Use revalidatePath() after successful mutations
- Wrap operations in try-catch blocks for unexpected errors only
- Use useActionState hook on client to handle returned error states

## Client/Server Components
- Use 'use client' directive for client components
- Keep server components as the default when possible
- Don't mix client/server code in the same component
- Extract client-side logic to dedicated components

## NEXTJS Rules
- ALWAYS use Next.js <Image> over <img> - add unoptimized for external URLs or dynamic paths that may not exist, use state-based error handling with conditional rendering for fallbacks.

## Hydration and Keys
- Always use unique, stable keys for list items - prefer object IDs over array indices
- Never use duplicate keys or values as keys - each sibling must have unique key
- Use suppressHydrationWarning={true} only for unavoidable server/client differences (timestamps, etc.)
- Disable SSR with dynamic imports ({ssr: false}) for components with browser-only features
- Wrap components using React Aria or similar libraries in SSRProvider to prevent ID mismatches
- Ensure server and client render identical HTML structure during initial render
- Use useEffect for browser-only code that shouldn't run during SSR
- Validate HTML nesting - avoid invalid combinations like div inside p tags

## Input sanetization (REQUIRED)
- Use isomorphic-dompurify to sanitize URL parameters server-side before using them in:
    - Database queries
    - External API calls
    - File system operations
- Never pass unsanitized data to sensitive operations
- Server-side sanitization is the critical layer and should never be skipped

## JS/JSX Syntax and Formatting
- Always escape special characters in JS/JSX:
  - Use &quot; instead of " (double quotes)
  - Use &apos; instead of ' (single quotes)
  - Use &amp; instead of & (ampersand)
  - Use &lt; instead of < (less than)
  - Use &gt; instead of > (greater than)