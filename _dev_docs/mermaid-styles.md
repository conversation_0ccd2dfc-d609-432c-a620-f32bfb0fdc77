---
description: >-
  Guide for creating consistent, readable, and syntactically correct Mermaid diagrams across the StalkChain documentation.
---

**Target audience:** Technical writers and developers who need to create or modify flowcharts and diagrams in the StalkChain documentation.

# Mermaid Diagram Guide

This guide provides standards and best practices for creating Mermaid diagrams in StalkChain documentation.

## Basic Syntax

All Mermaid diagrams are created within code blocks with the `mermaid` language identifier:

````
```mermaid
flowchart TD
    A --> B
```
````

## Flowchart Types

### Top-Down Flowchart (Default)

```mermaid
flowchart TD
    A("Start") --> B("Process")
    B --> C("End")
```

```
flowchart TD
    A("Start") --> B("Process")
    B --> C("End")
```

### Left-Right Flowchart

```mermaid
flowchart LR
    A("Start") --> B("Process")
    B --> C("End")
```

```
flowchart LR
    A("Start") --> B("Process")
    B --> C("End")
```

## Node Definitions

### Basic Nodes

```
A("Process") 
B["Process"]
C[/"Input/Output"/]
D{{"Data"}}
E{"Decision"}
```

### Recommended Node Types

| Process Type | Shape | Example |
|-------------|-------|---------|
| Process | Rounded Rectangle | `A("Process Text")` |
| Decision | Diamond | `B{"Decision Text"}` |
| Start/End | Stadium | `C([Start/End Text])` |
| Data Store | Cylinder | `D[(Database)]` |
| Input/Output | Parallelogram | `E[/Input/Output/]` |

## Connections

```
A --> B  % Simple arrow
A ---> B  % Extended arrow
A -.-> B  % Dotted arrow
A ==> B  % Thick arrow
A --Label--> B  % Arrow with label
A -->|Label| B  % Alternative label syntax
```

## Styling Nodes (Correct Syntax)

The proper way to apply styles in Mermaid is to define the styles in a `classDef` and then apply the class to nodes using the `class` keyword:

```mermaid
flowchart TD
    A("Client Process")
    B("Server Process")
    C("Database Operation")
    D{"Decision"}
    
    A --> B --> C
    B --> D
    
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    
    class A clientProcess
    class B serverProcess
    class C databaseProcess
    class D decisionPoint
```

**Code:**
```
flowchart TD
    A("Client Process")
    B("Server Process")
    C("Database Operation")
    D{"Decision"}
    
    A --> B --> C
    B --> D
    
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    
    class A clientProcess
    class B serverProcess
    class C databaseProcess
    class D decisionPoint
```

### Applying Style to Multiple Nodes

To apply the same style to multiple nodes, separate them with commas:

```
class A,B,E clientProcess
class C,D serverProcess
```

## StalkChain Standard Colors

For consistency across all diagrams, use these color combinations:

| Process Type | Fill | Stroke | Text |
|-------------|------|--------|------|
| Client-side | #e1f5fe | #01579b | #01579b |
| Server-side | #e8f5e9 | #2e7d32 | #2e7d32 |
| Stripe/External | #fff3e0 | #e65100 | #e65100 |
| Database | #f3e5f5 | #6a1b9a | #6a1b9a |
| Decision | #fff | #455a64 | #455a64 |

## Subgraphs for Grouping

When processes need grouping, use subgraphs:

```mermaid
flowchart TD
    subgraph ClientSide
        A("User Action")
        B("UI Response")
    end
    
    subgraph ServerSide
        C("API Processing")
        D("Database Query")
    end
    
    A --> B
    B --> C
    C --> D
    
    classDef clientGroup fill:#e1f5fe,stroke:#01579b
    classDef serverGroup fill:#e8f5e9,stroke:#2e7d32
    
    class ClientSide clientGroup
    class ServerSide serverGroup
```

**Code:**
```
flowchart TD
    subgraph ClientSide
        A("User Action")
        B("UI Response")
    end
    
    subgraph ServerSide
        C("API Processing")
        D("Database Query")
    end
    
    A --> B
    B --> C
    C --> D
    
    classDef clientGroup fill:#e1f5fe,stroke:#01579b
    classDef serverGroup fill:#e8f5e9,stroke:#2e7d32
    
    class ClientSide clientGroup
    class ServerSide serverGroup
```

## Common Syntax Errors and Solutions

### Error: STYLE_SEPARATOR with :::

**❌ Incorrect:**
```
A("Process") :::clientProcess
```

**✅ Correct:**
```
A("Process")
class A clientProcess
```

### Error: Missing Quotes in Node Text with Spaces

**❌ Incorrect:**
```
A[Process with spaces]
```

**✅ Correct:**
```
A["Process with spaces"]
A("Process with spaces")
```

### Error: Incorrect ID References

**❌ Incorrect:**
```
Process One --> Process Two
```

**✅ Correct:**
```
A("Process One") --> B("Process Two")
```

## Best Practices

1. **Maintain consistent direction** (top-down or left-right) throughout each diagram
2. **Limit color palette** to 3-5 colors with consistent meaning
3. **Keep node text concise** (5-7 words maximum)
4. **Use ID references** (A, B, C) rather than long node names in connections
5. **Add comments** with `%%` to document complex sections
6. **Organize nodes** spatially to minimize crossing lines
7. **Group related elements** with subgraphs only when necessary
8. **Use standard shapes** consistently for similar functions
9. **Apply styling at the end** of the diagram

## Validating Mermaid Syntax

Before committing diagrams to documentation, validate your syntax with the [Mermaid Live Editor](https://mermaid.live/) to catch errors. 