# Solana Tracker API Documentation

## Table of Contents
- [Authentication](#authentication)
- [Token Information](#token-information)
  - [GET /price](#get-price)
  - [GET /tokens/{tokenAddress}/ath](#get-tokenstokenaddressath)
  - [GET /chart/{token}](#get-charttoken)
- [Trade Data](#trade-data)
  - [GET /trades/{tokenAddress}](#get-tradestokenaddress)
  - [GET /trades/{tokenAddress}/by-wallet/{owner}](#get-tradestokenaddressbywalletowner)
- [PnL Data](#pnl-data)
  - [GET /pnl/{wallet}/{token}](#get-pnlwallettoken)
- [Analytics](#analytics)
  - [GET /top-traders/{token}](#get-top-traderstoken)
  - [GET /first-buyers/{token}](#get-first-buyerstoken)
- [Search]
  - [GET /search](#get-search)

## Authentication
Base URL: https://data.solanatracker.io

Include your API Key (available after subscription) in the x-api-key header with each API call.

## Token Information

### GET /price
Gets the current price and market data for a token.

**Query Parameters:**
* `token`: (Required) The token address
* `priceChanges`: (Optional) Returns price change percentages for the token up to 24 hours ago

**Example Request:**
```
GET /price?token=6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN
```

**Response without priceChanges:**
```json
{
  "price": 14.853862871847626,
  "priceQuote": 14.853862871847626,
  "liquidity": 407800098.9546815,
  "marketCap": 14853854317.5876,
  "lastUpdated": 1745749940617
}
```

**Response with priceChanges:**
```json
{
  "price": 14.853862871847626,
  "priceQuote": 14.853862871847626,
  "liquidity": 407800101.53176534,
  "marketCap": 14853854317.5876,
  "lastUpdated": 1745749997013,
  "priceChanges": {
    "1m": {
      "priceChangePercentage": -0.4975124378109467
    },
    "5m": {
      "priceChangePercentage": -0.4975124378109467
    },
    "15m": {
      "priceChangePercentage": -0.4975124378109467
    },
    "30m": {
      "priceChangePercentage": 0.500000000000008
    },
    "1h": {
      "priceChangePercentage": 0.500000000000008
    },
    "2h": {
      "priceChangePercentage": 1.5075125000000027
    },
    "3h": {
      "priceChangePercentage": 3.0377509393765614
    },
    "4h": {
      "priceChangePercentage": -1.4851240690190222
    },
    "5h": {
      "priceChangePercentage": -2.462933164049427
    },
    "6h": {
      "priceChangePercentage": -4.389531960045087
    },
    "12h": {
      "priceChangePercentage": -6.744353885704022
    },
    "24h": {
      "priceChangePercentage": -7.208312324083598
    }
  }
}
```

### GET /tokens/{tokenAddress}/ath
Retrieves the all-time high price of a token (since the data API started recording).

**Response:**
```json
{
  "highest_price": 0.002399892080590551,
  "timestamp": 171924662484
}
```

### GET /chart/{token}
Gets historical OHLCV (Open, High, Low, Close, Volume) data for a token.

**Query Parameters (all optional):**
* `type`: Time interval (1s, 5s, 15s, 1m, 3m, 5m, 15m, 30m, 1h, 2h, 4h, 6h, 8h, 12h, 1d, 3d, 1w, 1mn)
* `time_from`: Start time (Unix timestamp in seconds)
* `time_to`: End time (Unix timestamp in seconds)
* `marketCap`: Return chart for market cap instead of pricing
* `removeOutliers`: Set to false to disable outlier removal, true by default

**Response:**
```json
{
  "oclhv": [
    {
      "open": 15.077786711373587,
      "close": 15.153175644930457,
      "low": 15.077786711373587,
      "high": 15.153175644930457,
      "volume": 35306.452453539365,
      "time": 1745720940
    },
    {
      "open": 15.153175644930457,
      "close": 15.077786711373587,
      "low": 15.077786711373587,
      "high": 15.153175644930457,
      "volume": 17765.527338305634,
      "time": 1745721069
    },
    {
      "open": 15.077786711373587,
      "close": 15.002772847137898,
      "low": 15.002772847137898,
      "high": 15.077786711373587,
      "volume": 3970.4328545729095,
      "time": 1745721301
    }
    // Additional data points omitted for brevity
  ]
}
```

## Trade Data

### GET /trades/{tokenAddress}
Gets the latest trades for a token across all pools.

**Query Parameters:**
* `cursor`: Unix timestamp to look before
* `showMeta`: Get token metadata
* `parseJupiter`: Parse Jupiter transactions
* `hideArb`: Hide Arbitrage transactions

**Response:**
```json
{
    "trades": [
        {
            "tx": "2FxCUeEDQMtKAZtctMqCkPLM7tHPaGJ4hL7nR6VQnxgLUTaxvRYVjHY6nyWD61NmuN9vLKzHZzWvUK71KBu4xGYb",
            "amount": 164388.579723,
            "priceUsd": 0.0002468380661718994,
            "volume": 40.57735911957043,
            "volumeSol": 0.32487869,
            "type": "sell",
            "wallet": "4fFn7mVd8Bfa5LSmbXjHZnwnPkDQnugSycAbFG3caDVV",
            "time": 1743274860158,
            "program": "jupiter",
            "pools": [
                "********************************************",
                "*******************************************",
                "GS4CU59F31iL7aR2Q8zVS8DRrcRnXX1yjQ66TqNVQnaR"
            ]
        },
        {
            "tx": "46CP5Fu8wiLfj8nPu9afN2ByGAAmgiMMq6TQTWqMtWKdCgysYocx97YcU8uaHZL73zrSgYWZKnaGu9rp4QosS1EF",
            "amount": 176227.18061500788,
            "priceUsd": 0.0002488434720999679,
            "volume": 43.85298350262672,
            "volumeSol": 0.3499999999999659,
            "type": "buy",
            "wallet": "7ACsEkYSvVyCE5AuYC6hP1bNs4SpgCDwsfm3UdnyPERk",
            "time": 1743274728903,
            "program": "raydium",
            "pools": [
                "********************************************"
            ]
        }
    ],
    "nextCursor": 1743274728903,
    "hasNextPage": true
}
```

### GET /trades/{tokenAddress}/by-wallet/{owner}
Gets the latest trades for a specific token and wallet address.

**Query Parameters:**
* `cursor`: Unix timestamp to look before
* `showMeta`: Get token metadata
* `parseJupiter`: Parse Jupiter transactions
* `hideArb`: Hide Arbitrage transactions

**Response:**
```json
{
  "trades": [
    {
      "tx": "Transaction Signature",
      "amount": 1000,
      "priceUsd": 0.1,
      "volume": 100,
      "type": "buy",
      "wallet": "WalletAddress",
      "time": 1723726185254,
      "program": "jupiter"
    }
  ],
  "nextCursor": 1723726185254,
  "hasNextPage": true
}
```

## PnL Data

### GET /pnl/{wallet}/{token}
Gets profit and loss data for a specific wallet and token combination.

**Path Parameters:**
* `wallet`: (Required) The wallet address
* `token`: (Required) The token address

**Query Parameters:**
* `holdingCheck`: (Optional, Default: false) Set to true for accurate current holding check. This makes the response slower.

**Example Request:**
```
GET /pnl/4fFn7mVd8Bfa5LSmbXjHZnwnPkDQnugSycAbFG3caDVV/6p6xgHyF7AeE6TZkSmFsko444wqoP15icUSqi2jfGiPN?holdingCheck=false (Omitted from the request)
```

**Success Response:**
```json
{
  "holding": 1895934.1275479998,
  "held": 3158215.14743,
  "sold": 1262281.019882,
  "sold_usd": 356033.51360391267,
  "realized": -498292.567431457,
  "unrealized": 1249768.845556248,
  "total": 751476.278124791,
  "total_sold": 356033.51360391267,
  "total_invested": 2125922.4293494876,
  "average_buy_amount": 73307.66997756854,
  "current_value": 2521365.193870366,
  "cost_basis": 0.670696481401,
  "first_buy_time": 1734276916383,
  "last_buy_time": 1744303868268,
  "last_sell_time": 1744008264934,
  "last_trade_time": 1744303868268,
  "buy_transactions": 29,
  "sell_transactions": 8,
  "total_transactions": 37
}
```

**Error Response (Invalid wallet, invalid token, or no PnL found):**
```json
{
  "error": "Failed to execute 'json' on 'Response': Unexpected end of JSON input",
  "timestamp": "2025-06-11T07:03:44.798Z"
}
```

## Analytics

### GET /top-traders/{token}
Gets top 100 traders by PnL for a token.

**Response:**
```json
[
    {
        "wallet": "5cJ59MEVwdGGfPgSQMm2xoiacrh9U3edEWLeQDYRLavu",
        "held": 41549988.501104,
        "sold": 41549988.501104,
        "holding": 0,
        "realized": 663.8706273151051,
        "unrealized": 0,
        "total": 663.8706273151051,
        "total_invested": 345.5038083813247
    },
    {
        "wallet": "4ocG4Vvbv2Dsam2CQaU5etEbvESvX5oZQb5NjTEaJK27",
        "held": 51095238.095238,
        "sold": 51095238.095238,
        "holding": 0,
        "realized": 588.6414311400356,
        "unrealized": 0,
        "total": 588.6414311400356,
        "total_invested": 188.25865967784858
    },
    {
        "wallet": "FJCZZ9fodGG9NSqSbAJadgGVyz4sXDiJX77rYyq7m4ua",
        "held": 23467238.92106,
        "sold": 23467238.92106,
        "holding": 0,
        "realized": 560.522503566342,
        "unrealized": 0,
        "total": 560.522503566342,
        "total_invested": 184.350034205613
    }
]
```

### GET /first-buyers/{token}
Retrieves the first 100 buyers of a token (since API started recording data) with PnL data for each wallet.

**Response:**
```json
[
    {
        "wallet": "4ocG4Vvbv2Dsam2CQaU5etEbvESvX5oZQb5NjTEaJK27",
        "first_buy_time": 1743274275720,
        "last_transaction_time": 1743274299334,
        "held": 51095238.095238,
        "sold": 51095238.095238,
        "holding": 0,
        "realized": 588.6414311400356,
        "unrealized": 0,
        "total": 588.6414311400356,
        "total_invested": 188.25865967784858,
        "sold_usd": 776.9000908178842,
        "total_transactions": 2,
        "buy_transactions": 1,
        "sell_transactions": 1,
        "average_buy_amount": 188.25865967784858,
        "current_value": 0,
        "cost_basis": 0
    },
    {
        "wallet": "BaPQMxqmVhe47By6JqushUCfZ9ufHkKr8qbyH3tJ6y56",
        "first_buy_time": 1743274275767,
        "last_transaction_time": 1743274279500,
        "held": 19083014.894044,
        "sold": 19083014.894044,
        "holding": 0,
        "realized": 12.499577915444078,
        "unrealized": 0,
        "total": 12.499577915444078,
        "total_invested": 75.23111581721368,
        "sold_usd": 87.73069373265776,
        "total_transactions": 2,
        "buy_transactions": 1,
        "sell_transactions": 1,
        "average_buy_amount": 75.23111581721368,
        "current_value": 0,
        "cost_basis": 0
    }
]
```

## Search

### GET /search
Provides a flexible search interface for pools and tokens with comprehensive filtering options and pagination.

**Query Parameters:**
* `query`: (Required) Search term for token symbol, name, or address
* `page`: (Optional, Default: 1) Page number for pagination
* `limit`: (Optional, Default: 100) Number of results per page
* `sortBy`: (Optional, Default: createdAt) Field to sort by
* `sortOrder`: (Optional, Default: desc) Sort order: asc or desc
* `showAllPools`: (Optional, Default: false) Return all pools for a token if enabled
* `showPriceChanges`: (Optional) Include price change data in response
* `lpBurn`: (Optional) LP token burn percentage
* `market`: (Optional) Market identifier
* `freezeAuthority`: (Optional) Freeze authority address
* `mintAuthority`: (Optional) Mint authority address
* `deployer`: (Optional) Deployer address
* `status`: (Optional) Token status
* `minBuys`: (Optional) Minimum number of buy transactions
* `maxBuys`: (Optional) Maximum number of buy transactions
* `minSells`: (Optional) Minimum number of sell transactions
* `maxSells`: (Optional) Maximum number of sell transactions
* `minTotalTransactions`: (Optional) Minimum total number of transactions
* `maxTotalTransactions`: (Optional) Maximum total number of transactions
* `minVolume`: (Optional) Minimum volume in USD (for default timeframe)
* `maxVolume`: (Optional) Maximum volume in USD (for default timeframe)
* `volumeTimeframe`: (Optional) Timeframe for volume filtering (e.g., '24h')
* `minLiquidity`: (Optional) Minimum liquidity in USD
* `maxLiquidity`: (Optional) Maximum liquidity in USD
* `minMarketCap`: (Optional) Minimum market cap in USD
* `maxMarketCap`: (Optional) Maximum market cap in USD
* `minCreatedAt`: (Optional) Minimum creation date in unix time (ms)
* `maxCreatedAt`: (Optional) Maximum creation date in unix time (ms)

**Example Response:**
```json
{
  "status": "success",
  "data": [
    {
      "name": "Popcat",
      "symbol": "POPCAT",
      "mint": "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr",
      "decimals": 9,
      "image": "https://image.solanatracker.io/proxy?url=https%3A%2F%2Farweave.net%2FA1etRNMKxhlNGTf-gNBtJ75QJJ4NJtbKh_UXQTlLXzI",
      "holders": 140152,
      "jupiter": true,
      "verified": true,
      "liquidityUsd": 13787152.145922422,
      "marketCapUsd": 520225158.9268556,
      "priceUsd": 0.5308745284377849,
      "lpBurn": 98,
      "market": "raydium",
      "freezeAuthority": null,
      "mintAuthority": null,
      "poolAddress": "FRhB8L7Y9Qq41qZXYLtC2nw8An1RJfLLxRF2x9RwLLMo",
      "totalBuys": 634771,
      "totalSells": 340573,
      "totalTransactions": 975344,
      "volume_5m": 4807,
      "volume": 406327770.4654644,
      "volume_15m": 23944,
      "volume_30m": 81039,
      "volume_1h": 148440,
      "volume_6h": 562855,
      "volume_12h": 1406755,
      "volume_24h": 2194123
    },
    {
      "name": "Popcat 2.0",
      "symbol": "OGPOP",
      "mint": "9ju3YFcvJP5uaeMYPYxTEqRzepk4KYnwjBd1BCurpump",
      "decimals": 6,
      "image": "https://image.solanatracker.io/proxy?url=https%3A%2F%2Fimage-cdn.solana.fm%2Fimages%2F%3FimageUrl%3Dhttps%3A%2F%2Fcf-ipfs.com%2Fipfs%2FQmPg9QhNvXEB2zr7Jc49YzUCkZEaEJ4nYbwPWF4EGyaNQT",
      "holders": 637,
      "jupiter": true,
      "verified": true,
      "liquidityUsd": 20258.21011218379,
      "marketCapUsd": 19477.060873201975,
      "priceUsd": 0.00001948255734503629,
      "lpBurn": 100,
      "market": "raydium",
      "freezeAuthority": null,
      "mintAuthority": null,
      "poolAddress": "9VsFSZS59Y9joAibVQWf8zZX2v7aFH4AX97mhntoZrd2",
      "totalBuys": 710,
      "totalSells": 745,
      "totalTransactions": 1455,
      "volume_5m": 0,
      "volume": 97543.40597977741,
      "volume_15m": 0,
      "volume_30m": 0,
      "volume_1h": 0,
      "volume_6h": 96,
      "volume_12h": 182,
      "volume_24h": 283
    }
    // More results here
  ],
  "total": 1396,
  "pages": 280,
  "page": 1
}
```