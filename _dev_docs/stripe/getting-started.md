---
description: >-
  A developer guide for working with Stripe integration in StalkChain, covering setup, testing, webhooks, and payment flows.
---

**Target audience:** Backend and frontend developers who need to implement, test, or modify Stripe payment functionality in the StalkChain application.

# Stripe Integration: Getting Started

This guide covers the essentials of working with <PERSON><PERSON> in the StalkChain application, including local setup, testing, and understanding the payment flow.

## Environment Setup

### Prerequisites

- Stripe account (test mode for development)
- Stripe CLI installed locally
- StalkChain local development environment

### Environment Variables

```dotenv
# Public key (safe to expose in browser)
NEXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_...

# Private key (server-side only)
STRIPE_PRIVATE_KEY=sk_test_...

# Webhook signing secret
STRIPE_WEBHOOK_SECRET=whsec_...

# For product management
STRIPE_DEGEN_PLAN_PRODUCT_ID=prod_...
STRIPE_LEGEND_PLAN_PRODUCT_ID=prod_...
```

## Local Testing

### Testing Dashboard

For local testing, use the dashboard design environment:

```bash
# Navigate to the dashboard design page
http://localhost:3000/dashboard/design/checkout
```

This environment contains pre-configured checkout buttons for testing different subscription scenarios.

### Staging Environments

New staging environments can be created for testing Stripe integration in isolation from production. For details on how to set up and use staging environments, refer to the **[staging]** <!-- Placeholder for link to staging guide -->.

### Stripe CLI for Webhook Testing

1. Start webhook forwarding:

```bash
stripe listen --forward-to http://localhost:3000/api/stripe-incoming-webhooks
```

2. Trigger test events:

```bash
# Test a successful payment
stripe trigger checkout.session.completed

# Test other events
stripe trigger payment_intent.succeeded
stripe trigger customer.subscription.updated
```

## Architecture Overview

### Payment Flow

```mermaid
flowchart TD
    A("Client: Checkout Button") --> B("Server: Create Checkout Session")
    B --> C{"Redirect to Stripe"}
    C --> D("Customer Completes Payment")
    D --> E("Stripe Webhook: checkout.session.completed")
    E --> F("Process Payment")
    F --> G("Generate Activation Token")
    G --> H("Send Welcome Email")
    H --> I("Customer Activates Account")

    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    
    class A clientProcess
    class B,F,G,H serverProcess
    class C,D,E stripeProcess
    class I clientProcess
```

## Key Components

### Client-Side Integration

The `StripeCheckoutButton` component handles the client-side portion of the checkout flow:

```jsx
<StripeCheckoutButton
  userId={user.id}
  priceId="price_1234567890"
  planName="PRO"
  billingPeriodValue={1}
  billingPeriodUnit="month"
  trialDays={0}
/>
```

### Server Actions

Server-side Stripe operations are handled by server actions in `actions/checkout-v2/stripe.js`:

```javascript
// Create a checkout session
const response = await createStripeCheckout({
  priceId,
  userId,
  planName,
  referralId,
  billingPeriodValue,
  billingPeriodUnit,
  trialDays
});

// The response contains a URL to redirect to
if (response.success) {
  window.location.href = response.url;
}
```

### Webhook Processing

Stripe webhooks are handled by the `/api/stripe-incoming-webhooks` endpoint, which processes various event types:

- `checkout.session.completed` - When a payment is successful
- `customer.subscription.updated` - When a subscription details change
- `customer.subscription.deleted` - When a subscription is canceled

## Account Activation Flow

After checkout, customers need to activate their accounts:

1. Stripe checkout completion triggers `checkout.session.completed` webhook
2. System generates activation token and sends welcome email
3. Customer clicks activation link
4. Customer logs in/creates account with Privy
5. System links subscription to user account

## Important Data Structures

### Checkout Session

Example response from `getCheckoutSession`:

```json
{
  "success": true,
  "data": {
    "customer": "cus_1234567890",
    "subscription": "sub_1234567890",
    "status": "complete",
    "email": "<EMAIL>",
    "expiresAt": "2023-12-31T23:59:59Z",
    "billingInterval": "",
    "billingPeriodValue": 1,
    "billingPeriodUnit": "month",
    "planName": "PRO",
    "userId": "user_1234567890",
    "sessionId": "cs_1234567890"
  }
}
```

### Activation Token

```json
{
  "token": "a1b2c3d4e5f6...",
  "sessionId": "cs_1234567890",
  "subscriptionId": "sub_1234567890",
  "customerId": "cus_1234567890",
  "email": "<EMAIL>",
  "planName": "PRO",
  "billingPeriodValue": 1, 
  "billingPeriodUnit": "month",
  "createdAt": "2023-10-15T12:00:00Z",
  "expiresAt": "2023-10-22T12:00:00Z",
  "used": false
}
```

## Stripe Best Practices

1. **Test Thoroughly in Test Mode**: Always use Stripe's test mode before processing real payments.

2. **Handle Idempotency**: Stripe operations should be idempotent to prevent duplicate charges.

3. **Log All Events**: Store webhook events for debugging and audit purposes.

4. **Verify Webhooks**: Always verify webhook signatures to prevent fraud.

5. **Handle Failures Gracefully**: Implement proper error handling for failed payments.

## Troubleshooting

### Common Issues

1. **Webhook Errors**: Ensure the webhook signing secret is correct and the endpoint is accessible.

2. **Payment Failures**: Check Stripe Dashboard for detailed error messages.

3. **Session Expiration**: Checkout sessions expire after 24 hours.

### Debug Tools

- Stripe Dashboard Events log
- StalkChain webhook logs in MongoDB (`stripe.webhook_events` collection)
- Stripe CLI for local webhook testing

## Further Reading

- [Stripe API Documentation](https://stripe.com/docs/api)
- [Stripe Webhooks Guide](https://stripe.com/docs/webhooks)
- [Stripe Testing Documentation](https://stripe.com/docs/testing) 