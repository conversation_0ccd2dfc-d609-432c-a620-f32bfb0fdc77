---
description: >-
  Technical documentation of StalkChain's Stripe checkout process implementation, covering server-side session creation, subscription management, and client integration.
---

**Target audience:** Backend developers who need to implement, modify, or debug the Stripe checkout flow, with an emphasis on server-side components.

# Stripe Checkout Process

## Overview

The StalkChain checkout process handles subscription payments through Stripe's hosted checkout solution, followed by our custom account activation system. This document focuses primarily on the server-side implementation.

```mermaid
flowchart TD
    %% Define styles for different process types
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    
    A("Client: StripeCheckoutButton") --> B("Server: createStripeCheckout")
    B --> C{"Redirect to Stripe"}
    C --> D("Customer Completes Payment")
    D --> E("Stripe Webhook: checkout.session.completed")
    D --> F("Redirect to Success Page")
    
    %% Webhook Flow
    E --> G("Process Payment Data")
    G --> H("Generate Activation Token")
    H --> I("Send Welcome Email")
    I --> J("Customer Activates via Email")
    
    %% Direct Activation Flow
    F --> K("Success Page with session_id")
    K --> L("Verify Session & Direct Activation")
    
    %% Merge flows at account setup
    J --> M("Account Setup & Role Assignment")
    L --> M
    M --> N("Redirect to Dashboard")

    class A,F,K,J clientProcess
    class B,G,H,I,L,M serverProcess
    class C,D,E stripeProcess
    class N clientProcess
```

## Client-Side Implementation

The `StripeCheckoutButton` component (found in `components/UI/Buttons/StripeCheckoutButton.js`) initiates the checkout process by calling our server-side implementation. 

```jsx
// components/UI/Buttons/StripeCheckoutButton.js
import { useState } from 'react';
import { Button } from '@heroui/react';
import { createStripeCheckout } from '@/actions/checkout-v2/stripe';

export function StripeCheckoutButton({
  userId,
  priceId,
  planName = "PRO",
  billingPeriodValue = 1,
  billingPeriodUnit = "month",
  trialDays = 0
}) {
  const [isLoading, setIsLoading] = useState(false);
  
  const handleCheckout = async () => {
    setIsLoading(true);
    
    try {
      // Get referral ID from PromoteKit if available
      const referralId = window?.promotekitReferral || null;
      
      const result = await createStripeCheckout({
        priceId,
        userId,
        planName,
        referralId,
        billingPeriodValue,
        billingPeriodUnit,
        trialDays
      });
      
      if (result.success && result.url) {
        // Redirect to Stripe Checkout
        window.location.href = result.url;
      } else {
        console.error('Failed to create checkout session:', result.error);
        // Handle error here
      }
    } catch (error) {
      console.error('Error during checkout:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  return (
    <Button
      onClick={handleCheckout}
      isLoading={isLoading}
      color="primary"
      size="lg"
    >
      Subscribe Now
    </Button>
  );
}
```

This component handles:
- Collecting subscription parameters
- Retrieving referral information if available
- Calling the server action
- Redirecting to Stripe's hosted checkout

For more detailed information on frontend integration, refer to the [Frontend Integration Guide] <!-- Placeholder for future frontend documentation -->.

## Server-Side Implementation

### Creating Checkout Sessions

The server-side implementation begins with the `createStripeCheckout` server action in `actions/checkout-v2/stripe.js`:

```javascript
// actions/checkout-v2/stripe.js
import Stripe from 'stripe';
import { sanitize } from 'isomorphic-dompurify';

const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
  apiVersion: '2024-09-30.acacia',
});

// Sanitization utility defined in the file
const sanitizeInput = (input) => {
  if (input === null || input === undefined) return input;
  
  // Convert numbers to strings for sanitization
  const stringValue = typeof input === 'number' ? String(input) : input;
  
  // Return sanitized input
  return sanitize(stringValue);
};

export async function createStripeCheckout({ 
  priceId, 
  userId = null, 
  planName = 'PRO',
  referralId = null,
  billingPeriodValue = 1,
  billingPeriodUnit = 'month',
  trialDays = 0
}) {
  try {
    // Sanitize all inputs before using them
    const sanitizedPriceId = sanitizeInput(priceId);
    const sanitizedUserId = sanitizeInput(userId);
    const sanitizedPlanName = sanitizeInput(planName);
    const sanitizedReferralId = sanitizeInput(referralId);
    const sanitizedBillingPeriodValue = parseInt(sanitizeInput(billingPeriodValue)) || 1;
    const sanitizedBillingPeriodUnit = sanitizeInput(billingPeriodUnit);
    const sanitizedTrialDays = parseInt(sanitizeInput(trialDays)) || 0;
    
    // Prepare metadata with sanitized values
    const metadata = {
      planName: sanitizedPlanName,
      billingPeriodValue: sanitizedBillingPeriodValue,
      billingPeriodUnit: sanitizedBillingPeriodUnit,
    };

    // Add user ID to metadata if available
    if (sanitizedUserId) {
      metadata.userId = sanitizedUserId;
    }

    // Create checkout session options
    const sessionOptions = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: sanitizedPriceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.SITE_URL}/`,
      metadata,
      client_reference_id: sanitizedReferralId || undefined,
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
      consent_collection: {
        terms_of_service: 'required',
      },
      custom_text: {
        terms_of_service_acceptance: {
          message: 'I agree to the [Terms of Service](http://stalkchain.com/terms-conditions)',
        },
      },
    };

    // Add trial period if specified
    if (sanitizedTrialDays > 0) {
      sessionOptions.subscription_data = {
        trial_period_days: sanitizedTrialDays,
      };
    }

    // Create a checkout session with Stripe
    const session = await stripe.checkout.sessions.create(sessionOptions);

    return {
      success: true,
      url: session.url,
    };
  } catch (error) {
    console.error('Error creating Stripe checkout session:', error);
    return {
      success: false,
      error: error.message || 'Failed to create checkout session',
    };
  }
}
```

### Session Parameters and Options

When creating a Stripe checkout session, several important parameters must be configured correctly:

#### Required Parameters

| Parameter | Description | Importance |
|-----------|-------------|------------|
| `payment_method_types` | List of payment methods to accept | Determines available payment options |
| `line_items` | Products/prices to include in checkout | Defines what the customer is purchasing |
| `mode` | Type of checkout (`subscription` in our case) | Controls behavior of the session |
| `success_url` | URL to redirect after successful payment | Must include `{CHECKOUT_SESSION_ID}` parameter |
| `cancel_url` | URL to redirect if customer cancels | Provides a way back to your site after cancellation |
| `metadata` | Additional data to store with the session | Critical for tracking subscription details |
| `consent_collection` | Collection of consent from customers | Required for Terms of Service acceptance |

#### Customizing URLs

Both the success and cancel URLs can be customized to fit your specific needs:

```javascript
// Default implementation
success_url: `${process.env.SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
cancel_url: `${process.env.SITE_URL}/`,

// Custom implementation examples (these are examples, not how it is implemented)
success_url: `${process.env.SITE_URL}/checkout/success/${planName.toLowerCase()}?session_id={CHECKOUT_SESSION_ID}`,
cancel_url: `${process.env.SITE_URL}/pricing?canceled=true&plan=${planName.toLowerCase()}`,
```

The cancel URL can include query parameters to track abandoned checkouts or to display specific messaging to customers who cancel. For example, you might want to:

1. Redirect to a specific marketing page
2. Show a special offer to recover the abandoned checkout
3. Collect feedback on why they canceled
4. Return them to the pricing page with their selected plan pre-selected

#### Critical Metadata Fields

The `metadata` object stores crucial information about the subscription:

```javascript
metadata: {
  planName: "PRO",              // Plan name (PRO or Legend)
  billingPeriodValue: 1,        // Numeric billing period (e.g., 1, 3, 12)
  billingPeriodUnit: "month",   // Unit of billing period (month, year)
  userId: "user123",            // User ID for account linking (if available)
}
```

#### Terms of Service Requirements

StalkChain requires customers to explicitly accept the Terms of Service during checkout. This is configured using:

```javascript
consent_collection: {
  terms_of_service: 'required',
},
custom_text: {
  terms_of_service_acceptance: {
    message: 'I agree to the [Terms of Service](http://stalkchain.com/terms-conditions)',
  },
},
```

This ensures:
1. Customers must check a box agreeing to the terms
2. The terms link to our official Terms of Service page
3. The consent is recorded by Stripe as part of the checkout session

#### Security Rules for Session Creation

1. **Never store user ID in `client_reference_id`**
   - This field is exposed in logs and webhooks
   - Only use for referral/affiliate tracking

2. **Always sanitize all inputs**
   - Use `isomorphic-dompurify` for all user-provided data
   - Type-cast values where appropriate (e.g., parse integers)

3. **Include comprehensive metadata**
   - Store all necessary subscription details
   - This data will be used throughout the activation flow

### Handling Success and Failures

The server action returns a standardized response structure:

#### Success Response

```json
{
  "success": true,
  "url": "https://checkout.stripe.com/pay/cs_test_..."
}
```

The frontend uses this URL to redirect the user to Stripe's hosted checkout page.

#### Error Response

```json
{
  "success": false,
  "error": "Invalid price ID provided"
}
```

The frontend should display appropriate error messages to the user.

#### Webhook Processing

After checkout completion, Stripe sends a webhook to our endpoint at `/api/stripe-incoming-webhooks`. This endpoint:

1. Validates the webhook signature
2. Processes the event based on its type
3. Returns a success response to Stripe

For detailed information on webhook processing, refer to the [Webhook System Documentation](_dev_docs/stripe/webhook-system.md).

### Direct and Email-Based Activation Flows

Our system supports two separate paths for account activation:

#### Email-Based Activation

When a checkout is completed, our webhook handler:
1. Creates an activation token for the subscription
2. Sends a welcome email with an activation link
3. When the user clicks the link, they're prompted to log in (if not already)
4. The system verifies the token and activates their account

#### Direct Activation from Success Page

When a user completes payment and is redirected to our success page:
1. The success page receives the `session_id` parameter from Stripe
2. The page displays a UI for immediate account activation
3. When activated, the system verifies the session ID
4. If valid, it links the subscription to the user's account
5. User is redirected to the dashboard with their new subscription active

This dual-flow approach ensures users can activate their account either immediately after payment or later via email, providing flexibility and reducing friction.

### Direct Payment Flow

In some cases, customers may access Stripe checkout directly through a payment link rather than through our website's checkout flow. This scenario presents some challenges:

#### Direct Payment Link Challenges

1. **Missing Metadata**: Direct payments bypass our frontend, so they lack our custom metadata (planName, userId, etc.)
2. **Unknown User Context**: We don't know which user is making the payment
3. **Product Identification**: We need to determine which product/plan the customer purchased
4. **Account Linking**: We need a mechanism to link the payment to the correct user account

#### How We Handle Direct Payments

```javascript
// actions/stripe-webhooks/checkout/handler.js
async function fetchSubscriptionData(parsedData) {
  try {
    // Fetch the subscription details from Stripe
    const subscription = await stripe.subscriptions.retrieve(parsedData.subscription);
    
    if (subscription) {
      // Set the start and expiration dates from the subscription period
      if (subscription.current_period_start) {
        parsedData.startDate = unixTimestampToDate(subscription.current_period_start);
      }
      
      if (subscription.current_period_end) {
        parsedData.expirationDate = unixTimestampToDate(subscription.current_period_end);
      }
      
      // Extract billing period details from the plan
      if (subscription.items?.data?.[0]?.plan) {
        const plan = subscription.items.data[0].plan;
        
        parsedData.billingPeriodValue = plan.interval_count || 1;
        parsedData.billingPeriodUnit = plan.interval || '';
        
        // Get the product details to determine role ID
        const productId = plan.product;
        if (productId) {
          const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
          
          // Find the plan in our pricing data that matches this product ID
          const matchingPlan = pricingData.plans.find(plan => 
            plan.productIds && plan.productIds[environment] === productId
          );
          
          if (matchingPlan) {
            // Update the plan name and set the role ID
            parsedData.plan = matchingPlan.name;
            
            // Determine role ID based on plan name
            if (matchingPlan.name.toLowerCase() === 'legend') {
              parsedData.roleId = 3;  // Legend membership
            } else if (matchingPlan.name.toLowerCase() === 'pro') {
              parsedData.roleId = 4;  // Pro membership
            }
          }
        }
      }
    }
    
    return parsedData;
  } catch (error) {
    console.error(`Error fetching subscription details: ${error.message}`);
    return parsedData;  // Return original data if fetch fails
  }
}
```

Our system handles direct payments by:

1. **Fetching Additional Data**: When we receive a webhook with a subscription but limited metadata, we fetch additional details from Stripe
2. **Product Mapping**: We map Stripe product IDs to our internal plans and roles
3. **Email-Based Activation**: We generate an activation token and send it to the email used for payment
4. **Account Creation**: When the user activates via email, we either link to their existing account or prompt them to create one
5. **Role Assignment**: We assign the appropriate role based on the product they purchased

This ensures that direct payments are properly processed and customers receive access to what they purchased, regardless of how they entered the checkout flow.

## Subscription Management

### Billing Periods and Trial Days

StalkChain supports flexible billing periods and optional trial periods:

#### Billing Period Configuration

The billing period is defined by two parameters:
- `billingPeriodValue`: A numeric value (e.g., 1, 3, 12)
- `billingPeriodUnit`: The unit of time (`day`, `week`, `month`, or `year`)

This flexible approach allows for various subscription durations:
- Monthly: `value=1, unit="month"`
- Quarterly: `value=3, unit="month"`
- Annual: `value=1, unit="year"`

#### Trial Period Configuration

Trial periods can be added to any subscription by setting the `trialDays` parameter:

```javascript
// Add trial period if specified
if (sanitizedTrialDays > 0) {
  sessionOptions.subscription_data = {
    trial_period_days: sanitizedTrialDays,
  };
}
```

Key considerations for trials:
- Trials require a payment method but don't charge until the trial ends
- Trial information is preserved in webhook events
- Stripe handles automatic conversion to paid after trial end

### Pricing Configuration

StalkChain's pricing configuration uses Stripe's Price API objects to define subscription pricing:

#### Price Structure

Each price object in Stripe includes:
- `product`: Link to the product (PRO or Legend plan)
- `unit_amount`: Amount in cents
- `currency`: Currency code (USD)
- `recurring`: Details of the recurring payment
  - `interval`: Time unit (month, year)
  - `interval_count`: Number of intervals between payments

#### Environment-Specific Price IDs

We use different price IDs for development and production:

```javascript
// Example pricing data structure
const pricingData = {
  "plans": [
    {
      "name": "PRO",
      "priceIds": {
        "dev": {
          "monthly": "price_dev_pro_monthly",
          "yearly": "price_dev_pro_yearly"
        },
        "prod": {
          "monthly": "price_prod_pro_monthly",
          "yearly": "price_prod_pro_yearly"
        }
      }
    },
    {
      "name": "LEGEND",
      // Similar structure for Legend plan
    }
  ]
}
```

For more information on subscription lifecycle management, refer to the [Subscription Management Documentation](_dev_docs/stripe/subscription-management.md).

## Testing the Checkout Process

For local testing, use the dashboard design environment:

```bash
# Navigate to the dashboard design page
http://localhost:3000/dashboard/design/checkout
```

This environment contains pre-configured checkout buttons that test different subscription scenarios.

For information on webhook testing and troubleshooting, refer to the [Troubleshooting Guide](_dev_docs/stripe/troubleshooting.md). 