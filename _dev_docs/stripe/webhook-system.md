---
description: >-
  Comprehensive guide to StalkChain's Stripe webhook system, covering configuration, event processing pipeline, and handlers for various event types.
---

**Target audience:** Backend developers who need to implement, debug, or extend the Stripe webhook infrastructure in StalkChain.

# Stripe Webhook System

## Overview

StalkChain's webhook system provides an asynchronous mechanism for processing Stripe events such as completed checkouts, subscription changes, and payment confirmations. This architecture ensures reliable payment tracking and account management across various scenarios.

```mermaid
flowchart TD
    %% Define styles for different process types
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    classDef groupStyle fill:none,stroke:#ccc,stroke-width:1px,stroke-dasharray:5 5
    
    subgraph StripeSystem["Stripe System"]
        A("Stripe Event Occurs")
        B("Stripe Sends Webhook")
    end
    
    subgraph WebhookProcessing["Webhook Processing"]
        C["Webhook Endpoint<br>/api/stripe-incoming-webhooks"]
        D{"Verify<br>Signature"}
        E["Return 400 Error"]
        F["Log Event in Database"]
    end
    
    subgraph EventRouting["Event Routing"]
        G{"Route Event<br>by Type"}
        
        H["Checkout<br>Handler"]
        I["Customer<br>Handler"]
        J["Payment<br>Handler"]
        K["Billing Portal<br>Handler"]
        L["Log Unhandled<br>Event"]
    end
    
    subgraph CheckoutProcessing["Checkout Processing"]
        M["Process<br>Checkout"]
        Q["Create Activation<br>Token"]
        R["Send Welcome<br>Email"]
    end
    
    subgraph SubscriptionProcessing["Subscription Processing"]
        N["Process<br>Subscription"]
        S["Update<br>Membership"]
        T["Update User<br>Role"]
    end
    
    subgraph OtherProcessing["Other Processing"]
        O["Process<br>Payment"]
        P["Process<br>Portal Event"]
    end
    
    A --> B
    B --> C
    C --> D
    D -->|Invalid| E
    D -->|Valid| F
    F --> G
    
    G -->|checkout.*| H
    G -->|customer.*| I
    G -->|payment_intent.*| J
    G -->|billing_portal.*| K
    G -->|Other| L
    
    H --> M
    I --> N
    J --> O
    K --> P
    
    M --> Q
    Q --> R
    N --> S
    S --> T
    
    class A,B stripeProcess
    class C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S,T serverProcess
    class F,L,Q databaseProcess
    class D decisionPoint
    class StripeSystem,WebhookProcessing,EventRouting,CheckoutProcessing,SubscriptionProcessing,OtherProcessing groupStyle
```

## Supported Webhook Events

StalkChain currently processes the following Stripe webhook events:

| Event Type | Description | Processing Actions |
|------------|-------------|-------------------|
| **Checkout Events** | | |
| `checkout.session.completed` | Payment was successful and checkout completed | Store session data, create activation token, send welcome email |
| `checkout.session.async_payment_succeeded` | Asynchronous payment method completed successfully | Log the event for tracking |
| `checkout.session.async_payment_failed` | Asynchronous payment method failed | Log the event for tracking |
| `checkout.session.expired` | Checkout session expired before completion | Log the event for tracking |
| **Customer Events** | | |
| `customer.subscription.updated` | Subscription details were modified | Update membership expiration date, update role if plan changed |
| `customer.subscription.deleted` | Subscription was canceled | Set role to free tier or keep current role based on cancellation type |
| **Billing Portal Events** | | |
| `billing_portal.session.created` | Customer accessed their billing portal | Log portal access for analytics |
| **Payment Events** | | |
| `payment_intent.succeeded` | Payment attempt completed successfully | Log payment completion |
| `payment_intent.payment_failed` | Payment attempt failed | Log payment failure |
| `charge.succeeded` | Payment was successfully charged | Log charge event |
| `charge.failed` | Charge attempt failed | Log charge failure |

## Webhook Configuration

### Local Development Setup

For local development, the Stripe CLI is used to forward webhook events to your local server:

1. **Install the Stripe CLI**:
   Follow the [official instructions](https://stripe.com/docs/stripe-cli) to install the CLI on your development machine.

2. **Start webhook forwarding**:
   ```bash
   stripe listen --forward-to http://localhost:3000/api/stripe-incoming-webhooks
   ```
   
   This command starts a listener that forwards webhook events from Stripe to your local development server.

3. **Get webhook signing secret**:
   The CLI will provide a webhook signing secret that looks like this:
   ```
   whsec_8719cddb2c551b4eb604fc6928c770f80548bcab8d51be372a8f9d84bf8dca0e
   ```
   
   In local development, this fixed secret is used for signature verification.

4. **Trigger test events**:
   ```bash
   # Test a successful checkout completion
   stripe trigger checkout.session.completed
   
   # Test a subscription update
   stripe trigger customer.subscription.updated
   
   # Test a subscription cancellation
   stripe trigger customer.subscription.deleted
   ```

### Production Setup

For production environments, configure the Stripe webhook endpoint in the Stripe Dashboard:

1. **Register webhook endpoint**:
   - Go to the [Stripe Dashboard](https://dashboard.stripe.com/)
   - Navigate to Developers > Webhooks
   - Add a new endpoint with URL: `https://yourdomain.com/api/stripe-incoming-webhooks`
   
2. **Select relevant events**:
   Subscribe to these key events:
   - `checkout.session.completed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `billing_portal.session.created`

3. **Store the webhook secret**:
   After creating the endpoint, Stripe provides a signing secret. Add this to your environment variables:
   ```
   STRIPE_WEBHOOK_SECRET=whsec_your_production_signing_secret
   ```

4. **Environment selection**:
   The application code selects the appropriate webhook secret based on the environment:
   
   ```javascript
   // app/api/stripe-incoming-webhooks/route.js
   const WEBHOOK_SECRET =
     process.env.NODE_ENV === "production"
       ? process.env.STRIPE_WEBHOOK_SECRET
       : "whsec_8719cddb2c551b4eb604fc6928c770f80548bcab8d51be372a8f9d84bf8dca0e"; // CLI secret
   ```

## Event Processing Pipeline

### Event Verification and Validation

All incoming webhook events undergo a verification process to ensure they're legitimate:

```javascript
// app/api/stripe-incoming-webhooks/route.js
export async function POST(request) {
  try {
    const headers = request.headers;
    // Use the get() method to access headers in Next.js App Router
    const stripeSignature = headers.get("stripe-signature");

    // Get the raw request body as text - this is crucial for Stripe signature verification
    const rawBody = await request.text();

    // Construct the Stripe event with the raw body
    const event = stripe.webhooks.constructEvent(
      rawBody,
      stripeSignature,
      WEBHOOK_SECRET
    );

    if (event) {
      await processStripeWebhook(event);
    }

    // Return a success response so stripe doesn't retry the event
    return onSuccess({ received: true });
  } catch (error) {
    console.error("Webhook error:", error.message);
    return onError({ error: error.message }, 400);
  }
}
```

Key security considerations:
- The signature verification requires the raw request body, not a parsed one
- The webhook secret must be kept secure and rotated periodically
- A 200 response is returned even if processing fails (to prevent retries)
- All errors are logged but don't cause HTTP failure responses

### Event Routing

After verification, events are routed to specialized handlers based on their type:

```javascript
// actions/stripe-webhooks/processor.js
export async function processStripeWebhook(event) {
  try {
    const db = await getDb("stripe");
    const stripeWebhooksCollection = db.collection("webhook_events");
    await stripeWebhooksCollection.insertOne({
      ...event,
      createdAt: new Date(),
    });

    // Handle the event based on its type
    const eventGroup = event.type.split(".")[0] || "unknown";
    switch (eventGroup) {
      case "billing_portal":
        await handleBillingPortal(event);
        break;
      case "charge":
        await handleCharge(event);
        break;
      case "checkout":
        await handleCheckout(event);
        break;
      case "customer":
        await handleCustomer(event);
        break;
      case "payment_intent":
        await handlePaymentIntent(event);
        break;
      case "invoice":
        await handleInvoice(event);
        break;
      case "subscription":
        await handleSubscriptions(event);
        break;
      default:
        await db.collection("log_unhandled").insertOne({
          createdAt: new Date(),
          eventId: event.id,
          eventType: event.type,
          customer: event.data.object.customer,
        });
        break;
    }
  } catch (error) {
    console.error("Error processing Stripe webhook:", error);
  }
}
```

Key aspects of event routing:
1. **Persistence**: All events are logged in the database before processing
2. **Categorization**: Events are categorized by their prefix (e.g., "checkout.session.completed" → "checkout")
3. **Delegation**: Each category has a dedicated handler function
4. **Fault Tolerance**: Errors in one handler don't affect other events
5. **Unhandled Events**: Events without handlers are logged in a separate collection

## Event Handlers

### Checkout Events

The checkout handler processes events related to Stripe Checkout sessions:

```javascript
// actions/stripe-webhooks/checkout/handler.js
export default async function handleCheckout(event) {
  try {
    // Process different checkout session events
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event);
        break;
      
      // Other checkout events
      case 'checkout.session.async_payment_succeeded':
        console.log('Checkout async payment succeeded:', event.id);
        break;
        
      case 'checkout.session.async_payment_failed':
        console.log('Checkout async payment failed:', event.id);
        break;
        
      case 'checkout.session.expired':
        console.log('Checkout session expired:', event.id);
        break;
        
      default:
        console.log(`Unhandled checkout event: ${event.type}`);
    }
  } catch (error) {
    console.error('Error handling checkout event:', error);
  }
}
```

The most critical event is `checkout.session.completed`, which:
1. Extracts data from the checkout session
2. Retrieves additional subscription details from Stripe
3. Stores checkout data in `stripe.checkout_completed` collection
4. Generates activation token in `stripe.activation_tokens` collection
5. Sends welcome email with activation instructions

#### Checkout Data Structure

```json
{
  "_id": {"$oid": "682c505ad4980661ee19f26a"},
  "id": "cs_test_b1bbv0Dy7irsogWQZxCcnbigLOVbTmmsvTFPHbulXSVJzThV5aDLliBeKt",
  "status": "complete",
  "mode": "subscription",
  "promotekitReferral": "8f740b2c-a617-41aa-b882-6859d415858c",
  "customer": "cus_SLTlOKJFKsomXB",
  "subscription": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
  "email": "<EMAIL>",
  "amountSubtotal": {"$numberInt": "79900"},
  "amountTotal": {"$numberInt": "79900"},
  "currency": "usd",
  "plan": "PRO",
  "billingPeriodValue": {"$numberInt": "1"},
  "billingPeriodUnit": "year",
  "userId": "",
  "startDate": {"$date": {"$numberLong": "1747734611000"}},
  "expirationDate": {"$date": {"$numberLong": "1779270611000"}},
  "roleId": {"$numberInt": "4"},
  "createdAt": {"$date": {"$numberLong": "1747734618208"}},
  "updatedAt": {"$date": {"$numberLong": "1747734618208"}}
}
```

### Subscription Events

The customer handler processes subscription-related events:

```javascript
// actions/stripe-webhooks/customer/handler.js
export default async function handleCustomer(event) {
  switch (event.type) {
    case "customer.subscription.updated":
      // Handle subscription updates
      break;
    case "customer.subscription.deleted":
      // Handle subscription cancellation
      break;
    // Other customer events
  }
}
```

#### Subscription Update Logic

When a subscription is updated (`customer.subscription.updated`), the system:
1. Logs the change in `stripe.log_customer` collection
2. Identifies the user associated with the Stripe customer
3. Updates subscription details including:
   - Expiration date based on `current_period_end`
   - User role if the product/plan changed
4. Updates both `paymentdata.memberships` and `stalkreact.users` collections

#### Subscription Cancellation Logic

When a subscription is canceled (`customer.subscription.deleted`), the system:
1. Determines if it's an immediate cancellation or end-of-period
2. For immediate cancellations:
   - Sets role to 8 (free tier)
   - Sets expiration date to the current date
3. For end-of-period cancellations:
   - Maintains current role
   - Sets expiration date to end of current period
4. Updates both `paymentdata.memberships` and `stalkreact.users` collections

#### Customer Log Structure

```json
{
  "_id": {"$oid": "682c6c93ca93c5a901e5d52b"},
  "createdAt": {"$date": {"$numberLong": "1747741843647"}},
  "eventId": "evt_1RQogxBMwsd7cKYCZPrCter1",
  "eventType": "customer.subscription.deleted",
  "customer": "cus_SLUi0ntoscljD4",
  "idUser": "5c4fcc7b-261c-48ee-bf61-3bf2ad164a2e",
  "subscription": "sub_1RQnisBMwsd7cKYCdf5V2vi8",
  "cancelAtPeriodEnd": false,
  "cancelAt": null,
  "canceledAt": {"$numberInt": "1747741842"},
  "currentPeriodEnd": {"$numberInt": "1750416518"},
  "beforeChange": {
    "status": "active",
    "idRole": {"$numberInt": "4"},
    "expirationDate": {"$date": {"$numberLong": "1747824508000"}}
  },
  "afterChange": {
    "status": "canceled",
    "idRole": {"$numberInt": "8"},
    "expirationDate": {"$date": {"$numberLong": "1747741843647"}}
  },
  "isImmediateCancellation": true
}
```

### Customer Portal Events

The billing portal handler tracks when users access their Stripe billing portal:

```javascript
// actions/stripe-webhooks/billing_portal/handler.js
export default async function handleBillingPortal(event) {
  if (event.type === "billing_portal.session.created") {
    const stripeDb = await getDb("stripe");
    const payDb = await getDb("paymentdata");
    
    // get the membership of this user to find the idUser
    const membership = await payDb.collection("memberships").findOne({
      stripeCustomerId: event.data.object.customer,
    });

    // log the event
    await stripeDb.collection("log_billing_portal").insertOne({
      createdAt: new Date(),
      eventId: event.id,
      eventType: event.type,
      customer: event.data.object.customer,
      idUser: membership?.idUser,
    });
  }
}
```

This tracking allows for user analytics and support troubleshooting.

### Payment Events

The payment handler processes events related to payments and charges:

```javascript
// actions/stripe-webhooks/payment_intent/handler.js
export default async function handlePaymentIntent(event) {
  // Processing for payment_intent events
}

// actions/stripe-webhooks/charge/handler.js
export default async function handleCharge(event) {
  // Processing for charge events
}
```

These handlers primarily log events for tracking and troubleshooting purposes, but can be extended for more complex payment processing workflows.

## Error Handling

The webhook system implements robust error handling:

1. **Isolated Handler Errors**: Errors in one handler don't affect other handlers
2. **Complete Event Logging**: All events are logged before processing begins
3. **HTTP Success Responses**: Always returns 200 to Stripe to prevent retries
4. **Detailed Error Logging**: All errors are logged with context
5. **Idempotent Processing**: Prevents duplicate processing of events

## Database Collections

The webhook system uses several MongoDB collections:

| Collection | Purpose | Key Fields |
|------------|---------|------------|
| `stripe.webhook_events` | Stores all raw webhook events | `eventId`, `eventType`, `createdAt` |
| `stripe.checkout_completed` | Stores processed checkout sessions | `id` (session ID), `customer`, `subscription` |
| `stripe.activation_tokens` | Stores account activation tokens | `token`, `sessionId`, `subscriptionId` |
| `stripe.log_customer` | Logs customer subscription changes | `customer`, `idUser`, `beforeChange`, `afterChange` |
| `stripe.log_billing_portal` | Logs customer portal access | `customer`, `idUser` |
| `stripe.log_unhandled` | Logs events without specific handlers | `eventType` |

## Extending the Webhook System

To add support for new event types:

1. **Identify the event group**: Determine which event group the new event belongs to
2. **Update the handler**: Add a case in the appropriate handler file
3. **Implement the logic**: Add the necessary processing code
4. **Update MongoDB schemas**: If new data needs to be stored, update the relevant collections

Example of adding a new event to the checkout handler:

```javascript
// actions/stripe-webhooks/checkout/handler.js
switch (event.type) {
  case 'checkout.session.completed':
    await handleCheckoutSessionCompleted(event);
    break;
  
  // Add a new event handler
  case 'checkout.session.payment_method_selected':
    await handlePaymentMethodSelected(event);
    break;
    
  // Existing events...
}

// Implement the new handler
async function handlePaymentMethodSelected(event) {
  // Logic for the new event
}
```

## Troubleshooting

Common webhook issues and solutions:

| Issue | Possible Causes | Troubleshooting Steps |
|-------|----------------|----------------------|
| Webhook verification failure | Incorrect secret, modified payload | Check the webhook secret, ensure raw body is used |
| Missing events | Webhook not registered, service down | Check Stripe Dashboard events log, verify endpoint configuration |
| Duplicate event processing | Multiple instances, failed deduplication | Check for existing event in database before processing |
| Failed subscription updates | Database inconsistency, invalid data | Verify customer ID mapping in membership collection |
| Missing activation tokens | Failed token generation, database errors | Check webhook event logs and error messages |

For detailed debugging:

1. Check the Stripe Dashboard events log
2. Search the `stripe.webhook_events` collection for the specific event ID
3. Look for errors in the server logs
4. Verify that all environment variables are correctly set 