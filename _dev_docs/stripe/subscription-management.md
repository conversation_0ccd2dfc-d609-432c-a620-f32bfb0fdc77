---
description: >-
  Comprehensive guide to StalkChain's subscription management system, including lifecycle, customer actions, and Stripe Customer Portal integration.
---

**Target audience:** Backend developers who need to implement, modify, or troubleshoot the subscription management system within StalkChain.

# Subscription Management

## Overview

StalkChain's subscription management system handles the complete lifecycle of user subscriptions, from initial creation through upgrades, downgrades, and cancellations. This document covers the key processes and implementation details for managing subscriptions.

```mermaid
flowchart LR
    %% Define styles
    classDef systemProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef customerAction fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef dbProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    
    %% Subscription creation
    Checkout["Checkout<br>Completion"]:::customerAction
    Create["Create<br>Subscription"]:::stripeProcess
    Storage["Store in<br>Database"]:::dbProcess
    Activate["Activate<br>Account"]:::systemProcess
    
    %% Subscription management
    Portal["Customer<br>Portal"]:::customerAction
    Upgrade["Upgrade<br>Subscription"]:::customerAction
    Downgrade["Downgrade<br>Subscription"]:::customerAction
    Cancel["Cancel<br>Subscription"]:::customerAction
    
    %% Webhook handling
    Webhook["Webhook<br>Events"]:::stripeProcess
    Process["Process<br>Changes"]:::systemProcess
    Update["Update<br>Role & Expiry"]:::dbProcess
    
    %% Connections
    Checkout --> Create
    Create --> Storage
    Storage --> Activate
    
    Portal --> Upgrade
    Portal --> Downgrade
    Portal --> Cancel
    
    Upgrade --> Webhook
    Downgrade --> Webhook
    Cancel --> Webhook
    
    Webhook --> Process
    Process --> Update
```

## Subscription Lifecycle

### Creation and Initial Setup

Subscriptions are created when a customer completes the checkout process:

1. **Checkout Session Creation**:
   ```javascript
   // actions/checkout-v2/stripe.js
   export async function createStripeCheckout({ 
     priceId, 
     userId = null, 
     planName = 'PRO',
     referralId = null,
     billingPeriodValue = 1,
     billingPeriodUnit = 'month',
     trialDays = 0
   }) {
     // Create a Stripe checkout session
     const sessionOptions = {
       payment_method_types: ['card'],
       line_items: [{ price: sanitizedPriceId, quantity: 1 }],
       mode: 'subscription',
       // Other options...
     };
     
     // Add trial period if specified
     if (sanitizedTrialDays > 0) {
       sessionOptions.subscription_data = {
         trial_period_days: sanitizedTrialDays,
       };
     }
     
     const session = await stripe.checkout.sessions.create(sessionOptions);
     return { success: true, url: session.url };
   }
   ```

2. **Subscription Creation by Stripe**:
   When a customer completes payment in the Stripe Checkout, Stripe automatically:
   - Creates a Customer object if one doesn't exist
   - Creates a Subscription object linked to the customer
   - Sends a `checkout.session.completed` webhook event

3. **Webhook Processing**:
   ```javascript
   // actions/stripe-webhooks/checkout/handler.js
   async function handleCheckoutSessionCompleted(event) {
     // Extract session data
     const session = event.data.object;
     
     // Store checkout data
     await db.collection("checkout_completed").insertOne({
       id: session.id,
       customer: session.customer,
       subscription: session.subscription,
       // Other fields...
     });
     
     // Generate activation token
     const activationToken = await generateActivationToken({
       sessionId: session.id,
       subscriptionId: session.subscription,
       customerId: session.customer,
       // Other fields...
     });
     
     // Send welcome email with activation link
     await sendWelcomeEmail({
       email: session.customer_details.email,
       planName: parsedData.plan,
       activationToken
     });
   }
   ```

4. **Initial Database Records**:
   Successful checkout creates records in multiple collections:
   - `stripe.checkout_completed`: Records checkout session details
   - `stripe.activation_tokens`: Stores token for account activation
   - After activation: `paymentdata.memberships` and updates to `stalkreact.users`

After subscription creation, the customer must activate their account by either:
- Clicking the activation link in the welcome email
- Using the direct activation option on the checkout success page

Both paths eventually link the subscription to the user's account through the activation token.

### Trials and Free Tiers

StalkChain supports both trial periods and a free tier:

#### Trial Periods

Trial periods allow customers to access paid features for a limited time before being charged:

1. **Configuration**:
   ```javascript
   // Example of creating a checkout with trial
   const result = await createStripeCheckout({
     priceId: "price_1Abc123...",
     userId: "user_123",
     planName: "PRO",
     billingPeriodValue: 1,
     billingPeriodUnit: "month",
     trialDays: 7  // 7-day trial period
   });
   ```

2. **Stripe Setup**:
   - Trial periods are configured via `trial_period_days` parameter
   - Stripe requires a payment method but doesn't charge until trial ends
   - Payment method is used for validation only during the trial

3. **Trial Status Handling**:
   ```javascript
   // Example of checking trial status
   if (subscription.status === 'trialing') {
     // Handle trial-specific logic
     const trialEnd = new Date(subscription.trial_end * 1000);
     // Set expirationDate to trial end date
   }
   ```

4. **End of Trial**:
   - Stripe automatically attempts payment at trial end
   - If successful, subscription status changes to `active`
   - If unsuccessful, status changes to `incomplete` or `past_due`

#### Free Tier (Role ID 8)

The free tier provides limited access to users without a paid subscription:

1. **Default Role**:
   - New users without subscriptions default to role ID 8
   - Users with canceled subscriptions are moved to role ID 8

2. **Implementation**:
   ```javascript
   // Example of reverting to free tier on subscription cancellation
   if (isImmediateCancellation) {
     updateFields.idRole = 8; // Free tier
     updateFields.expirationDate = new Date(); // Set to now
   }
   ```

3. **Feature Limitations**:
   - Free tier users have restricted access to features
   - Permission checks occur throughout the application based on role ID

## Customer Actions

### Upgrades and Downgrades

Customers can change their subscription plans through the Stripe Customer Portal:

```mermaid
flowchart LR
    %% Define styles
    classDef customerAction fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef webhook fill:#f8bbd0,stroke:#c2185b,color:#c2185b
    classDef dbProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    
    %% Main workflow
    A["Customer<br>Changes Plan"]:::customerAction
    B["Stripe Updates<br>Subscription"]:::stripeProcess
    C["customer.subscription.updated<br>Webhook"]:::webhook
    D{"Product<br>Changed?"}
    E["Map New Product<br>to Role ID"]
    F["No Role<br>Change"]
    G["Update Membership<br>& User Records"]:::dbProcess
    
    %% Connections
    A --> B
    B --> C
    C --> D
    D -->|Yes| E
    D -->|No| F
    E --> G
    F -->|Update expiry only| G
```

#### Product Mapping to Roles

When a plan changes, the system maps the new Stripe product ID to a role ID:

```javascript
// actions/stripe-webhooks/customer/handler.js
if (afterChange?.plan && beforeChange?.plan) {
  const STRIPE_DEGEN_PLAN_PRODUCT_ID = process.env.STRIPE_DEGEN_PLAN_PRODUCT_ID; // idRole 4
  const STRIPE_LEGEND_PLAN_PRODUCT_ID = process.env.STRIPE_LEGEND_PLAN_PRODUCT_ID; // idRole 3
  
  const oldProductId = beforeChange.plan.product;
  const newProductId = afterChange.plan.product;
  
  // Only update if the product has changed
  if (oldProductId !== newProductId) {
    // Determine the new role based on the product
    if (newProductId === STRIPE_DEGEN_PLAN_PRODUCT_ID) {
      newIdRole = 4; // Pro
    } else if (newProductId === STRIPE_LEGEND_PLAN_PRODUCT_ID) {
      newIdRole = 3; // Legend
    }
  }
}
```

#### Database Updates

After a plan change, both the membership and user records are updated:

```javascript
// Update the membership with all the changes at once
const updatedMembership = await payDb.collection("memberships").updateOne(
  { stripeCustomerId: customerID },
  { $set: updateFields }
);

// Update user role if needed
if (needsUserRoleUpdate && newIdRole) {
  const updatedUser = await usersDb.collection("users").updateOne(
    { id: idUser },
    {
      $set: {
        idRole: newIdRole,
        updatedAt: new Date(),
      }
    }
  );
}
```

#### Proration and Billing

StalkChain uses Stripe's default proration behavior:

1. **Upgrades**: Stripe calculates prorated charges for the remaining billing period
2. **Downgrades**: Changes typically take effect at the end of the billing period
3. **Immediate Changes**: Some plan changes may take effect immediately based on Stripe settings

### Cancellations (Immediate vs. End of Period)

Customers can cancel their subscriptions through the Stripe Customer Portal. Currently, only the end-of-period cancellation option is available to customers through the portal, but the backend supports both types:

```mermaid
flowchart LR
    %% Define styles
    classDef customerAction fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef webhook fill:#f8bbd0,stroke:#c2185b,color:#c2185b
    classDef decisionClass fill:#fff,stroke:#455a64,color:#455a64
    classDef dbProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    
    %% Main workflow
    A["Customer<br>Cancels Subscription"]:::customerAction
    C["Stripe Updates<br>Subscription"]:::stripeProcess
    D["customer.subscription.deleted<br>Webhook"]:::webhook
    E{"Immediate<br>Cancellation?"}:::decisionClass
    F["Set Role to<br>Free (8)"]
    G["Keep Current Role<br>Until Period End"]
    H["Update Membership<br>& User Records"]:::dbProcess
    
    %% Connections
    A --> C
    C --> D
    D --> E
    E -->|Yes| F
    E -->|No| G
    F --> H
    G -->|Update expiry only| H
```

#### Immediate Cancellation

Immediate cancellations end the subscription immediately and revert the user to the free tier. Currently, this option is only used internally for refunds and not exposed to customers in the portal:

```javascript
// actions/stripe-webhooks/customer/handler.js
if (isImmediateCancellation) {
  console.log("Immediate cancellation detected, setting role to 8 and expiration to now");
  updateFields.expirationDate = new Date(); // Set to now
  newIdRole = 8; // Free tier
  needsUserRoleUpdate = true;
}
```

This results in:
- Subscription status changing to `canceled`
- User role changing to 8 (free tier) immediately
- `expirationDate` set to the current date

#### End-of-Period Cancellation

End-of-period cancellations maintain access until the end of the current billing period. This is the only option currently available to customers through the Stripe Customer Portal:

```javascript
// actions/stripe-webhooks/customer/handler.js
if (!isImmediateCancellation && currentPeriodEnd) {
  console.log("End of period cancellation, only setting expiration to period end");
  updateFields.expirationDate = new Date(currentPeriodEnd * 1000);
  // Role stays the same for period-end cancellations
}
```

This results in:
- Subscription marked with `cancel_at_period_end: true`
- User role remaining unchanged until end of period
- `expirationDate` set to the end of the current billing period

After the period ends:
- Stripe sends a `customer.subscription.deleted` webhook
- The system then fully processes the cancellation

#### Detecting Cancellation Type

The webhook handler determines the cancellation type based on webhook data:

```javascript
// Determine if this is an immediate cancellation
const isImmediateCancellation = !cancelAtPeriodEnd || cancelAt === null;
```

The backend system supports both cancellation types, even though only end-of-period is currently exposed to customers. This allows for future flexibility if portal settings are changed to offer both options.

### Refunds and Credits

StalkChain's system processes refunds through cancellations in the subscription system:

#### Processing Refunds

Refunds are currently handled by canceling subscriptions immediately. The `charge.refunded` webhook is not currently implemented in the system. Instead, when a customer is refunded:

1. Their subscription is canceled immediately in Stripe (either manually or via API)
2. This generates a `customer.subscription.deleted` webhook event
3. The system detects this is an immediate cancellation and ends the membership:

```javascript
// actions/stripe-webhooks/customer/handler.js
// Determine if this is an immediate cancellation
const isImmediateCancellation = !cancelAtPeriodEnd || cancelAt === null;

if (isImmediateCancellation) {
  console.log("Immediate cancellation detected, setting role to 8 and expiration to now");
  updateFields.expirationDate = new Date(); // Set to now
  newIdRole = 8; // Free tier
  needsUserRoleUpdate = true;
}
```

This approach treats refunds as immediate cancellations, ending the user's access to premium features and reverting them to the free tier (role ID 8).

If the event isn't an immediate cancellation, then it means the user has simply canceled their subscription and it will end at the end of their current billing period.

#### Credits for Downgrade Proration

When a customer downgrades their subscription, Stripe may issue credits for the unused portion of the billing period:

1. **Credit Generation**: Handled automatically by Stripe based on subscription changes
2. **Credit Application**: Applied to future invoices by Stripe
3. **Webhook Processing**: Credits are reflected in `customer.subscription.updated` events

## Stripe Customer Portal

StalkChain integrates with Stripe Customer Portal to allow users to manage their subscriptions.

### Configuration

The Stripe Customer Portal is configured through the Stripe Dashboard with specific settings for StalkChain:

#### Portal Settings

| Setting | Configuration |
|---------|---------------|
| Products | PRO (role 4) and Legend (role 3) plans |
| Subscription updates | Allowed with prorations |
| Cancellations | Both immediate and end-of-period options enabled |
| Customer information | Email address and payment methods editable |
| Billing history | Available with downloadable invoices |

#### Portal Session Creation

The application creates portal sessions when users need to manage their subscription:

```javascript
// actions/checkout/getCustomerPortal.js
import Stripe from "stripe";
import { getDb } from "@/utils/mongodb";
import { getUserId } from "@/utils/authState";

const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY, {
  apiVersion: "2024-09-30.acacia",
});

export const getCustomerPortal = async (returnUrl) => {
  const userId = await getUserId();
  const db = await getDb("paymentdata");
  const membership = await db.collection("memberships").findOne({ idUser: userId });
  
  if(!membership) {
    return {
      status: "error",
      message: "No membership found",
    };
  }

  if(!membership?.stripeCustomerId) {
    return {
      status: "error",
      message: "No stripe customer id found", // this user has no stripe customer id, so we can't create a portal for them  
    };
  }

  const getPortal = await stripe.billingPortal.sessions.create({
    customer: membership?.stripeCustomerId,
    return_url: returnUrl ? returnUrl : `${process.env.SITE_URL}/settings`,
  });

  return JSON.parse(JSON.stringify({
    url: getPortal.url
  }));
}
```

This function:
1. Retrieves the current user's ID from auth state
2. Looks up the user's membership to get their `stripeCustomerId`
3. Creates a new portal session with Stripe
4. Returns the portal URL for the client to redirect to

The client-side implementation in the settings page:

```jsx
// content/settings/page.js
import { getCustomerPortal } from "@/actions/checkout/getCustomerPortal";

// Within component
const handleStripeBilling = async () => {
  const portal = await getCustomerPortal(window.location.href);
  if (portal?.url) {
    window.open(portal.url, "_blank");
  }
};

// Usage in JSX
<Button
  color="secondary"
  variant="bordered"
  onPress={() => {
    handleStripeBilling();
  }}
  className="rounded"
>
  Manage Your Subscription (Stripe)
</Button>
```

### Handling Portal Session Events

The system tracks when users access the customer portal and processes any changes they make:

#### Portal Session Tracking

```javascript
// actions/stripe-webhooks/billing_portal/handler.js
export default async function handleBillingPortal(event) {
  if (event.type === "billing_portal.session.created") {
    const stripeDb = await getDb("stripe");
    const payDb = await getDb("paymentdata");
    
    // Find the membership to get the user ID
    const membership = await payDb.collection("memberships").findOne({
      stripeCustomerId: event.data.object.customer,
    });

    // Log the portal access
    await stripeDb.collection("log_billing_portal").insertOne({
      createdAt: new Date(),
      eventId: event.id,
      eventType: event.type,
      customer: event.data.object.customer,
      idUser: membership?.idUser,
    });
  }
}
```

#### Processing Changes Made in Portal

Changes made through the portal generate standard Stripe webhook events:

1. **Subscription Updates**: Processed via `customer.subscription.updated` event
2. **Cancellations**: Processed via `customer.subscription.deleted` event
3. **Payment Method Changes**: Processed via `customer.source.updated` event

These events are handled by the respective webhook handlers described earlier in this document.

## Troubleshooting

### Common Issues

| Issue | Possible Causes | Solution |
|-------|----------------|----------|
| Subscription not activated | Token not used or expired | Check `stripe.activation_tokens` for status |
| Role not updated after plan change | Webhook not received or processed | Check webhook events in Stripe Dashboard and `stripe.webhook_events` collection |
| Unexpected cancellation behavior | Mismatch between immediate vs. end-of-period setting | Verify `isImmediateCancellation` determination in handler |
| Missing customer ID | Failed account activation | Check `stripe.checkout_completed` for customer ID and verify it was linked in activation |

### Verification Steps

When troubleshooting subscription issues:

1. **Check Stripe Dashboard**: Verify subscription status in Stripe
2. **Examine Webhook Log**: Check `stripe.webhook_events` for relevant events
3. **Verify Database Records**: Check all collections for consistent data:
   - `stripe.checkout_completed`
   - `stripe.activation_tokens`
   - `paymentdata.memberships`
   - `stalkreact.users`
4. **Test Portal Access**: Attempt to access the Customer Portal with the user's `stripeCustomerId`

### Recovery Actions

For data inconsistencies, manual fixes may be needed:

```javascript
// Example of manually updating a user's role and expiration
await db.collection("stalkreact.users").updateOne(
  { id: "user_id_here" },
  { 
    $set: {
      idRole: 4, // Set to appropriate role
      expirationDate: new Date("2023-12-31"), // Set appropriate date
      stripeCustomerId: "cus_stripe_id_here" // Link to correct Stripe customer
    }
  }
);

// Also update membership record
await db.collection("paymentdata.memberships").updateOne(
  { idUser: "user_id_here" },
  {
    $set: {
      idRole: 4,
      expirationDate: new Date("2023-12-31"),
      stripeCustomerId: "cus_stripe_id_here",
      stripeSubscriptionId: "sub_stripe_id_here"
    }
  }
);
``` 