---
description: >-
  Comprehensive documentation of StalkChain's account activation system, covering token generation, email delivery, verification, authentication integration, and database updates.
---

**Target audience:** Backend developers who need to understand, implement, or debug the account activation flow after Stripe checkout.

# Account Activation System

## Overview

The account activation system is a critical component that connects payment processing with user account setup. After a customer completes payment through Stripe, this system enables them to:

1. Receive an activation link via email
2. Authenticate with <PERSON> (or use direct session-based activation)
3. Link their subscription to their account
4. Receive the appropriate role and permissions

```mermaid
flowchart TD
    A("Stripe Webhook: checkout.session.completed") --> B("Generate Activation Token")
    B --> C("Send Welcome Email with Token")
    C --> D{"Customer clicks activation link"}
    
    D -->|Email Link| E("Verify Token")
    
    subgraph "Checkout Success Page"
    S("Stripe redirects to success page") --> V("Session-based verification")
    V --> W("Direct activation option")
    end
    
    W --> F
    E -->|Token Valid| F("Privy Authentication")
    F --> G("Check User & Token Status")
    
    G -->|Valid| H("Create/Update Membership")
    H --> I("Update User Role & Status")
    I --> J("<PERSON> Token as Used")
    J --> K("Redirect to Dashboard")
    
    G -->|Invalid| L("Show Error Message")
    
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    
    class A stripeProcess
    class B,C,E,G,H,I,J serverProcess
    class D,F,K,L clientProcess
    class S,V,W clientProcess
```

## Activation Flow

### Token Generation

The activation token system creates secure, time-limited tokens that link a specific subscription to a user account:

```javascript
// actions/stripe-webhooks/utils.js
export async function generateActivationToken({
  sessionId,
  subscriptionId,
  customerId,
  email,
  planName,
  billingInterval,
  billingPeriodValue,
  billingPeriodUnit
}) {
  // Verify subscription is active before generating token
  const subscription = await stripe.subscriptions.retrieve(subscriptionId);
  if (subscription.status !== 'active' && subscription.status !== 'trialing') {
    return null;
  }
  
  // Generate cryptographically secure random token
  const crypto = await import('crypto');
  const token = crypto.randomBytes(32).toString('hex');
  
  // Store token in database with all relevant subscription data
  await db.collection("activation_tokens").insertOne({
    token,
    sessionId,
    subscriptionId,
    customerId,
    email,
    planName,
    billingPeriodValue,
    billingPeriodUnit,
    billingInterval, // Legacy field for backward compatibility
    createdAt: new Date(),
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days expiry
    used: false
  });
  
  return token;
}
```

Key aspects of token generation:

1. **Subscription Validation**: Tokens are only generated for active or trialing subscriptions
2. **Token Security**: Uses cryptographically secure random bytes (256 bits of entropy)
3. **Limited Lifespan**: Tokens expire after 7 days
4. **Comprehensive Data**: Stores all subscription details needed for activation
5. **Single-Use Design**: Each token can only be used once

The token generation happens automatically when a `checkout.session.completed` webhook event is received from Stripe.

### Email Delivery

After token generation, a welcome email is sent to the customer with an activation link:

```javascript
// actions/stripe-webhooks/checkout/handler.js
async function sendWelcomeEmail({ 
  email, 
  planName, 
  billingInterval, 
  billingPeriodValue = 1, 
  billingPeriodUnit = 'month', 
  activationToken = null 
}) {
  // Format billing information for display
  let formattedBillingCycle = formatBillingCycle(
    billingPeriodValue, 
    billingPeriodUnit, 
    billingInterval
  );
  
  // Generate activation URL if token exists
  const activationUrl = activationToken 
    ? getActivationUrl(activationToken) 
    : '';
  
  // Generate HTML using email template
  const emailHtml = createWelcomeEmailHtml({
    planName: planName.toUpperCase(),
    billingCycle: formattedBillingCycle, 
    billingPeriodValue,
    billingPeriodUnit,
    email,
    activationUrl
  });
  
  // Send email with appropriate subject line
  await sendEmail({
    to: email,
    from: "<EMAIL>",
    subject: activationToken 
      ? `Activate Your StalkChain ${planName} Account` 
      : `Welcome to StalkChain ${planName}!`,
    html: emailHtml,
  });
}
```

### Email Service and Templates

StalkChain uses the [Resend](https://resend.com) email service for sending transactional emails:

```javascript
// utils/resend.js
import { Resend } from 'resend';

// Singleton pattern for Resend client
export const getResend = () => {
  if (!resendInstance) {
    resendInstance = new Resend(process.env.RESEND_API_KEY);
  }
  return resendInstance;
};

// Send an email using Resend
export const sendEmail = async ({
  to,
  from,
  subject,
  html,
  // Other parameters...
}) => {
  const resend = getResend();
  const response = await resend.emails.send({
    to, subject, html, 
    ...from && { from }
    // Other parameters...
  });
  return response;
};
```

Key aspects of the email system:

1. **HTML Templates**: Email templates are defined in the `components/email` directory
2. **Resend Integration**: Uses the official Resend SDK for delivery
3. **Activation Link**: Includes the token in the URL parameter
4. **Customizable Content**: Variables for plan name, billing cycle, etc.
5. **Responsive Design**: Templates are mobile-friendly

To modify email templates or content:

1. Edit the template in `components/email/WelcomeEmail.js`
2. Test changes by sending a test email before deployment
3. Environment variables for email configuration are in `.env`

The email contains instructions for activating the account and explains the benefits of the subscription plan.

### Token Verification

When a user clicks the activation link, the token is verified before proceeding:

```javascript
// actions/stripe-webhooks/utils.js
export async function verifyActivationToken(token) {
  if (!token) return null;
  
  // Find the token in the database
  const db = await getDb("stripe");
  const tokenData = await db.collection("activation_tokens").findOne({ token });
  
  // If token doesn't exist, return null
  if (!tokenData) {
    return null;
  }
  
  // Check if token is expired
  if (tokenData.expiresAt && new Date() > new Date(tokenData.expiresAt)) {
    return {
      ...tokenData,
      status: 'expired'
    };
  }
  
  // Return the token data with status
  return {
    ...tokenData,
    status: tokenData.used ? 'used' : 'valid'
  };
}
```

The verification process checks the token against the `stripe.activation_tokens` collection (see [Database Structure](#mongodb-database-structure)) and handles several important cases:

1. **Token Existence**: Validates that the token exists in the database
2. **Expiration Check**: Verifies the token has not expired (after 7 days)
3. **Usage Status**: Determines if token has already been used
4. **Status Code**: Returns a status (`valid`, `used`, or `expired`) with the token data

This verification process is called from the account activation server action when a user attempts to activate their account.

## User Authentication Integration

### Privy Integration

StalkChain uses Privy for authentication, integrating it with the activation flow through the `ActivateAccountButton` component:

```jsx
// components/privy/ActivateAccountButton.js
export default function ActivateAccountButton({
  className = "w-full",
  size = "lg",
  color = "primary",
  label = "Activate Account",
  onLoginSuccess = () => {},
  sessionId = null,
  token = null
}) {
  const { login, ready, authenticated, user, logout } = usePrivy();
  const auth = useAuth();
  
  // Track login and activation state
  const [isActivating, setIsActivating] = useState(false);
  const loginTriggered = useRef(false);
  const activationAttempted = useRef(false);
  
  // Effect to call activateAccount when authenticated
  useEffect(() => {
    const handleActivateAccount = async () => {
      // Only attempt activation if authenticated and login was triggered by our button
      if (authenticated && user && (sessionId || token) && loginTriggered.current) {
        try {
          setIsActivating(true);
          const result = await activateAccount(sessionId, token);
          
          if (result.success) {
            // Get fresh permissions before redirecting
            await auth.getPermission();
            window.dispatchEvent(new CustomEvent("privyAuthChange"));
            window.location.href = '/kol-feed';
          } else {
            // Handle activation failure
            setErrorMessage(result.message || "Unknown error during activation");
            setActivationFailed(true);
          }
        } catch (error) {
          // Handle error
        }
      }
    };

    if (authenticated) {
      handleActivateAccount();
    }
  }, [authenticated, user, sessionId, token]);
}
```

Key aspects of the Privy integration:

1. **Login Flow**: Uses Privy's login method to authenticate users
2. **Authentication Tracking**: Monitors Privy authentication state changes
3. **Post-Login Activation**: Automatically triggers activation after successful login
4. **Permission Syncing**: Updates user permissions after successful activation
5. **Guided UX**: Uses a step-by-step modal to explain the activation process

This component is used on the checkout success page and the account activation page, providing a consistent activation experience.

### Role Assignment

After authentication, the system determines the appropriate role based on the subscription plan:

```javascript
// actions/checkout-v2/activateAccount.js
// Determine role based on plan name
// Default to Pro (idRole 4) if plan name can't be determined
let idRole = 4; // Pro membership by default
let planName = '';

if (checkoutData.plan) {
  planName = checkoutData.plan.toLowerCase();
} else if (checkoutData.fullWebhook && checkoutData.fullWebhook.metadata && checkoutData.fullWebhook.metadata.planName) {
  planName = checkoutData.fullWebhook.metadata.planName.toLowerCase();
} else if (activationToken.planName) {
  planName = activationToken.planName.toLowerCase();
}

if (planName.includes('legend')) {
  idRole = 3; // Legend membership
} else if (planName.includes('pro')) {
  idRole = 4; // Pro membership
}
```

The role assignment system:

1. **Fallback Logic**: Uses multiple sources to determine plan name
2. **Role Mapping**: Maps plan names to role IDs (3 for Legend, 4 for Pro)
3. **Case Insensitivity**: Handles case variations in plan names
4. **Default Safety**: Defaults to Pro (idRole 4) if plan name can't be determined

These roles control access to features throughout the application, with Legend (idRole 3) having the highest level of access.

## Database Updates

### MongoDB Database Structure

The account activation system spans across three MongoDB databases, each with specific collections that handle different aspects of the process:

#### 1. `stripe` Database

Contains collections related to Stripe payments and activation tokens:

```
stripe
├── checkout_completed       # Completed checkout sessions
├── activation_tokens        # Account activation tokens
├── webhook_events           # Raw webhook events from Stripe
└── log_customer             # Customer subscription event logs
```

#### 2. `paymentdata` Database

Manages subscription and membership information:

```
paymentdata
└── memberships              # User membership and subscription data
```

#### 3. `stalkreact` Database

Stores user account information:

```
stalkreact
└── users                    # Primary user records with authentication data
```

### Collection Schemas

#### `stripe.activation_tokens`

Stores tokens used for account activation:

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| token | String | Activation token (64-char hex string) |
| sessionId | String | Stripe checkout session ID |
| subscriptionId | String | Stripe subscription ID |
| customerId | String | Stripe customer ID |
| email | String | Customer email address |
| planName | String | Plan name (e.g., "PRO") |
| billingPeriodValue | Number | Length of billing period |
| billingPeriodUnit | String | Unit of billing period (e.g., "month") |
| billingInterval | String | Legacy billing interval field |
| createdAt | Date | Token creation timestamp |
| expiresAt | Date | Token expiration timestamp (7 days) |
| used | Boolean | Whether token has been used |
| activatedAt | Date | When token was activated (if used) |
| activatedBy | String | User ID who activated the token |

#### `paymentdata.memberships`

Links users to their subscriptions:

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| idUser | String | User ID in our system |
| idRole | Number | Role ID (3 for Legend, 4 for Pro) |
| stripeCustomerId | String | Stripe customer ID |
| stripeSubscriptionId | String | Stripe subscription ID |
| email | String | User's email address |
| walletAddress | String | User's wallet address |
| referredBy | String | Referral code if applicable |
| startDate | Date | Subscription start date |
| expirationDate | Date | Subscription expiration date |
| createdAt | Date | Document creation timestamp |
| updatedAt | Date | Document update timestamp |

#### `stalkreact.users`

Stores user authentication and role information:

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| id | String | User ID in our system |
| privyId | String | Privy authentication ID |
| enabled | Boolean | Whether user is enabled |
| idRole | Number | Role ID (permission level) |
| linkedAccounts | Array | Array of linked authentication methods |
| createdAt | Date | User creation timestamp |
| updatedAt | Date | User update timestamp |
| oldDbEmail | String | Email from previous database/activation |
| oldDbWallet | String | Wallet from previous database/activation |
| expirationDate | Date | Subscription expiration date |
| stripeCustomerId | String | Stripe customer ID |

### Membership Creation/Updates

The activation process creates or updates membership records to link users with their subscriptions:

```javascript
// actions/checkout-v2/activateAccount.js
if (existingUserMembership) {
  // Update existing membership
  await paymentDb.collection("memberships").updateOne(
    { idUser: idUser },
    { 
      $set: {
        idRole: idRole,
        stripeCustomerId: stripeCustomerId,
        stripeSubscriptionId: stripeSubscriptionId,
        email: email,
        startDate: startDate,
        expirationDate: expirationDate,
        updatedAt: now
      }
    }
  );
} else {
  // Create new membership for the user
  const newMembership = {
    idUser: idUser,
    idRole: idRole,
    stripeCustomerId: stripeCustomerId,
    stripeSubscriptionId: stripeSubscriptionId,
    email: email,
    walletAddress: walletAddress,
    referredBy: referralCode,
    startDate: startDate,
    expirationDate: expirationDate,
    createdAt: now,
    updatedAt: now
  };
  
  await paymentDb.collection("memberships").insertOne(newMembership);
}
```

Key aspects of membership management:

1. **Upsert Pattern**: Updates existing membership or creates new one
2. **Comprehensive Data**: Stores all subscription and user details
3. **Wallet Integration**: Captures wallet address for crypto users
4. **Referral Tracking**: Records referral information if available
5. **Timestamps**: Maintains accurate start, expiration, and update times

The `paymentdata.memberships` collection links users to their subscription details and provides the foundation for access control.

### User Status Updates

The activation process also updates the user record with subscription details:

```javascript
// actions/checkout-v2/activateAccount.js
// Prepare update fields for user
const updateFields = {
  idRole,
  stripeCustomerId,
  expirationDate,
  updatedAt: now
};

// Add oldDbWallet and oldDbEmail if available
if (walletAddress) updateFields.oldDbWallet = walletAddress;
if (email) updateFields.oldDbEmail = email;

// Update user's role and subscription details
await reactDb.collection("users").updateOne(
  { id: idUser },
  {
    $set: updateFields
  }
);

// Mark token as used
await stripeDb.collection("activation_tokens").updateOne(
  { _id: activationToken._id },
  { $set: { used: true, activatedAt: new Date(), activatedBy: idUser } }
);
```

The user update process:

1. **Role Synchronization**: Updates idRole to match membership
2. **Customer ID Linking**: Stores Stripe customer ID for future operations
3. **Expiration Tracking**: Sets subscription expiration date
4. **Contact Information**: Updates wallet and email information
5. **Token Marking**: Marks the activation token as used to prevent reuse

After these updates, the user has immediate access to the features included in their subscription.

## Security Considerations

The activation system implements several important security measures:

1. **High-Entropy Tokens**: 256 bits of cryptographically secure randomness (64 hex characters)
2. **Limited Lifespan**: Tokens expire after 7 days
3. **Single Use**: Tokens are marked as used after activation
4. **Subscription Verification**: Validates subscription status before creating tokens
5. **Customer Matching**: Ensures the customer ID matches between `stripe.checkout_completed` and `stripe.activation_tokens` collections
6. **Cross-User Protection**: Prevents activating a subscription already linked to another user in the `paymentdata.memberships` collection

These measures ensure that only legitimate customers who have completed payment can activate accounts, preventing unauthorized access to paid features.

## Checkout Success Direct Activation

In addition to email-based activation, users can activate their accounts directly from the checkout success page:

```jsx
// content/checkout/success/page.js
<div className="mt-6">
  <ActivateAccountButton 
    sessionId={checkoutData.sessionId}
    label="Activate Account"
    onLoginSuccess={handleActivationStart}
  />
</div>
```

When a user completes payment and is redirected to the success page:

1. The page receives the `session_id` parameter from Stripe
2. The session is verified server-side and checkout data is retrieved
3. An activation button is displayed for immediate account connection
4. The activation process looks up the token using the session ID rather than the token value
5. After successful activation, the user is redirected to the dashboard

### Token Lookup and Verification

The system handles both activation flows through the same core function, using a dual lookup pattern that searches for tokens by either direct token value or session ID:

```javascript
// actions/checkout-v2/activateAccount.js
// Find activation token by either direct token or session ID
let activationToken = null;

if (token) {
  activationToken = await stripeDb.collection("activation_tokens").findOne({
    token: token
  });
} else if (sessionId) {
  activationToken = await stripeDb.collection("activation_tokens").findOne({
    sessionId: sessionId
  });
}
```

Both activation flows:
1. Perform the same validation checks on the token
2. Create or update the same membership and user records
3. Mark the token as used in the same way:

```javascript
// Mark token as used regardless of how it was found
await stripeDb.collection("activation_tokens").updateOne(
  { _id: activationToken._id },
  { $set: { used: true, activatedAt: new Date(), activatedBy: idUser } }
);
```

This ensures complete consistency between both activation methods, with the only difference being whether the token is found by token value (email link) or by session ID (success page). In both cases, the same token is used and marked as used, preventing duplicate activations.

This dual-flow approach provides flexibility and reduces friction by allowing users to activate their account either immediately after payment or later via email.

## Troubleshooting

Common issues in the activation flow and their solutions:

| Issue | Possible Causes | Troubleshooting Steps |
|-------|----------------|----------------------|
| Token not found | Link is incorrect or token deleted | Check database for token existence |
| Token expired | User tried to activate after 7 days | Generate new token via admin interface |
| Already activated | User attempting to reuse a token | Check membership collection for existing record |
| Customer ID mismatch | Database inconsistency between collections | Verify IDs in checkout_completed and activation_tokens |
| Invalid session ID | Bad redirect from Stripe | Check Stripe dashboard for successful payment | 