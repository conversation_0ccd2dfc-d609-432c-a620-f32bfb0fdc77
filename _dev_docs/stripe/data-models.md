---
description: >-
  Detailed documentation of StalkChain's MongoDB data models used for Stripe integration, covering collection schemas, relationships, and role management.
---

**Target audience:** Backend developers who need to understand, query, or modify the data structures that support the Stripe payment system.

# Stripe Data Models

## Overview

StalkChain's payment system uses MongoDB to store and manage subscription data across multiple collections. This document details each collection's structure, their relationships, and how they support the payment and subscription lifecycle.

```mermaid
flowchart TD
    %% Define styles
    classDef stripeClass fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef userClass fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef linkClass fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32

    %% Stripe collections
    WebhookEvents[stripe.webhook_events]
    CheckoutCompleted[stripe.checkout_completed]
    ActivationTokens[stripe.activation_tokens]
    LogCustomer[stripe.log_customer]
    
    %% User collections
    Users[stalkreact.users]
    Memberships[paymentdata.memberships]
    
    %% Relationships
    WebhookEvents -->|creates| CheckoutCompleted
    CheckoutCompleted -->|generates| ActivationTokens
    ActivationTokens -->|activates| Memberships
    LogCustomer -->|updates| Memberships
    Memberships -->|linked to| Users
    
    %% Assign classes
    class WebhookEvents,CheckoutCompleted,ActivationTokens,LogCustomer stripeClass
    class Users userClass
    class Memberships linkClass
```

## Stripe Collections in MongoDB

The Stripe integration uses multiple collections in the `stripe` database to track payments, subscriptions, and activation tokens.

### stripe.webhook_events

Stores all raw webhook events received from Stripe for audit and debugging purposes.

#### Schema

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| id | String | Stripe event ID |
| type | String | Event type (e.g., "checkout.session.completed") |
| data | Object | Complete event data from Stripe |
| createdAt | Date | When the event was received |

#### Example

```json
{
  "_id": "682c5059d4980661ee19f269", // ObjectId
  "id": "evt_1RQmoLBMwsd7cKYC1qBGD6yF", 
  "object": "event",
  "api_version": "2024-09-30.acacia",
  "created": 1747734611, // Number (Unix timestamp)
  "data": {
    "object": {
      "id": "cs_test_b1bbv0Dy7irsogWQZxCcnbigLOVbTmmsvTFPHbulXSVJzThV5aDLliBeKt",
      "object": "checkout.session",
      "customer": "cus_SLTlOKJFKsomXB",
      "subscription": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
      "status": "complete",
      "payment_status": "paid",
      "metadata": {
        "billingPeriodValue": "1",
        "billingPeriodUnit": "year",
        "planName": "PRO"
      }
      // Additional fields omitted for brevity
    }
  },
  "livemode": false,
  "pending_webhooks": 0,
  "request": {
    "id": null,
    "idempotency_key": null
  },
  "type": "checkout.session.completed",
  "createdAt": "2023-07-20T15:23:37.842Z" // Date
}
```

### stripe.checkout_completed

Stores processed checkout session data, providing a record of successful payments.

#### Schema

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| id | String | Stripe checkout session ID |
| status | String | Status of the checkout (e.g., "complete") |
| mode | String | Checkout mode (e.g., "subscription") |
| promotekitReferral | String | Referral ID if applicable |
| customer | String | Stripe customer ID |
| subscription | String | Stripe subscription ID |
| email | String | Customer email address |
| amountSubtotal | Number | Subtotal amount in cents |
| amountTotal | Number | Total amount in cents |
| currency | String | Currency code (e.g., "usd") |
| plan | String | Plan name (e.g., "PRO") |
| billingPeriodValue | Number | Length of billing period |
| billingPeriodUnit | String | Unit of billing period (e.g., "month") |
| billingInterval | String | Legacy billing interval field |
| userId | String | User ID in our system |
| created | Date | When the checkout was created |
| expiresAt | Date | When the checkout session expires |
| startDate | Date | Subscription start date |
| expirationDate | Date | Subscription expiration date |
| roleId | Number | Role ID for the subscription |
| fullWebhook | Object | Complete webhook payload from Stripe |
| createdAt | Date | Document creation timestamp |
| updatedAt | Date | Document update timestamp |

#### Example

```json
{
  "_id": "682c505ad4980661ee19f26a", // ObjectId
  "id": "cs_test_b1bbv0Dy7irsogWQZxCcnbigLOVbTmmsvTFPHbulXSVJzThV5aDLliBeKt",
  "status": "complete",
  "mode": "subscription",
  "promotekitReferral": "8f740b2c-a617-41aa-b882-6859d415858c",
  "customer": "cus_SLTlOKJFKsomXB",
  "subscription": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
  "email": "<EMAIL>",
  "amountSubtotal": 79900, // Number (cents)
  "amountTotal": 79900, // Number (cents)
  "currency": "usd",
  "plan": "PRO",
  "billingPeriodValue": 1, // Number
  "billingPeriodUnit": "year",
  "billingInterval": "",
  "userId": "",
  "created": "2023-07-20T15:23:03.000Z", // Date
  "expiresAt": "2023-07-21T15:23:03.000Z", // Date
  "startDate": "2023-07-20T15:23:31.000Z", // Date
  "expirationDate": "2024-07-19T15:23:31.000Z", // Date
  "roleId": 4, // Number
  "fullWebhook": {
    // Full webhook payload omitted for brevity
  },
  "createdAt": "2023-07-20T15:23:38.208Z", // Date
  "updatedAt": "2023-07-20T15:23:38.208Z" // Date
}
```

### stripe.activation_tokens

Stores tokens that link Stripe subscriptions to user accounts.

#### Schema

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| token | String | Activation token (64-char hex string) |
| sessionId | String | Stripe checkout session ID |
| subscriptionId | String | Stripe subscription ID |
| customerId | String | Stripe customer ID |
| email | String | Customer email address |
| planName | String | Plan name (e.g., "PRO") |
| billingPeriodValue | Number | Length of billing period |
| billingPeriodUnit | String | Unit of billing period (e.g., "month") |
| billingInterval | String | Legacy billing interval field |
| createdAt | Date | Token creation timestamp |
| expiresAt | Date | Token expiration timestamp (7 days) |
| used | Boolean | Whether token has been used |
| activatedAt | Date | When token was activated (if used) |
| activatedBy | String | User ID who activated the token |

#### Example

```json
{
  "_id": "682c505bd4980661ee19f26b", // ObjectId
  "token": "54a2f511ae992e2cd5777b1a727c5616de755f1c9acd0eb264e4e1f591c19a22",
  "sessionId": "cs_test_b1bbv0Dy7irsogWQZxCcnbigLOVbTmmsvTFPHbulXSVJzThV5aDLliBeKt",
  "subscriptionId": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
  "customerId": "cus_SLTlOKJFKsomXB",
  "email": "<EMAIL>",
  "planName": "PRO",
  "billingPeriodValue": 1, // Number
  "billingPeriodUnit": "year",
  "billingInterval": "",
  "createdAt": "2023-07-20T15:23:39.705Z", // Date
  "expiresAt": "2023-07-27T15:23:39.705Z", // Date (7 days later)
  "used": true, // Boolean
  "activatedAt": "2023-07-20T15:24:49.500Z", // Date
  "activatedBy": "33ef82ea-5414-4a8a-831f-5a95af667c0d"
}
```

### stripe.log_customer

Logs customer subscription changes, providing an audit trail of subscription lifecycle events.

#### Schema

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| createdAt | Date | Log creation timestamp |
| eventId | String | Stripe event ID |
| eventType | String | Type of Stripe event |
| customer | String | Stripe customer ID |
| idUser | String | User ID in our system |
| subscription | String | Stripe subscription ID |
| cancelAtPeriodEnd | Boolean | Whether subscription will cancel at period end |
| cancelAt | Date/null | When subscription is scheduled to cancel |
| canceledAt | Number | Timestamp when subscription was canceled |
| currentPeriodEnd | Number | Timestamp when current billing period ends |
| beforeChange | Object | Subscription state before change |
| afterChange | Object | Subscription state after change |
| isImmediateCancellation | Boolean | Whether cancellation was immediate |

#### Example

```json
{
  "_id": "682c6c93ca93c5a901e5d52b", // ObjectId
  "createdAt": "2023-07-20T17:30:43.647Z", // Date
  "eventId": "evt_1RQogxBMwsd7cKYCZPrCter1",
  "eventType": "customer.subscription.deleted",
  "customer": "cus_SLUi0ntoscljD4",
  "idUser": "5c4fcc7b-261c-48ee-bf61-3bf2ad164a2e",
  "subscription": "sub_1RQnisBMwsd7cKYCdf5V2vi8",
  "cancelAtPeriodEnd": false, // Boolean
  "cancelAt": null,
  "canceledAt": 1747741842, // Number (Unix timestamp)
  "currentPeriodEnd": 1750416518, // Number (Unix timestamp)
  "beforeChange": {
    "status": "active",
    "cancel_at": null,
    "cancel_at_period_end": false,
    "canceled_at": null,
    "ended_at": null,
    "idRole": 4, // Number
    "expirationDate": "2023-07-21T14:35:08.000Z" // Date
  },
  "afterChange": {
    "status": "canceled",
    "canceled_at": 1747741842, // Number (Unix timestamp)
    "ended_at": 1747741842, // Number (Unix timestamp)
    "cancel_at": null,
    "cancel_at_period_end": false,
    "idRole": 8, // Number (Free tier)
    "expirationDate": "2023-07-20T17:30:43.647Z" // Date (set to now)
  },
  "isImmediateCancellation": true // Boolean
}
```

### Additional Collections

| Collection | Purpose |
|------------|---------|
| `stripe.log_billing_portal` | Logs customer portal access |
| `stripe.log_unhandled` | Logs events without specific handlers |

## User and Membership Data

User data and subscription information are stored in separate databases but linked via common identifiers.

### paymentdata.memberships

Links users to their subscription status and roles.

#### Schema

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| idUser | String | User ID in our system |
| walletAddress | String | User's wallet address |
| idRole | Number | Role ID (permission level) |
| startDate | Date | Subscription start date |
| expirationDate | Date | Subscription expiration date |
| createdAt | Date | Document creation timestamp |
| updatedAt | Date | Document update timestamp |
| email | String | User's email address |
| stripeCustomerId | String | Stripe customer ID |
| stripeSubscriptionId | String | Stripe subscription ID |
| referredBy | String | User ID of referrer |

#### Example

```json
{
  "_id": "682c50a0d4980661ee19f26c", // ObjectId
  "idUser": "33ef82ea-5414-4a8a-831f-5a95af667c0d",
  "idRole": 4, // Number (Pro tier)
  "stripeCustomerId": "cus_SLTlOKJFKsomXB",
  "stripeSubscriptionId": "sub_1RQmoJBMwsd7cKYCkDDENo5w",
  "email": "<EMAIL>",
  "walletAddress": null,
  "referredBy": "8f740b2c-a617-41aa-b882-6859d415858c",
  "startDate": "2023-07-20T15:23:03.000Z", // Date
  "expirationDate": "2023-07-20T16:48:59.650Z", // Date
  "createdAt": "2023-07-20T15:24:48.438Z", // Date
  "updatedAt": "2023-07-20T16:48:59.650Z" // Date
}
```

### stalkreact.users

Main user collection with authentication and linked accounts.

#### Schema

| Field | Type | Description |
|-------|------|-------------|
| _id | ObjectId | MongoDB document ID |
| id | String | User ID in our system |
| privyId | String | Privy authentication ID |
| enabled | Boolean | Whether user is enabled |
| idRole | Number | Role ID (permission level) |
| linkedAccounts | Array | Array of linked accounts (email, wallet) |
| createdAt | Date | User creation timestamp |
| updatedAt | Date | User update timestamp |
| oldDbEmail | String | Email from previous database |
| oldDbWallet | String | Wallet from previous database |
| didWatchVideo | Boolean | Whether user watched intro video |
| expirationDate | Date | Subscription expiration date |
| stripeCustomerId | String | Stripe customer ID |

#### Example

```json
{
  "_id": "682aab4487337a1161effafa", // ObjectId
  "id": "33ef82ea-5414-4a8a-831f-5a95af667c0d",
  "privyId": "did:privy:cmaujvvi6014djr0nohwi8qak",
  "enabled": true, // Boolean
  "idRole": 4, // Number (Pro tier)
  "linkedAccounts": [
    {
      "type": "email",
      "address": "<EMAIL>",
      "latestVerifiedAt": "2023-07-19T09:13:38.000Z" // Date
    },
    {
      "type": "wallet",
      "address": "7W1Utn4ZMYkjGxgiWYyA2VYKGVPyEBfb6nbc8Zdd2jkG",
      "chainType": "solana",
      "walletClientType": "privy",
      "latestVerifiedAt": "2023-07-19T09:13:41.000Z" // Date
    }
  ],
  "createdAt": "2023-07-19T09:13:40.776Z", // Date
  "updatedAt": "2023-07-20T16:49:00.678Z", // Date
  "expirationDate": "2023-07-20T16:48:59.650Z", // Date
  "oldDbEmail": "<EMAIL>",
  "stripeCustomerId": "cus_SLTlOKJFKsomXB"
}
```

### Relationship between Stripe and User data

The StalkChain system links Stripe data to user accounts through several key relationships:

```mermaid
flowchart LR
    %% Define styles
    classDef stripeClass fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef userClass fill:#e1f5fe,stroke:#01579b,color:#01579b
    
    %% Entities
    StripeCustomer["Stripe Customer<br>cus_123..."]:::stripeClass
    StripeSubscription["Stripe Subscription<br>sub_123..."]:::stripeClass
    User["User<br>id: user123..."]:::userClass
    Membership["Membership<br>idUser: user123..."]:::userClass
    
    %% Relationships
    StripeCustomer -->|stored as| User.stripeCustomerId
    StripeCustomer -->|stored as| Membership.stripeCustomerId
    StripeSubscription -->|stored as| Membership.stripeSubscriptionId
    User.id -->|matches| Membership.idUser
```

Key Identifiers:
1. **Stripe Customer ID** (`cus_xxx`): Stored in both `stalkreact.users` and `paymentdata.memberships`
2. **Stripe Subscription ID** (`sub_xxx`): Stored in `paymentdata.memberships`
3. **User ID**: Links `stalkreact.users` to `paymentdata.memberships`

During checkout:
1. The Stripe webhook system creates records in `stripe.checkout_completed`
2. Activation tokens are generated in `stripe.activation_tokens`
3. When a customer activates their account, the system links the Stripe data to their user account

For existing users:
1. The `stripeCustomerId` field ensures consistent identification
2. When subscriptions change, the system updates both `paymentdata.memberships` and `stalkreact.users`

### Role Management

StalkChain uses a role-based access control system that's integrated with the subscription system:

#### Role ID Mapping

| Role ID | Role Name | Description |
|---------|-----------|-------------|
| 3 | Legend | Highest tier subscription |
| 4 | Pro | Standard subscription |
| 8 | Free | No active subscription |

Roles are stored in two places:
1. `paymentdata.memberships.idRole`
2. `stalkreact.users.idRole`

#### Role Update Flow

```mermaid
flowchart LR
    %% Define styles
    classDef eventClass fill:#f5f5f5,stroke:#333,color:#333
    classDef decisionClass fill:#fff,stroke:#455a64,color:#455a64
    classDef actionClass fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef roleClass fill:#e3f2fd,stroke:#1976d2,color:#1976d2
    
    %% Event node
    Start["Subscription<br>Event"]:::eventClass
    
    %% First level: Event type
    Start --> NewSub["New<br>Subscription"]:::eventClass
    Start --> UpdateSub["Subscription<br>Update"]:::eventClass
    Start --> CancelSub["Subscription<br>Cancellation"]:::eventClass
    
    %% New subscription flow
    NewSub --> MapProduct["Map Product<br>to Role ID"]:::actionClass
    MapProduct --> UpdateMembership["Update Role in<br>Memberships"]:::actionClass
    UpdateMembership --> UpdateUser["Update Role in<br>User Record"]:::actionClass
    
    %% Update subscription flow
    UpdateSub --> ProductChanged{"Product<br>Changed?"}:::decisionClass
    ProductChanged -->|Yes| NewMapping["Map New Product<br>to Role ID"]:::actionClass
    ProductChanged -->|No| NoRoleChange["No Role<br>Change"]:::actionClass
    NewMapping --> UpdateMembership
    NoRoleChange --> UpdateExpiry["Update Expiration<br>Date Only"]:::actionClass
    
    %% Cancellation flow
    CancelSub --> CancelType{"Immediate<br>Cancellation?"}:::decisionClass
    CancelType -->|Yes| SetFree["Set Role to<br>Free (8)"]:::roleClass
    CancelType -->|No| KeepRole["Keep Current Role<br>Until Period End"]:::roleClass
    SetFree --> UpdateMembership
    KeepRole --> UpdateExpiry
```

Key role management features:
1. **Product-Based Role Assignment**: 
   - Legend plan (role 3) for highest tier products
   - Pro plan (role 4) for standard products
   - Free tier (role 8) for canceled subscriptions

2. **Dual Updates**:
   Changes to roles are always applied to both:
   - `paymentdata.memberships.idRole`
   - `stalkreact.users.idRole`

3. **Cancellation Handling**:
   - Immediate cancellations change role to Free (8) immediately
   - End-of-period cancellations keep the current role until the subscription expires

4. **Subscription Upgrades/Downgrades**:
   - When a user changes their subscription, the role is updated based on the new product
   - Role changes take effect immediately 