---
description: >-
  Comprehensive guide to Stripe integration in StalkChain, covering checkout flows, account activation, subscription management, webhooks, and data models.
---

**Target audience:** Back-end developers who need to understand, maintain, extend, or troubleshoot the Stripe payment infrastructure within StalkChain.

# Overview

StalkChain integrates with Stripe to manage subscriptions, process payments, and handle customer billing. This integration follows a webhook-driven architecture that processes events asynchronously, allowing for reliable payment tracking and account activation.

## Architecture

The Stripe integration consists of several key components:

1. **Checkout Flow** - Server-side creation of checkout sessions and client-side redirect to Stripe Checkout
2. **Webhook System** - Event processing for subscription lifecycle events
3. **Account Activation** - Token-based system for linking payments to user accounts
4. **Subscription Management** - Handling upgrades, downgrades, and cancellations

## Key Components

- **Stripe API Client** - Uses the official Stripe Node.js SDK
- **Webhook Handlers** - Process various event types from Stripe
- **Activation Token System** - Securely links payments to user accounts
- **MongoDB Collections** - Stores checkout, subscription, and user data

## Business Rules

- Stripe Customer IDs are stored with user records for consistent identification
- Subscription status changes trigger role updates in user accounts
- Activation tokens expire after 7 days
- Cancellations can be immediate or end-of-period
- Terms of service are required on the checkout page

## Checkout Flow Diagram

```mermaid
flowchart TD
    %% Define styles for different process types
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef stripeProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    classDef clientGroup fill:#e1f5fe,stroke:#01579b,stroke-width:1px
    classDef serverGroup fill:#e8f5e9,stroke:#2e7d32,stroke-width:1px
    classDef stripeGroup fill:#fff3e0,stroke:#e65100,stroke-width:1px

    %% Organize into logical process groups with subgraphs
    subgraph CheckoutProcess["Checkout Process"]
        Start("User clicks checkout button")
        CreateSession("Create Stripe Checkout Session")
        StripeCheckout("Stripe Hosted Checkout Page")
        Payment{"Payment completed?"}
        ErrorRedirect("Redirect to cancel page")
    end
    
    subgraph SuccessFlow["Success Page & Direct Activation"]
        SuccessRedirect("Redirect to success page")
        SuccessPage("Display checkout success page")
        DirectActivation("Activation from success page")
    end
    
    subgraph WebhookProcessing["Webhook Processing"]
        StripeWebhook("Stripe sends webhook event")
        ProcessWebhook("Process webhook event")
        StoreCheckout("Store checkout data")
        GenerateToken("Generate activation token")
        SendEmail("Send welcome email with token")
    end
    
    subgraph EmailActivation["Email-Based Activation"]
        UserActivation("User clicks activation link from email")
    end
    
    subgraph AccountSetup["Account Setup"]
        VerifyToken{"Token valid?"}
        LoginUser("Login with Privy")
        ActivateAccount("Link subscription to user")
        UpdateRoles("Update user roles & permissions")
        RedirectToDashboard("Redirect to dashboard")
        TokenInvalid("Show invalid token error")
        TokenExpired("Show token expired error")
    end

    %% Define the flow connections between and within groups
    Start --> CreateSession
    CreateSession --> StripeCheckout
    StripeCheckout --> Payment
    
    Payment -->|Success| SuccessRedirect
    Payment -->|Failure| ErrorRedirect
    SuccessRedirect --> SuccessPage
    
    %% Session-based token activation from success page
    SuccessPage -->|Has session_id| DirectActivation
    
    %% Webhook flow connections
    StripeWebhook -->|checkout.session.completed| ProcessWebhook
    ProcessWebhook --> StoreCheckout
    StoreCheckout --> GenerateToken
    GenerateToken --> SendEmail
    
    %% Connect the two activation methods to account setup
    DirectActivation --> VerifyToken
    UserActivation --> VerifyToken
    SendEmail -.->|Sends activation link| UserActivation
    
    %% Account setup flows
    VerifyToken -->|Yes| LoginUser
    VerifyToken -->|No| TokenInvalid
    VerifyToken -->|Expired| TokenExpired
    LoginUser --> ActivateAccount
    ActivateAccount --> UpdateRoles
    UpdateRoles --> RedirectToDashboard
    
    %% Apply styles to individual nodes
    class Start,SuccessRedirect,SuccessPage,DirectActivation,ErrorRedirect,UserActivation,LoginUser,TokenInvalid,TokenExpired,RedirectToDashboard clientProcess
    class CreateSession,ProcessWebhook,GenerateToken,SendEmail,ActivateAccount,UpdateRoles serverProcess
    class StripeCheckout,StripeWebhook stripeProcess
    class StoreCheckout databaseProcess
    class Payment,VerifyToken decisionPoint
    
    %% Apply styles to subgraphs
    class CheckoutProcess,SuccessFlow,EmailActivation clientGroup
    class WebhookProcessing,AccountSetup serverGroup
```

<!-- 
DOCUMENTATION HIERARCHY (FOR SEPARATE FILES):

overview.md
- Introduction to Stripe Integration
- Architecture and Components
- Key Principles and Business Rules
- Flow Diagrams

getting-started.md
- Environment Setup
- API Keys and Configuration
- Testing Tools
- Local Development

checkout-process.md
- Server-Side Implementation
  - Creating Checkout Sessions
  - Session Parameters and Options
  - Handling Success and Failures
- Subscription Management
  - Billing Periods and Trial Days
  - Pricing Configuration
- Client-Side Implementation (Brief overview with placeholder for front-end guide)

account-activation.md
- Activation Flow
  - Token Generation
  - Email Delivery
  - Token Verification
- User Authentication Integration
  - Privy Integration
  - Role Assignment
- Database Updates
  - Membership Creation/Updates
  - User Status Updates

subscription-management.md
- Subscription Lifecycle
  - Creation and Initial Setup
  - Trials and Free Tiers
- Customer Actions
  - Upgrades and Downgrades
  - Cancellations (Immediate vs. End of Period)
  - Refunds and Credits
- Stripe Customer Portal
  - Configuration
  - Handling Portal Session Events

webhook-system.md
- Webhook Configuration
  - Local Development Setup
  - Production Setup
- Event Processing Pipeline
  - Event Verification and Validation
  - Event Routing
- Event Handlers
  - Checkout Events
  - Subscription Events
  - Customer Events
  - Payment Events

data-models.md
- Stripe Collections in MongoDB
  - checkout_completed
  - activation_tokens
  - webhook_events
  - log_customer
- User and Membership Data
  - Relationship between Stripe and User data
  - Role Management

troubleshooting.md
- Common Issues
- Debugging Techniques
- Testing Webhooks
- Error Handling

KEY PRINCIPLES:
* Clarity and Conciseness: Avoid technical jargon and ensure all documentation is easy to understand, using diagrams, flowcharts, and screenshots where appropriate. 
* Accuracy and Timeliness: Regularly update documentation to reflect changes in code, processes, or environments. Establish clear ownership and review processes to maintain accuracy.
* Lightweight and Focused: Avoid overly detailed documentation. Focus on what developers need to know to get their job done efficiently.

DOCUMENTATION RULES:
1. Add placeholders for screenshots ONLY if they will enhance the dev docs
2. Follow the key principles outlined above
3. Use "Docs for Developers" framework - task-based organization, progressive disclosure
4. Use example outputs, formatted in JSON, when applicable
5. Explain data structures, like columns of a postgres table, or returns from an API
6. Show imports on code blocks for imported components (e.g., include the import for StripeCheckoutButton.js when it's used in a code block)
7. Include the file path as a comment at the top of code blocks (e.g., // actions/checkout-v2/stripe.js)

FLOWCHART RULES:
1. Stick to "Visual Explanations" by Edward Tufte methodology
2. Use conceptual grouping judiciously - Group related elements into subgraphs only when complexity requires it; prefer a flat structure for simpler flows.
3. Establish clear visual hierarchy - Apply subtle color differences to distinguish process types; limit palette to 3-5 colors with consistent meaning.
4. Maintain directional clarity - Design flow in one primary direction (top-to-bottom or left-to-right) to minimize line crossings.
5. Balance information density - Include only essential details relevant to the current level of abstraction; create separate diagrams for deeper dives.
6. Apply consistent symbolism - Use standard shapes purposefully (diamonds for decisions, rectangles for processes) across all related diagrams.

AI RULES:
1. Gather all relevant information first before applying.
2. Stick to proven methods over theory.

TEMPLATE:
The code block below describes the top of every .md file.

```
---
description: >-
  Write a page description with a max length of 200 characters.
---

**Target audience:** Write 1-2 lines who this document is intended for and to do what with it.
```
-->

