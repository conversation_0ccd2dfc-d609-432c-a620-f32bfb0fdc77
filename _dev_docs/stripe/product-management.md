---
description: >-
  Guide to managing Stripe products, pricing configuration, and handling direct payment flows in StalkChain.
---

**Target audience:** Backend developers who need to configure, update, or troubleshoot Stripe product and pricing structures within the StalkChain application.

# Product and Pricing Management

## Overview

StalkChain's subscription system relies on a centralized pricing configuration that maps Stripe products and prices to application roles. This document covers how these components work together and provides guidance on managing them effectively.

```mermaid
flowchart TD
    %% Define styles
    classDef configFile fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef envConfig fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef stripeEntity fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef appEntity fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    
    %% Config sources
    A["static/pricingData.json"]:::configFile
    B[".env Files"]:::envConfig
    C["Stripe Dashboard"]:::stripeEntity
    
    %% Application components
    D["Checkout Button Component"]:::appEntity
    E["Webhook Handler"]:::appEntity
    F["Role Assignment"]:::appEntity
    
    %% Flow
    A --> D
    A --> E
    B --> E
    C --> A
    D --> |"Uses product/price IDs"| G["Stripe Checkout"]:::stripeEntity
    E --> |"Determines product details"| F
    G --> |"Webhook events"| E
```

## Configuration Components

### 1. Pricing Data JSON

The central source of truth for pricing configuration is the `static/pricingData.json` file:

```javascript
// static/pricingData.json
{
    "pricingTables": {
        "dev": "prctbl_1RPisQBMwsd7cKYCk5GrcKA9",
        "prod": "prctbl_1RPib3BMwsd7cKYCBTZVHeVf"
    },
    "plans": [
        {
            "name": "Free",
            "price": {
                "monthly": 0,
                "yearly": 0
            },
            "priceIds": {
                "dev": {
                    "monthly": null,
                    "yearly": null
                },
                "prod": {
                    "monthly": null,
                    "yearly": null
                }
            },
            "productIds": {
                "dev": null,
                "prod": null
            },
            // Other plan details...
        },
        {
            "name": "PRO",
            "price": {
                "monthly": 149,
                "yearly": 799
            },
            "priceIds": {
                "dev": {
                    "monthly": "price_1QuqN4BMwsd7cKYCDS98KqhJ",
                    "yearly": "price_1QwypcBMwsd7cKYCEIuTg4VI"
                },
                "prod": {
                    "monthly": "price_1R3Cc9BMwsd7cKYCnp7opO9f",
                    "yearly": "price_1R3Cc9BMwsd7cKYCoNuCYtfl"
                }
            },
            "productIds": {
                "dev": "prod_RoTJsHMs5dRwtK",
                "prod": "prod_Rx6p8VQAyZ5p1S"
            },
            // Other plan details...
        }
    ]
}
```

This file maintains:
- Price values displayed to customers
- Stripe Price IDs for each billing period (monthly/yearly) and environment (dev/prod)
- Stripe Product IDs for each environment
- Feature lists and marketing copy for the pricing page

### 2. Environment Variables

Key Stripe product IDs are also stored in environment variables:

```
# .env variables for Stripe products
STRIPE_DEGEN_PLAN_PRODUCT_ID=prod_RoTJsHMs5dRwtK  # PRO plan (role 4) in dev
STRIPE_LEGEND_PLAN_PRODUCT_ID=prod_RqgETqFiY4RXDp  # Legend plan (role 3) in dev
```

These environment variables are used primarily in webhook handlers for backward compatibility and role mapping.

### 3. Role Mapping

The application maps product IDs to role IDs according to this structure:

| Plan Name | Role ID | Description |
|-----------|---------|-------------|
| Legend | 3 | Highest tier with all features |
| PRO | 4 | Standard paid tier |
| Free | 8 | Free tier with limited features |

This mapping is used in both the checkout flow and webhook processing to assign the appropriate permissions to users.

## Product Configuration Workflows

### 1. Frontend Checkout Flow

The checkout component retrieves the appropriate price ID based on the plan and billing period:

```javascript
// components/checkout/PricingPlanCard.js
const getPriceId = () => {
  // Check if we have priceIds directly on the plan object
  if (plan.priceIds) {
    const env = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
    const period = isYearly ? 'yearly' : 'monthly';
    
    return plan.priceIds[env]?.[period] || null;
  }
  
  return null;
};
```

This price ID is then used to create a Stripe checkout session:

```javascript
// components/UI/Buttons/StripeCheckoutButton.js
const handleCheckout = async () => {
  // Prepare the checkout data
  const checkoutData = {
    priceId,
    userId,
    planName,
    referralId: currentReferralId,
    billingPeriodValue,
    billingPeriodUnit,
    trialDays
  };

  const response = await createStripeCheckout(checkoutData);

  if (response.success && response.url) {
    // Redirect to Stripe Checkout
    window.location.href = response.url;
  }
};
```

### 2. Direct Payment Link Flow

When customers pay through direct payment links (bypassing the frontend), the webhook handler must identify products and assign roles:

```javascript
// actions/stripe-webhooks/checkout/handler.js
// Get the product ID from the subscription
const productId = plan.product;

if (productId) {
  const environment = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
  
  // Find the plan in our pricing data that matches this product ID
  const matchingPlan = pricingData.plans.find(plan => 
    plan.productIds && plan.productIds[environment] === productId
  );
  
  if (matchingPlan) {
    // Update the plan name and set the role ID
    parsedData.plan = matchingPlan.name;
    
    // Determine role ID based on plan name
    if (matchingPlan.name.toLowerCase() === 'legend') {
      parsedData.roleId = 3;  // Legend membership
    } else if (matchingPlan.name.toLowerCase() === 'pro') {
      parsedData.roleId = 4;  // Pro membership
    }
  }
}
```

This approach ensures that even when users bypass our frontend (direct payment links, API-created checkout sessions), they still get assigned the correct role based on what they purchased.

## Adding or Updating Products

Follow these steps to add a new product or update existing ones:

### Step 1: Create Products in Stripe Dashboard

1. Log in to the [Stripe Dashboard](https://dashboard.stripe.com)
2. Navigate to Products & Prices section
3. Create a new product or select an existing one
4. Add prices with appropriate billing intervals (monthly/yearly)
5. Note the Product ID and Price IDs for both test and live modes

### Step 2: Update pricingData.json

Add the new product or update existing products in `static/pricingData.json`:

```javascript
{
  "name": "NEW_PLAN",
  "price": {
    "monthly": 299,
    "yearly": 2999
  },
  "priceIds": {
    "dev": {
      "monthly": "price_dev_monthly_id",
      "yearly": "price_dev_yearly_id"
    },
    "prod": {
      "monthly": "price_prod_monthly_id",
      "yearly": "price_prod_yearly_id"
    }
  },
  "productIds": {
    "dev": "prod_dev_id",
    "prod": "prod_prod_id"
  },
  // Other plan details...
}
```

### Step 3: Update Role Mapping Logic

Modify the webhook handler logic in `actions/stripe-webhooks/checkout/handler.js` to map the new product to the appropriate role ID:

```javascript
// Add condition for the new plan
if (matchingPlan.name.toLowerCase() === 'legend') {
  parsedData.roleId = 3;
} else if (matchingPlan.name.toLowerCase() === 'pro') {
  parsedData.roleId = 4;
} else if (matchingPlan.name.toLowerCase() === 'new_plan') {
  parsedData.roleId = 5;  // Assign appropriate role ID
}
```

### Step 4: Update Environment Variables (if needed)

If the new product needs to be referenced in environment variables:

```
# Add to .env files
STRIPE_NEW_PLAN_PRODUCT_ID=prod_new_plan_id
```

Also update any references to these environment variables in the code.

### Step 5: Test Both Checkout Flows

1. **Frontend Checkout**: Test purchasing through the website UI
2. **Direct Payment**: Test purchasing through direct Stripe payment links or Checkout Sessions created via API

Verify that:
- The correct price is shown on the frontend
- Checkout completes successfully
- Webhook processes the subscription correctly
- User is assigned the correct role

## Handling Plan Changes

When a user changes their subscription plan through the Stripe Customer Portal, the system identifies the product change and updates their role using a similar mapping logic:

```javascript
// actions/stripe-webhooks/customer/handler.js
if (afterChange?.plan && beforeChange?.plan) {
  const STRIPE_DEGEN_PLAN_PRODUCT_ID = process.env.STRIPE_DEGEN_PLAN_PRODUCT_ID; // idRole 4
  const STRIPE_LEGEND_PLAN_PRODUCT_ID = process.env.STRIPE_LEGEND_PLAN_PRODUCT_ID; // idRole 3
  
  const oldProductId = beforeChange.plan.product;
  const newProductId = afterChange.plan.product;
  
  // Only update if the product has changed
  if (oldProductId !== newProductId) {
    // Determine the new role based on the product
    if (newProductId === STRIPE_DEGEN_PLAN_PRODUCT_ID) {
      newIdRole = 4; // Pro
    } else if (newProductId === STRIPE_LEGEND_PLAN_PRODUCT_ID) {
      newIdRole = 3; // Legend
    }
  }
}
```

This ensures that when users upgrade or downgrade their subscription, their permissions are updated accordingly.

## Troubleshooting

### Missing or Incorrect Role Assignments

If users aren't assigned the correct roles after payment:

1. Check that the product ID in Stripe matches what's in `pricingData.json`
2. Verify the webhook handler logic correctly maps product ID to role ID
3. Examine the webhook processing logs for any errors
4. Check if the webhook was received and processed (see `stripe.webhook_events` collection)

### Price ID Not Found

If checkout fails with price ID errors:

1. Verify that the price ID exists in Stripe
2. Confirm environment matching (dev vs prod)
3. Ensure price IDs are correctly entered in `pricingData.json`
4. Check browser console for any JavaScript errors during checkout

### Direct Payment Link Issues

For problems with direct payment links:

1. Check that the webhook is correctly identifying the product ID
2. Verify that the product is included in `pricingData.json` with correct IDs
3. Ensure role mappings are correctly defined for the product
4. Examine the stored checkout data in the `stripe.checkout_completed` collection

## Best Practices

1. **Keep Configurations in Sync**: Always update both Stripe Dashboard and `pricingData.json` when making changes
2. **Use Descriptive Names**: Make product and price names clearly indicate the plan and billing period
3. **Test in Dev First**: Always test changes in the development environment before deploying to production
4. **Backup Before Changes**: Take a backup of configuration files before making changes
5. **Document Role IDs**: Maintain clear documentation mapping role IDs to features and access levels
6. **Use Consistent IDs**: Follow a consistent naming convention for product and price IDs
7. **Version Control**: Keep pricing changes in version control to track history

## Related Documentation

For more information on related topics, see:
- [Subscription Management](_dev_docs/stripe/subscription-management.md) for details on subscription lifecycle
- [Checkout Process](_dev_docs/stripe/checkout-process.md) for the complete checkout flow
- [Webhook System](_dev_docs/stripe/webhook-system.md) for how webhooks process subscription events 