---
description: >-
  Comprehensive guide for implementing Stripe checkout in React applications, covering pricing configuration, checkout components, subscription management, and frontend integration patterns.
---

**Target audience:** Frontend developers who need to implement, customize, or troubleshoot Stripe checkout functionality in StalkChain's React application.

# Stripe Frontend Integration

## Overview

StalkChain's Stripe checkout integration provides a complete system for handling subscription payments with a focus on a seamless user experience. This guide covers the frontend implementation details including:

- Configuring pricing plans and tables
- Using pricing UI components
- Implementing checkout buttons
- Handling trials, referrals, and different billing periods
- Common integration patterns

## Pricing Configuration

### Understanding pricingData.json

The central source for pricing configuration is the `static/pricingData.json` file, which contains all plan details, pricing tiers, and Stripe product/price IDs:

```javascript
// static/pricingData.json
{
    "pricingTables": {
        "dev": "prctbl_1RPisQBMwsd7cKYCk5GrcKA9",
        "prod": "prctbl_1RPib3BMwsd7cKYCBTZVHeVf"
    },
    "plans": [
        {
            "name": "PRO",
            "price": {
                "monthly": 149,
                "yearly": 799
            },
            "priceIds": {
                "dev": {
                    "monthly": "price_1QuqN4BMwsd7cKYCDS98KqhJ",
                    "yearly": "price_1QwypcBMwsd7cKYCEIuTg4VI"
                },
                "prod": {
                    "monthly": "price_1R3Cc9BMwsd7cKYCnp7opO9f",
                    "yearly": "price_1R3Cc9BMwsd7cKYCoNuCYtfl"
                }
            },
            "productIds": {
                "dev": "prod_RoTJsHMs5dRwtK",
                "prod": "prod_Rx6p8VQAyZ5p1S"
            },
            "descriptions": [...],
            "features": [...],
            "cta": "Get PRO",
            "popular": true,
            "level": 70
        },
        // Additional plans...
    ]
}
```

#### Key Sections

| Section | Description | Usage |
|---------|-------------|-------|
| `pricingTables` | Stripe pricing table IDs | For embedded pricing tables (optional) |
| `plans[].name` | Plan name | Displayed to users and sent to Stripe metadata |
| `plans[].price` | Pricing details | Used for UI display (monthly/yearly) |
| `plans[].priceIds` | Stripe price IDs | Required for checkout, environment & billing-period specific |
| `plans[].productIds` | Stripe product IDs | Used for webhook processing & identifying subscriptions |
| `plans[].features` | Feature list | Displayed in pricing cards |
| `plans[].popular` | Highlight flag | Used for UI emphasis |

### Adding or Updating Plans

To add a new pricing plan or update an existing one:

1. **Create the product and prices in Stripe Dashboard**:
   - Log in to the [Stripe Dashboard](https://dashboard.stripe.com)
   - Create a new product or select an existing one
   - Add prices with appropriate billing intervals (monthly/yearly)
   - Note the Product ID and Price IDs for both test and live modes

2. **Update the pricingData.json file**:
   ```javascript
   // Add a new plan or update an existing one
   {
     "name": "NEW_PLAN",
     "price": {
       "monthly": 299,
       "yearly": 2999
     },
     "priceIds": {
       "dev": {
         "monthly": "price_dev_id_from_stripe",
         "yearly": "price_dev_yearly_id_from_stripe"
       },
       "prod": {
         "monthly": "price_prod_id_from_stripe",
         "yearly": "price_prod_yearly_id_from_stripe"
       }
     },
     "productIds": {
       "dev": "prod_dev_id_from_stripe",
       "prod": "prod_prod_id_from_stripe"
     },
     // Features, descriptions, etc.
   }
   ```

3. **Verify plan details**:
   - Ensure all price values match what's in Stripe
   - Confirm IDs are correct for both environments
   - Test the plan in the development environment before deploying to production

## Pricing UI Components

StalkChain includes ready-to-use components for displaying pricing plans and enabling checkout.

### PricingSelect Component

The `PricingSelect` component displays pricing plans with monthly/yearly toggle:

```jsx
// components/checkout/PricingSelect.js
import { useState } from "react";
import PricingPlanCard from "./PricingPlanCard";
import { Tabs, Tab } from "@heroui/tabs";
import pricingData from "@/static/pricingData.json";

const PricingSelect = ({ showTitle = true, availableUpgradePlans }) => {
  const [isYearly, setIsYearly] = useState(true);
  
  return (
    <div id="plans" className="flex-1 scroll-mt-24 w-full bg-white mt-6 pt-6 pb-16">
      <div className="max-w-screen-xl mx-auto px-4">
        {/* Billing period toggle */}
        <div className="bg-neutral-100 p-0.5 rounded-full">
          <Tabs
            aria-label="Billing period"
            selectedKey={isYearly ? "yearly" : "monthly"}
            onSelectionChange={(key) => setIsYearly(key === "yearly")}
          >
            <Tab key="monthly" title="Monthly" />
            <Tab key="yearly" title="Yearly" />
          </Tabs>
        </div>
        
        {/* Plan cards grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
          {(availableUpgradePlans || pricingData.plans)?.map((plan, index) => (
            <PricingPlanCard
              key={index}
              plan={plan}
              isYearly={isYearly}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default PricingSelect;
```

#### Usage Examples

**Basic Usage**:
```jsx
import PricingSelect from "@/components/checkout/PricingSelect";

export default function PricingPage() {
  return (
    <div className="container">
      <PricingSelect />
    </div>
  );
}
```

**With Custom Plans**:
```jsx
import PricingSelect from "@/components/checkout/PricingSelect";
import pricingData from "@/static/pricingData.json";

export default function UpgradePage() {
  // Filter to only show paid plans
  const paidPlans = pricingData.plans.filter(plan => 
    plan.price.monthly > 0
  );
  
  return (
    <div className="container">
      <PricingSelect 
        availableUpgradePlans={paidPlans}
        showTitle={false}
      />
    </div>
  );
}
```

### PricingPlanCard Component

The `PricingPlanCard` component displays individual plan details and checkout buttons:

```jsx
// components/checkout/PricingPlanCard.js
import { usePrivy } from "@privy-io/react-auth";
import StripeCheckoutButton from "@/components/UI/Buttons/StripeCheckoutButton";
import CryptoCheckoutButton from "@/components/UI/Buttons/CryptoCheckoutButton";

export default function PricingPlanCard({
  plan,
  isYearly,
}) {
  const { user } = usePrivy();
  
  // Calculate the display price
  const yearlyAsMonthly = Math.round(plan.price.yearly / 12);
  const displayPrice = isYearly ? yearlyAsMonthly : plan.price.monthly;
  
  // Get appropriate price ID for Stripe checkout
  const getPriceId = () => {
    const env = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
    const period = isYearly ? 'yearly' : 'monthly';
    
    return plan.priceIds[env]?.[period] || null;
  };
  
  return (
    <Card className="p-6 bg-white border border-neutral-200 rounded-xl">
      {/* Plan header */}
      <div className="mb-6 flex justify-between items-start">
        <h3 className="text-2xl">{plan.name}</h3>
        {plan.popular && (
          <span className="bg-emerald-600 text-white px-3 py-1 rounded-full text-xs">
            Most Popular
          </span>
        )}
      </div>
      
      {/* Pricing details */}
      <div className="mb-6">
        <div className="flex items-baseline gap-2">
          <span className="text-4xl">${displayPrice}</span>
          <span className="text-neutral-600">/month</span>
        </div>
        {isYearly && (
          <div className="text-sm text-neutral-500 mt-1">
            Billed annually as ${plan.price.yearly}
          </div>
        )}
      </div>
      
      {/* Features list */}
      <div className="space-y-3 mb-6">
        {plan.features.map((feature, index) => (
          <div key={index} className="flex gap-3 items-center">
            <CheckIcon className="flex-shrink-0 w-5 h-5" />
            <div>
              <div className="font-medium">{feature.title}</div>
              {feature.description && (
                <div className="text-sm text-neutral-500 mt-0.5">
                  {feature.description}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
      
      {/* Checkout buttons */}
      <div className="space-y-2">
        <StripeCheckoutButton
          userId={user?.id}
          priceId={getPriceId()}
          planName={plan.name}
          billingPeriodValue={isYearly ? 12 : 1}
          billingPeriodUnit="month"
          trialDays={plan.trialDays || 0}
        >
          {plan.cta || "Pay with Card"}
        </StripeCheckoutButton>
        
        {isYearly && (
          <>
            <div className="flex items-center justify-center my-2">
              <div className="h-px bg-neutral-200 w-16"></div>
              <span className="px-4 text-neutral-500 text-sm">or</span>
              <div className="h-px bg-neutral-200 w-16"></div>
            </div>
            
            <CryptoCheckoutButton 
              planName={plan.name}
              period="yearly"
              userAuth={user}
            />
          </>
        )}
      </div>
    </Card>
  );
}
```

## Checkout Buttons

### StripeCheckoutButton Component

The core component for initiating Stripe checkout is the `StripeCheckoutButton`:

```jsx
// components/UI/Buttons/StripeCheckoutButton.js
import { useState, useEffect } from 'react';
import { Button } from '@heroui/button';
import { createStripeCheckout } from '@/actions/checkout-v2/stripe';

/**
 * Stripe checkout button component
 * @param {Object} props
 * @param {string} props.userId - User ID for tracking
 * @param {string} props.priceId - Stripe price ID for the subscription
 * @param {string} props.planName - Name of the plan (PRO or Legend)
 * @param {number} props.billingPeriodValue - The billing period value (e.g., 1, 12)
 * @param {string} props.billingPeriodUnit - The billing period unit (month, year)
 * @param {number} props.trialDays - Number of trial days (default: 0 for no trial)
 * @param {ReactNode} props.children - Button text
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.variant - Button variant
 * @param {string} props.color - Button color
 * @param {string} props.size - Button size
 */
export default function StripeCheckoutButton({
  userId,
  priceId,
  planName = 'PRO',
  billingPeriodValue = 1,
  billingPeriodUnit = 'month',
  trialDays = 0,
  children = 'Pay with Card',
  className = '',
  variant = 'solid',
  color = 'primary',
  size = 'lg',
  ...props
}) {
  const [isLoading, setIsLoading] = useState(false);
  const [referralId, setReferralId] = useState(null);

  // Get the promotekit referral ID from the window object
  useEffect(() => {
    if (typeof window !== 'undefined') {
      if (window.promotekit_referral) {
        setReferralId(window.promotekit_referral);
      } else {
        const checkReferralInterval = setInterval(() => {
          if (window.promotekit_referral) {
            setReferralId(window.promotekit_referral);
            clearInterval(checkReferralInterval);
          }
        }, 500);
        
        return () => clearInterval(checkReferralInterval);
      }
    }
  }, []);

  const handleCheckout = async () => {
    if (!priceId) {
      console.error('No price ID provided');
      return;
    }

    // Get the current referral ID
    const currentReferralId = typeof window !== 'undefined' 
      ? window.promotekit_referral 
      : referralId;

    // Prepare the checkout data
    const checkoutData = {
      priceId,
      userId,
      planName,
      referralId: currentReferralId,
      billingPeriodValue,
      billingPeriodUnit,
      trialDays
    };

    try {
      setIsLoading(true);
      const response = await createStripeCheckout(checkoutData);

      if (response.success && response.url) {
        // Redirect to Stripe Checkout
        window.location.href = response.url;
      } else {
        console.error('Failed to create checkout session:', response.error);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      setIsLoading(false);
    }
  };

  return (
    <Button
      color={color}
      size={size}
      variant={variant}
      className={`w-full ${className}`}
      onClick={handleCheckout}
      isLoading={isLoading}
      {...props}
    >
      {children}
    </Button>
  );
}
```

#### Usage Examples

**Basic Usage**:
```jsx
import StripeCheckoutButton from '@/components/UI/Buttons/StripeCheckoutButton';

// For a monthly subscription
<StripeCheckoutButton
  userId={user?.id}
  priceId="price_1234567890"
  planName="PRO"
>
  Subscribe Now
</StripeCheckoutButton>

// For a yearly subscription
<StripeCheckoutButton
  userId={user?.id}
  priceId="price_yearly_id"
  planName="PRO"
  billingPeriodValue={12}
  billingPeriodUnit="month"
>
  Subscribe Yearly
</StripeCheckoutButton>
```

**With Free Trial**:
```jsx
<StripeCheckoutButton
  userId={user?.id}
  priceId="price_1234567890"
  planName="PRO"
  trialDays={14}
>
  Start 14-Day Free Trial
</StripeCheckoutButton>
```

**Custom Styling**:
```jsx
<StripeCheckoutButton
  userId={user?.id}
  priceId="price_1234567890"
  planName="PRO"
  color="secondary"
  variant="bordered"
  size="md"
  className="rounded-full px-8"
>
  Upgrade to PRO
</StripeCheckoutButton>
```

### Server Actions

The checkout button leverages server actions to create a Stripe checkout session:

```javascript
// actions/checkout-v2/stripe.js
export async function createStripeCheckout({ 
  priceId, 
  userId = null, 
  planName = 'PRO',
  referralId = null,
  billingPeriodValue = 1,
  billingPeriodUnit = 'month',
  trialDays = 0
}) {
  try {
    // Sanitize all inputs before using them
    const sanitizedPriceId = sanitizeInput(priceId);
    const sanitizedUserId = sanitizeInput(userId);
    const sanitizedPlanName = sanitizeInput(planName);
    const sanitizedReferralId = sanitizeInput(referralId);
    const sanitizedBillingPeriodValue = parseInt(sanitizeInput(billingPeriodValue)) || 1;
    const sanitizedBillingPeriodUnit = sanitizeInput(billingPeriodUnit);
    const sanitizedTrialDays = parseInt(sanitizeInput(trialDays)) || 0;
    
    // Prepare metadata with sanitized values
    const metadata = {
      planName: sanitizedPlanName,
      billingPeriodValue: sanitizedBillingPeriodValue,
      billingPeriodUnit: sanitizedBillingPeriodUnit,
    };

    // Add user ID to metadata if available
    if (sanitizedUserId) {
      metadata.userId = sanitizedUserId;
    }

    // Create checkout session options
    const sessionOptions = {
      payment_method_types: ['card'],
      line_items: [
        {
          price: sanitizedPriceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${process.env.SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${process.env.SITE_URL}/`,
      metadata,
      client_reference_id: sanitizedReferralId || undefined,
      allow_promotion_codes: true,
      billing_address_collection: 'auto',
      consent_collection: {
        terms_of_service: 'required',
      },
      // ...additional options
    };

    // Add trial period if specified
    if (sanitizedTrialDays > 0) {
      sessionOptions.subscription_data = {
        trial_period_days: sanitizedTrialDays,
      };
    }

    // Create a checkout session with Stripe
    const session = await stripe.checkout.sessions.create(sessionOptions);

    return {
      success: true,
      url: session.url,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Failed to create checkout session',
    };
  }
}
```

This server action:
1. Sanitizes all inputs for security
2. Configures the checkout session with plan details
3. Adds metadata for tracking subscriptions
4. Sets up trial periods when requested
5. Returns a URL for redirecting to Stripe's hosted checkout

## Advanced Implementation Patterns

### Implementing Free Trials

To add a free trial to a subscription plan:

1. **Update pricingData.json**:
   ```javascript
   {
     "name": "PRO",
     "trialDays": 14,
     // Other plan details...
   }
   ```

2. **Pass trial days to the checkout button**:
   ```jsx
   <StripeCheckoutButton
     userId={user?.id}
     priceId={getPriceId()}
     planName="PRO"
     trialDays={14}
   >
     Start 14-Day Free Trial
   </StripeCheckoutButton>
   ```

3. **Adjust button text to indicate trial**:
   ```jsx
   // Dynamically set button text based on trial days
   const buttonText = plan.trialDays 
     ? `Start ${plan.trialDays}-Day Free Trial` 
     : 'Subscribe Now';
   
   <StripeCheckoutButton
     // ...props
   >
     {buttonText}
   </StripeCheckoutButton>
   ```

### Handling Billing Periods

To support both monthly and yearly billing:

1. **Ensure both price IDs are in pricingData.json**:
   ```javascript
   "priceIds": {
     "dev": {
       "monthly": "price_dev_monthly_id",
       "yearly": "price_dev_yearly_id"
     },
     "prod": {
       "monthly": "price_prod_monthly_id",
       "yearly": "price_prod_yearly_id"
     }
   },
   ```

2. **Use a toggle for user selection**:
   ```jsx
   const [isYearly, setIsYearly] = useState(true);
   
   // Toggle component
   <Tabs
     selectedKey={isYearly ? "yearly" : "monthly"}
     onSelectionChange={(key) => setIsYearly(key === "yearly")}
   >
     <Tab key="monthly" title="Monthly" />
     <Tab key="yearly" title="Yearly" />
   </Tabs>
   ```

3. **Pass correct values to checkout button**:
   ```jsx
   <StripeCheckoutButton
     userId={user?.id}
     priceId={isYearly ? yearlyPriceId : monthlyPriceId}
     planName={plan.name}
     billingPeriodValue={isYearly ? 12 : 1}
     billingPeriodUnit="month"
   >
     Subscribe Now
   </StripeCheckoutButton>
   ```

### Integrating Referral Tracking

The `StripeCheckoutButton` component automatically integrates with PromoteKit for comprehensive referral tracking. This integration enables affiliate marketing and commission tracking without requiring any additional code.

#### How Referral Tracking Works

1. **Automatic Detection**:
   The component automatically detects the `promotekit_referral` ID from the browser's `window` object:
   
   ```javascript
   // From StripeCheckoutButton.js
   useEffect(() => {
     if (typeof window !== 'undefined') {
       if (window.promotekit_referral) {
         setReferralId(window.promotekit_referral);
       } else {
         // Check periodically in case PromoteKit loads after the component
         const checkReferralInterval = setInterval(() => {
           if (window.promotekit_referral) {
             setReferralId(window.promotekit_referral);
             clearInterval(checkReferralInterval);
           }
         }, 500);
         
         return () => clearInterval(checkReferralInterval);
       }
     }
   }, []);
   ```

2. **Checkout Integration**:
   The referral ID is included in the Stripe checkout session as the `client_reference_id`:
   
   ```javascript
   // From the createStripeCheckout server action
   const sessionOptions = {
     // Other options...
     client_reference_id: sanitizedReferralId || undefined,
   };
   ```

3. **Webhook Processing**:
   When a checkout is completed, the webhook handler extracts the referral ID and processes it for affiliate attribution.

#### Supported URL Parameters

PromoteKit automatically detects these referral parameters in the URL:

| Parameter   | Example                                                | Description               |
| ----------- | ------------------------------------------------------ | ------------------------- |
| `ref`       | `https://stalkchain.com/pricing?ref=museonchain`       | Primary referral code     |
| `aff`       | `https://stalkchain.com/pricing?aff=museonchain`       | Alternative referral code |
| `promocode` | `https://stalkchain.com/pricing?promocode=museonchain` | Promotional code format   |
| `via`       | `https://stalkchain.com/pricing?via=museonchain`       | Source format             |

> **Note:** The PromoteKit dashboard provides `?via={code}` links by default for affiliates to share.

#### Implementation in StalkChain

The referral tracking system is implemented through scripts added to the `app/layout.js` file:

```jsx
// In app/layout.js
<head>
  {/* Other head elements */}
  
  {/* PromoteKit tracking script with organization UUID */}
  <script 
    async 
    src="https://cdn.promotekit.com/promotekit.js" 
    data-promotekit="{organization-uuid}"
  />
  
  {/* Stripe integration script */}
  <script dangerouslySetInnerHTML={{
    __html: `
      document.addEventListener("DOMContentLoaded", function () {
          setTimeout(function () {
              // Add referral ID to Stripe checkout links
              document.querySelectorAll('a[href^="https://buy.stripe.com/"]').forEach(function (link) {
                  const oldBuyUrl = link.getAttribute("href");
                  const referralId = window.promotekit_referral;
                  if (!oldBuyUrl.includes("client_reference_id")) {
                      const newBuyUrl = oldBuyUrl + "?client_reference_id=" + referralId;
                      link.setAttribute("href", newBuyUrl);
                  }
              });

              // Add referral ID to Stripe pricing tables
              document.querySelectorAll("[pricing-table-id]").forEach(function (element) {
                  element.setAttribute("client-reference-id", window.promotekit_referral);
              });

              // Add referral ID to Stripe buy buttons
              document.querySelectorAll("[buy-button-id]").forEach(function (element) {
                  element.setAttribute("client-reference-id", window.promotekit_referral);
              });
          }, 1500); // Wait 1.5 seconds after page load
      });
    `
  }} />
</head>
```

#### Testing Referrals

To test the referral tracking system:

1. **Debug Mode**:
   Enable debugging on the checkout button to see referral data:
   ```jsx
   <StripeCheckoutButton
     userId={user?.id}
     priceId="price_1234567890"
     planName="PRO"
     debug={true}
   />
   ```

2. **Manual Testing**:
   For testing, you can manually set a referral ID:
   ```javascript
   // Set a test referral ID in the browser console
   window.promotekit_referral = "ref_test_id";
   ```

3. **URL Parameters**:
   Test a full flow by adding one of the supported referral parameters to a URL:
   ```
   https://stalkchain.com/?via=affiliate123
   ```

> **Important Rule**: Always use `client_reference_id` for the PromoteKit referral ID, never for the user ID. As noted in the Stripe integration code: `client_reference_id should ONLY be promotekit_referral - NEVER use userId here`

For detailed information on the PromoteKit integration, including API endpoints and additional implementation details, refer to the [PromoteKit Documentation](_dev_docs/promotekit%20(1).md).

## Troubleshooting

### Common Issues and Solutions

| Issue | Possible Cause | Solution |
|-------|---------------|----------|
| Checkout button not working | Missing or incorrect price ID | Check `pricingData.json` and verify price IDs in Stripe Dashboard |
| Redirect not happening | JavaScript error in checkout handler | Check browser console for errors, verify all required props are provided |
| Price displayed doesn't match Stripe | Mismatch between `pricingData.json` and Stripe | Update pricing data to match what's in Stripe |
| Trial period not applying | Incorrect `trialDays` parameter | Verify `trialDays` is a positive integer and being passed correctly |
| Referral not tracking | PromoteKit not initialized | Ensure PromoteKit script is loaded before checkout |

### Debug Mode

Enable debug mode on the checkout button to diagnose issues:

```jsx
<StripeCheckoutButton
  userId={user?.id}
  priceId="price_1234567890"
  planName="PRO"
  debug={true}
>
  Debug Checkout
</StripeCheckoutButton>
```

This will display:
- The payload sent to the Stripe API
- Whether a referral ID was detected
- The response from the server action

## Best Practices

1. **Always use environment-specific price IDs**:
   ```javascript
   const env = process.env.NODE_ENV === 'production' ? 'prod' : 'dev';
   const priceId = plan.priceIds[env][isYearly ? 'yearly' : 'monthly'];
   ```

2. **Include comprehensive metadata**:
   Always pass `planName`, `billingPeriodValue`, and `billingPeriodUnit` to ensure proper tracking.

3. **Handle loading states**:
   The `StripeCheckoutButton` has built-in loading state handling that disables the button during checkout.

4. **Keep pricing data in sync**:
   Whenever you update prices in Stripe, update `pricingData.json` to match.

5. **Test in Stripe test mode**:
   Always test new pricing plans in test mode before deploying to production.

6. **Security considerations**:
   - Never store sensitive payment data on the client
   - Always use server actions for Stripe API calls
   - Sanitize all inputs before sending to Stripe
