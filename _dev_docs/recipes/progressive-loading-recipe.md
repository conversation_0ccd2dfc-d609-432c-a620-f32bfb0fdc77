# Progressive Loading Recipe for Data-Fetching Cards

A complete guide to creating smooth, professional loading experiences for card components that fetch their own data.

## 🎯 The Problem We're Solving

**Bad UX:**
- Multiple skeleton states competing with each other
- Flicker between skeleton → data → skeleton → data
- Layout shifts during loading
- Generic skeletons that don't match final content
- Sequential API calls blocking each other

**Good UX:**
- Single, coordinated loading strategy
- Progressive content reveal as data becomes available
- Zero layout shifts
- Skeleton that perfectly matches final layout
- Parallel API calls for maximum speed

## 🏗 Architecture Overview

```
┌─────────────────────────────────────────────────────────────┐
│ Card Component                                              │
├─────────────────────────────────────────────────────────────┤
│ 1. Unified Loading State Management                         │
│    ├── initializing: true/false                             │
│    ├── dataTypeALoaded: true/false                          │
│    ├── dataTypeBLoaded: true/false                          │
│    └── hasError/errorMessage                                │
│                                                             │
│ 2. Parallel Data Fetching                                   │
│    ├── Start ALL API calls immediately                      │
│    ├── Handle validation in parallel (not blocking)         │
│    └── Progressive state updates as data arrives            │
│                                                             │
│ 3. Layout-Matched Skeleton                                  │
│    ├── Real labels visible during loading                   │
│    ├── Placeholders in exact positions                      │
│    └── Zero layout shift when data fills in                 │
└─────────────────────────────────────────────────────────────┘
```

## 📋 Step-by-Step Implementation

### Step 1: Design Your Loading State Machine

Create a **unified loading state** instead of multiple boolean flags:

```javascript
// ❌ BAD: Multiple competing states
const [loading, setLoading] = useState(true);
const [dataALoading, setDataALoading] = useState(true);
const [dataBLoading, setDataBLoading] = useState(true);
const [permissionChecked, setPermissionChecked] = useState(false);

// ✅ GOOD: Single coordinated state
const [loadingState, setLoadingState] = useState({
  initializing: true,           // Show skeleton until ready
  basicDataLoaded: false,       // Core info (name, description)
  priceDataLoaded: false,       // Price/market data
  analyticsDataLoaded: false,   // Analytics/charts
  hasError: false,
  errorMessage: null
});
```

### Step 2: Implement Parallel Data Fetching

Start **all API calls immediately**, handle validation in parallel:

```javascript
const fetchData = async () => {
  try {
    // 🚀 Start ALL operations in parallel immediately
    const permissionPromise = checkPermissions();
    const validationPromise = validateInput(inputData);
    const basicDataPromise = fetchBasicData(inputData);
    const priceDataPromise = fetchPriceData(inputData);
    const analyticsPromise = fetchAnalytics(inputData);

    // Wait for validation (but data fetching already started)
    const [hasPermission, isValid] = await Promise.all([
      permissionPromise,
      validationPromise
    ]);

    // Update initialization state
    setLoadingState(prev => ({ ...prev, initializing: false }));

    // Handle validation results
    if (!hasPermission || !isValid) {
      setLoadingState({
        initializing: false,
        basicDataLoaded: false,
        priceDataLoaded: false,
        analyticsDataLoaded: false,
        hasError: true,
        errorMessage: "Validation failed"
      });
      return;
    }

    // Handle data as it arrives (already in flight)
    basicDataPromise
      .then(data => {
        if (data) {
          setBasicData(data);
          setLoadingState(prev => ({ ...prev, basicDataLoaded: true }));
        }
      })
      .catch(err => handleError(err));

    priceDataPromise
      .then(data => {
        if (data) {
          setPriceData(data);
          setLoadingState(prev => ({ ...prev, priceDataLoaded: true }));
        }
      })
      .catch(err => handleError(err));

    analyticsPromise
      .then(data => {
        if (data) {
          setAnalyticsData(data);
          setLoadingState(prev => ({ ...prev, analyticsDataLoaded: true }));
        }
      })
      .catch(err => handleError(err));

  } catch (err) {
    setLoadingState({
      initializing: false,
      basicDataLoaded: false,
      priceDataLoaded: false,
      analyticsDataLoaded: false,
      hasError: true,
      errorMessage: "Failed to initialize"
    });
  }
};
```

### Step 3: Create Progressive Loading Placeholders

Use **inline placeholders** that match your exact layout:

```javascript
// Reusable placeholder component
const LoadingPlaceholder = ({ width, height = "h-4", inline = false }) => (
  <span className={`${height} ${width} bg-gray-200 animate-pulse rounded ${inline ? 'inline-block' : 'block'}`}></span>
);

// Progressive content rendering
<div className="text-sm text-default-500">
  <span className="text-xs">Label: </span>
  <span className="text-default-600">
    {loadingState.dataTypeLoaded && data?.field ? (
      formatData(data.field)  // Real data
    ) : (
      <LoadingPlaceholder width="w-16" height="h-4" inline={true} />  // Placeholder
    )}
  </span>
</div>
```

### Step 4: Build Layout-Matched Skeleton

Create a skeleton that **exactly matches** your progressive loading layout:

```javascript
// YourCardSkeleton.js
export default function YourCardSkeleton({ className = "" }) {
  const SkeletonPlaceholder = ({ width, height = "h-4" }) => (
    <span className={`${height} ${width} bg-gray-200 animate-pulse rounded inline-block`}></span>
  );

  return (
    <Card className={className}>
      <CardBody>
        {/* Match EXACT layout structure */}
        <div className="flex gap-4">
          {/* Image placeholder */}
          <div className="w-20 h-20 bg-gray-200 animate-pulse rounded"></div>
          
          <div className="flex-1">
            {/* Title with real styling */}
            <div className="text-xl mb-2">
              <SkeletonPlaceholder width="w-48" height="h-6" />
            </div>
            
            {/* Details with REAL LABELS */}
            <div className="flex gap-4">
              <div className="text-sm">
                <span className="text-xs">Label 1: </span>
                <SkeletonPlaceholder width="w-12" height="h-4" />
              </div>
              <div className="text-sm">
                <span className="text-xs">Label 2: </span>
                <SkeletonPlaceholder width="w-16" height="h-4" />
              </div>
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
```

### Step 5: Remove Conflicting Page-Level Suspense

If your card handles its own loading, remove redundant page-level Suspense:

```javascript
// ❌ BAD: Double loading management
<Suspense fallback={<YourCardSkeleton />}>
  <YourCard data={data} />  {/* Also has internal loading */}
</Suspense>

// ✅ GOOD: Single loading management
<YourCard data={data} />  {/* Handles its own loading */}
```

## 🎨 Real-World Example: TokenMetadataCard

### Loading State Design
```javascript
const [loadingState, setLoadingState] = useState({
  initializing: true,        // Show skeleton
  basicDataLoaded: false,    // Name, symbol, description
  priceDataLoaded: false,    // Price, market cap
  ageDataLoaded: false,      // Token age (non-critical)
  hasError: false,
  errorMessage: null
});
```

### Parallel API Strategy
```javascript
// All started simultaneously
const permissionPromise = checkMinimumLevel('NOLOGIN');
const addressCheckPromise = checkAddressType(tokenAddress);
const tokenDetailsPromise = getTokenDetails(tokenAddress);  // ← No waiting
const tokenAgePromise = getTokenAge(tokenAddress);          // ← No waiting
```

### Progressive Rendering
```javascript
{/* Age field - loads independently */}
<div className="text-sm">
  <span className="text-xs">Age: </span>
  <span>
    {loadingState.ageDataLoaded ? (
      <TimeAgoDisplay timestamp={tokenAge?.mintDate} />  // Real data
    ) : (
      <LoadingPlaceholder width="w-16" inline={true} />  // Placeholder
    )}
  </span>
</div>
```

## ⚡ Performance Benefits

| Approach | Load Time | User Experience |
|----------|-----------|-----------------|
| **Sequential** | `validation + max(api_calls)` | Slow, jarring |
| **Progressive** | `max(validation, api_calls)` | Fast, smooth |

**Example:** 200ms validation + 300ms API = **500ms vs 300ms** (40% faster!)

## 🚨 Common Pitfalls to Avoid

### 1. Multiple Loading States
```javascript
// ❌ Creates flicker
const [loading1, setLoading1] = useState(true);
const [loading2, setLoading2] = useState(true);

// ✅ Unified state
const [loadingState, setLoadingState] = useState({...});
```

### 2. Sequential API Calls
```javascript
// ❌ Slow - each waits for previous
const validation = await validate();
if (validation) {
  const data = await fetchData();
}

// ✅ Fast - parallel execution
const [validation, data] = await Promise.all([validate(), fetchData()]);
```

### 3. Generic Skeletons
```javascript
// ❌ Layout shift when data loads
<div className="h-4 w-32 bg-gray-200"></div>

// ✅ Matches exact layout
<span className="text-xs">Label: </span>
<LoadingPlaceholder width="w-16" inline={true} />
```

### 4. Conflicting Loading Management
```javascript
// ❌ Double skeletons
<Suspense fallback={<Skeleton />}>
  <Component />  {/* Also shows loading states */}
</Suspense>

// ✅ Single responsibility
<Component />  {/* Handles own loading */}
```

## 🎯 Best Practices Checklist

- [ ] **Single loading state object** instead of multiple booleans
- [ ] **Start all API calls in parallel** immediately
- [ ] **Progressive field rendering** as data arrives
- [ ] **Layout-matched skeleton** with real labels
- [ ] **Inline placeholders** to prevent layout shift
- [ ] **Remove conflicting Suspense** boundaries
- [ ] **Handle errors gracefully** with proper states
- [ ] **Test with slow networks** to verify smooth experience
- [ ] **Use semantic loading indicators** (not just spinners)
- [ ] **Cache API responses** appropriately

## 🔄 Adaptation for Different Card Types

### Data Dashboard Card
```javascript
const [loadingState, setLoadingState] = useState({
  initializing: true,
  metricsLoaded: false,    // KPIs, numbers
  chartsLoaded: false,     // Chart data
  filtersLoaded: false,    // Filter options
  hasError: false
});
```

### User Profile Card
```javascript
const [loadingState, setLoadingState] = useState({
  initializing: true,
  profileLoaded: false,    // Basic profile info
  statsLoaded: false,      // User statistics
  activityLoaded: false,   // Recent activity
  hasError: false
});
```

### Product Card
```javascript
const [loadingState, setLoadingState] = useState({
  initializing: true,
  productLoaded: false,    // Name, description, image
  pricingLoaded: false,    // Price, discounts
  reviewsLoaded: false,    // Reviews, ratings
  hasError: false
});
```

## 🚀 Result: Professional Loading Experience

✅ **Fast perceived performance** - Content appears progressively  
✅ **Zero layout shifts** - Everything stays in position  
✅ **Smooth transitions** - Skeleton matches final layout  
✅ **Parallel efficiency** - All APIs called simultaneously  
✅ **Error resilience** - Graceful failure handling  
✅ **Consistent UX** - Professional, intentional feel  

This pattern transforms loading from a **necessary evil** into a **polished user experience** that builds confidence and trust in your application. 