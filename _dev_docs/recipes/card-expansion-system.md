# Card Expansion System with Framer Motion

This guide explains how to implement card expansion in our project using Framer Motion, providing a smooth, consistent animation system for expandable cards.

## Table of Contents

1. [Overview](#overview)
2. [Installation](#installation)
3. [The useCardExpansion Hook](#the-usecardexpansion-hook)
4. [Basic Implementation](#basic-implementation)
5. [Advanced Usage](#advanced-usage)
6. [Examples](#examples)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Overview

Our card expansion system enables cards to expand and collapse with smooth animations, using Framer Motion to handle the animation logic. The system:

- Maintains expansion state with a simple local state approach
- Provides consistent animations via the renderExpandedContent function
- Is easily customizable for different card types
- Handles edge cases like varying content height

## Installation

Ensure Framer Motion is installed in your project:

```bash
pnpm add framer-motion
```

## The useCardExpansion Hook

The core of our system is the `useCardExpansion` hook that handles both the expansion state and the animations:

```jsx
// hooks/useCardExpansion.js
import { useState, useCallback } from "react";
import { AnimatePresence, motion } from "framer-motion";

/**
 * Hook for managing card expansion with Framer Motion animations
 * 
 * @param {Object} options - Configuration options
 * @param {boolean} options.defaultExpanded - Whether the card is expanded by default (default: false)
 * @param {number} options.duration - Animation duration in seconds (default: 0.3)
 * @returns {Object} Expansion state and helper functions/components
 */
export function useCardExpansion(options = {}) {
  const { 
    defaultExpanded = false,
    duration = 0.3,
  } = options;

  // Use local state for expansion tracking
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  
  // Toggle expansion state
  const toggleExpand = useCallback(() => {
    setIsExpanded(prev => !prev);
  }, []);

  // Expand and collapse functions
  const expand = useCallback(() => setIsExpanded(true), []);
  const collapse = useCallback(() => setIsExpanded(false), []);
  
  // Render function to use with AnimatePresence
  const renderExpandedContent = useCallback((content, props = {}) => {
    const { 
      className = "", 
      initial = { height: 0, opacity: 0 },
      animate = { height: "auto", opacity: 1 },
      exit = { height: 0, opacity: 0 },
      ...rest 
    } = props;

    return (
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={initial}
            animate={animate}
            exit={exit}
            transition={{ 
              duration,
              ease: [0.04, 0.62, 0.23, 0.98]
            }}
            className={className}
            {...rest}
          >
            {content}
          </motion.div>
        )}
      </AnimatePresence>
    );
  }, [isExpanded, duration]);
  
  return { 
    isExpanded,
    toggleExpand,
    expand,
    collapse,
    renderExpandedContent
  };
}

export default useCardExpansion;
```

## Basic Implementation

Here's how to implement the hook in a card component:

```jsx
import React from "react";
import { Card, CardBody } from "@heroui/card";
import { Button } from "@heroui/button";
import ChevronDownIcon from "@/components/icons/ChevronDownIcon";
import ChevronUpIcon from "@/components/icons/ChevronUpIcon";
import useCardExpansion from "@/hooks/useCardExpansion";

function ExpandableCard({ item }) {
  // Use the expansion hook
  const { isExpanded, toggleExpand, renderExpandedContent } = useCardExpansion();
  
  return (
    <Card className="border border-neutral-200">
      <CardBody className="p-4">
        {/* Card header with toggle button */}
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">{item.title}</h3>
          <Button
            variant="light"
            size="sm"
            isIconOnly
            aria-label={isExpanded ? "Collapse" : "Expand"}
            onPress={toggleExpand}
          >
            {isExpanded ? <ChevronUpIcon size={16} /> : <ChevronDownIcon size={16} />}
          </Button>
        </div>
        
        {/* Card summary (always visible) */}
        <div className="mt-2">
          <p>{item.summary}</p>
        </div>
        
        {/* Expandable content with animations */}
        {renderExpandedContent(
          <div className="px-3">
            <h4 className="text-sm font-medium mb-2">Details</h4>
            <p>{item.details}</p>
          </div>,
          { className: "bg-default-50 rounded-lg" }
        )}
      </CardBody>
    </Card>
  );
}

export default ExpandableCard;
```

## Advanced Usage

### Customizing Animations

You can customize the animation behavior by passing options to the hook and the renderExpandedContent function:

```jsx
// Custom animation duration when creating the hook
const { isExpanded, toggleExpand, renderExpandedContent } = useCardExpansion({
  defaultExpanded: false,
  duration: 0.5, // slower animation
});

// Custom animation properties when rendering content
{renderExpandedContent(
  <div>Your content here</div>,
  {
    className: "bg-default-50 rounded-lg",
    initial: { height: 0, opacity: 0, scale: 0.95 },
    animate: { height: "auto", opacity: 1, scale: 1 },
    exit: { height: 0, opacity: 0, scale: 0.9 }
  }
)}
```

### Staggered Children Animations

For more complex animations, you can combine with Framer Motion's staggered children:

```jsx
{renderExpandedContent(
  <div className="px-3">
    {detailItems.map((detail, index) => (
      <motion.div
        key={index}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        {detail.content}
      </motion.div>
    ))}
  </div>,
  { className: "bg-default-50 rounded-lg" }
)}
```

## Examples

### Example 1: DCA Order Card

Here's a real implementation from our DcaOrderCard component:

```jsx
import { useMemo } from "react";
import { Card, CardBody } from "@heroui/card";
import { Divider } from "@heroui/divider";
import { Chip } from "@heroui/chip";
import { Progress } from "@heroui/progress";
import { formatCurrency, formatNumber } from "@/helpers/TextFormat";
import { SolanaToken } from "@/components/UI/SolanaAddress";
import { Button } from "@heroui/button";
import ChevronDownIcon from "@/components/icons/ChevronDownIcon";
import ChevronUpIcon from "@/components/icons/ChevronUpIcon";
import dynamic from "next/dynamic";
import useCardExpansion from "@/hooks/useCardExpansion";

const TimeAgoDisplay = dynamic(() => import("@/components/dashboard/TimeAgoDisplay"));

// Status Badge Component
const StatusBadge = ({ isActive, wasCanceled }) => {
  if (isActive) {
    return (
      <Chip size="sm" color="primary" variant="flat">Active</Chip>
    );
  }
  
  return wasCanceled ? (
    <Chip size="sm" color="danger" variant="flat">Canceled</Chip>
  ) : (
    <Chip size="sm" color="success" variant="flat">Completed</Chip>
  );
};

// Card section component for consistent styling
const CardSection = ({ title, children }) => {
  return (
    <Card className="w-full bg-default-50 border-none" radius="sm" shadow="none">
      <CardBody className="p-3">
        <div className="text-xs text-neutral-500 mb-1">{title}</div>
        {children}
      </CardBody>
    </Card>
  );
};

export default function DcaOrderCard({ order }) {
  const percentComplete = parseFloat(order.percent_fulfilled).toFixed(0);
  const isActive = !order.is_closed;
  const wasCanceled = order.was_canceled;
  
  // Use the card expansion hook
  const { isExpanded, toggleExpand, renderExpandedContent } = useCardExpansion();
  
  // Format date for display
  const formattedDate = new Date(isActive ? order.opened_time : (order.closed_time || order.opened_time)).toLocaleString();
  
  // Calculate estimated completion time for active orders
  const estimatedCompletionTime = useMemo(() => {
    if (!isActive) return null;
    
    const cycleSeconds = parseInt(order.cycle_frequency.split(' ')[0]);
    const remainingFills = Math.ceil(order.total_expected_fills) - order.num_fills;
    const secondsToComplete = remainingFills * cycleSeconds;
    
    // Create a date object for when it completes
    const completionDate = new Date();
    completionDate.setSeconds(completionDate.getSeconds() + secondsToComplete);
    
    return completionDate;
  }, [isActive, order.cycle_frequency, order.total_expected_fills, order.num_fills]);

  return (
    <Card 
      radius="sm" 
      shadow="none"
      classNames={{
        base: "border border-neutral-200"
      }}
    >
      <CardBody className="py-3 px-4">
        <div className="flex flex-col gap-3">
          {/* Header with status (left) and timeago (right) */}
          <div className="flex justify-between items-center">
            <StatusBadge isActive={isActive} wasCanceled={wasCanceled} />
            <div className="flex items-center gap-2">
              <div className="text-xs text-default-500">
                <TimeAgoDisplay timestamp={new Date(isActive ? order.opened_time : (order.closed_time || order.opened_time))} />
              </div>
              <Button
                variant="light"
                size="sm"
                isIconOnly
                onPress={toggleExpand}
                className="text-default-400 hover:text-default-600"
                aria-label={isExpanded ? "Collapse details" : "Expand details"}
              >
                {isExpanded ? <ChevronUpIcon size={16} /> : <ChevronDownIcon size={16} />}
              </Button>
            </div>
          </div>
          
          {/* Cards Grid Layout for Tokens - Always visible */}
          <div className="grid grid-cols-2 gap-2">
            {/* Selling Card */}
            <CardSection title="Selling">
              <SolanaToken
                address={order.input_mint}
                size="xs"
                showButtons={true}
                showControls={true}
              />
            </CardSection>
            
            {/* Buying Card */}
            <CardSection title="Buying">
              <SolanaToken
                address={order.output_mint}
                size="xs"
                showButtons={true}
                showControls={true}
              />
            </CardSection>
          </div>
          
          {/* Always visible content */}
          <div className="grid grid-cols-3 gap-2">
            {/* Deposited Card */}
            <CardSection title="Deposited">
              <div className="flex flex-col">
                <div className="font-medium text-sm">
                  {formatCurrency(order.in_deposited_usd)}
                </div>
                <div className="text-xs text-neutral-500">
                  {formatNumber(order.in_deposited, true, order.in_deposited < 10 ? 2 : 0)} tokens
                </div>
              </div>
            </CardSection>
            
            {/* Entry Prices Card */}
            <CardSection title="Entry Price">
              <div className="flex flex-col">
                <div className="flex justify-between items-center text-xs">
                  <span className="text-neutral-500">Selling:</span>
                  <span className="font-medium">${formatNumber(order.input_usd_price_at_open, true, 4)}</span>
                </div>
                <div className="flex justify-between items-center text-xs mt-1">
                  <span className="text-neutral-500">Buying:</span>
                  <span className="font-medium">${formatNumber(order.output_usd_price_at_open, true, 4)}</span>
                </div>
              </div>
            </CardSection>
            
            {/* Average Prices Card */}
            <CardSection title="Avg Price">
              <div className="flex flex-col">
                <div className="flex justify-between items-center text-xs">
                  <span className="text-neutral-500">Selling:</span>
                  <span className="font-medium">${formatNumber(order.avg_input_usd_price, true, 4)}</span>
                </div>
                <div className="flex justify-between items-center text-xs mt-1">
                  <span className="text-neutral-500">Buying:</span>
                  <span className="font-medium">${formatNumber(order.avg_output_usd_price, true, 4)}</span>
                </div>
              </div>
            </CardSection>
          </div>
          
          {/* Progress Card - Always visible */}
          <CardSection title="Progress">
            <div className="space-y-1">
              <Progress 
                value={percentComplete} 
                color={isActive ? "primary" : wasCanceled ? "danger" : "success"} 
                size="sm"
                aria-label="DCA Progress"
                className="mb-1"
              />
              <div className="flex justify-between items-center text-xs text-neutral-500 mt-1">
                <span>Status: {percentComplete}%</span>
                {estimatedCompletionTime && isActive && (
                  <span>Completes in: <TimeAgoDisplay timestamp={estimatedCompletionTime} options={{ suffix: false }} /></span>
                )}
              </div>
            </div>
          </CardSection>
          
          {/* Use the renderExpandedContent function for expandable content */}
          {renderExpandedContent(
            <div className="space-y-3 px-3 py-3">
              <Divider className="my-1" />
              
              {/* Enhanced Details Section */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                {/* Transaction Fill Details */}
                <CardSection title="Transaction Details">
                  <div className="flex flex-col gap-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-neutral-500">Fill Count:</span>
                      <span className="font-medium">{order.num_fills} of {order.total_expected_fills}</span>
                    </div>
                    {/* More transaction details... */}
                  </div>
                </CardSection>
                
                {/* Price Details */}
                <CardSection title="Price Details">
                  <div className="flex flex-col gap-1 text-xs">
                    <div className="flex justify-between">
                      <span className="text-neutral-500">Entry Price:</span>
                      <span className="font-medium">${formatNumber(order.entry_price, true, 8)}</span>
                    </div>
                    {/* More price details... */}
                  </div>
                </CardSection>
              </div>
              
              {/* More expandable sections... */}
            </div>,
            { className: "bg-default-50/50 rounded-lg overflow-hidden" }
          )}
          
          {/* Footer - Full DateTime - Always visible */}
          <div className="flex justify-between items-center pt-1">
            <div className="text-xs text-default-500">
              {formattedDate}
            </div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
```

### Example 2: Simple Expandable FAQ

```jsx
import React from "react";
import { Card, CardBody } from "@heroui/card";
import { Button } from "@heroui/button";
import ChevronDownIcon from "@/components/icons/ChevronDownIcon";
import ChevronUpIcon from "@/components/icons/ChevronUpIcon";
import useCardExpansion from "@/hooks/useCardExpansion";

function FaqItem({ question, answer }) {
  const { isExpanded, toggleExpand, renderExpandedContent } = useCardExpansion();
  
  return (
    <Card className="mb-4 border border-neutral-200">
      <CardBody className="p-4">
        {/* Question with toggle button */}
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">{question}</h3>
          <Button
            variant="light"
            size="sm"
            isIconOnly
            onPress={toggleExpand}
            aria-label={isExpanded ? "Hide answer" : "Show answer"}
          >
            {isExpanded ? <ChevronUpIcon size={16} /> : <ChevronDownIcon size={16} />}
          </Button>
        </div>
        
        {/* Animated answer section */}
        {renderExpandedContent(
          <div className="pt-2">
            <p className="text-default-600">{answer}</p>
          </div>,
          {
            initial: { height: 0, opacity: 0 },
            animate: { height: "auto", opacity: 1, marginTop: "0.75rem" },
            exit: { height: 0, opacity: 0, marginTop: 0 }
          }
        )}
      </CardBody>
    </Card>
  );
}

export default function Faq() {
  const faqItems = [
    {
      question: "How do I create a wallet?",
      answer: "You can create a wallet by downloading a compatible wallet app like Phantom or Solflare and following their setup instructions."
    },
    {
      question: "What is DCA?",
      answer: "Dollar-Cost Averaging (DCA) is an investment strategy where you divide your total investment across periodic purchases to reduce the impact of volatility."
    },
    // More FAQ items...
  ];
  
  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-semibold mb-6">Frequently Asked Questions</h2>
      {faqItems.map((item, index) => (
        <FaqItem key={index} question={item.question} answer={item.answer} />
      ))}
    </div>
  );
}
```

## Best Practices

1. **Performance**
   - Use the `defaultExpanded` prop only when necessary to have content expanded by default
   - Avoid heavy computations during animations
   - Memoize callbacks and derived values with `useCallback` and `useMemo`

2. **Accessibility**
   - Include proper ARIA attributes on toggle controls (e.g., `aria-label`, `aria-expanded`)
   - Test with screen readers
   - Make sure the expansion is keyboard navigable
   - Framer Motion respects the user's reduced motion preferences automatically

3. **Animation Timing**
   - Keep animations short (0.2-0.4 seconds) for better user experience
   - Use appropriate easing curves for natural motion (the default in our hook is already optimized)
   - Make animations consistent across the application

4. **Content Handling**
   - Let Framer Motion handle height transitions with `height: "auto"` (it properly calculates content height)
   - Structure content to avoid layout shifts during animations
   - For complex content, consider staggered animations within the expanded content

5. **Mobile Optimizations**
   - Test the expansion on all device sizes
   - Ensure touch targets are sufficiently large (at least 44x44px)
   - Consider simpler animations on mobile for better performance

## Troubleshooting

### Common Issues

1. **Animation Flickering**
   - Make sure you're not using multiple instances of the same animation or AnimatePresence
   - Add the `"use client"` directive to components using the animation
   - Check for conflicting styles or transitions

2. **Animation Not Playing**
   - Verify that Framer Motion is properly installed
   - Check that the animation properties in renderExpandedContent are properly formatted
   - Ensure there's no conflicting CSS that might be overriding the animation

3. **Layout Shifts During Animation**
   - Add `overflow: "hidden"` to your animation properties in renderExpandedContent
   - Use the className property to add proper padding/margin around expanded content
   - Consider using Framer Motion's `layout` prop for more complex animations

4. **Performance Issues**
   - Reduce the complexity of your animations
   - Use the React DevTools profiler to identify performance bottlenecks
   - Simplify the content of expanded sections

---

By using our useCardExpansion hook with Framer Motion, you can create consistent, accessible, and performant card expansions throughout the application. The renderExpandedContent function makes it easy to implement animations without duplicating code or managing complex state. 