---
description: >-
  A comprehensive guide for implementing access control with blurred content and
  upgrade prompts across the StalkChain application.
---

# How to: Locked Content

**Target audience:** Frontend developers implementing permission-protected features who need to integrate access control with data fetching and provide upgrade conversion opportunities.

<figure><img src="../.gitbook/assets/image (5).png" alt=""><figcaption></figcaption></figure>

## Permission System Implementation Guide

A practical guide for protecting components with permission levels while showing users what they're missing through blurred content previews.

### 🎯 Overview

**The Problem:** Features require different subscription levels, but blocking access entirely reduces conversion.

**The Solution:** Show blurred previews with upgrade prompts that demonstrate value while guiding users to appropriate subscription tiers.

### 🏗 Permission Levels

```json
// static/levels.json
{
  "NOLOGIN": 0,    // Not logged in users
  "FREE": 10,      // Logged in users (default)
  "TRIAL": 50,     // Trial users  
  "PRO": 60,       // Pro subscription
  "KOL": 70,       // Key Opinion Leader
  "LEGEND": 80,    // Legend tier
  "TEAM": 90,      // Team members
  "ADMIN": 99      // Administrators
}
```

### 📊 System Flow

#### Permission System Flow

```mermaid
flowchart TD
    A("Wrap Component with PermissionPrompt") --> B{"User Permission Level vs Required Level"}
    
    B -->|"User Not Logged In"| C("Show LoginPrompt")
    B -->|"User Level >= Required"| D("Show Your Content")
    B -->|"User Level < Required"| E("Show UpgradePrompt")
    
    C --> F("Blur Your Content + Login Overlay")
    E --> G("Blur Your Content + Upgrade Overlay")
    
    H("User Logs In") --> I("Re-check Permission")
    J("User Upgrades") --> I
    I --> B
    
    D --> K("Call onPermissionGranted")
    K --> L("Fetch Data / Show Real Content")
    
    classDef userAction fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef systemResponse fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef decisionPoint fill:#f8f9fa,stroke:#455a64,color:#455a64
    classDef promptDisplay fill:#fff3e0,stroke:#e65100,color:#e65100
    
    class A,H,J userAction
    class D,K,L systemResponse
    class B,I decisionPoint
    class C,E,F,G promptDisplay
```

### 📁 Component Structure

```
components/auth/
├── PermissionPrompt.js      # Main wrapper component
├── LoginPrompt.js           # Login CTA for non-logged users
├── UpgradePrompt.js         # Upgrade CTA for insufficient level
└── permission-system-recipe.md  # This documentation
```

### 🚀 Quick Start

#### Basic Protection (Login Required)

```jsx
// components/YourProtectedComponent.js
import PermissionPrompt from "@/components/auth/PermissionPrompt";

export default function YourProtectedComponent() {
  return (
    <PermissionPrompt requiredLevel="FREE">
      <div>
        <h2>Your Protected Content</h2>
        <p>This requires login</p>
      </div>
    </PermissionPrompt>
  );
}
```

#### With Blurred Preview (Upgrade Required)

<figure><img src="../.gitbook/assets/image (6).png" alt=""><figcaption></figcaption></figure>

```jsx
// components/AnalyticsWidget.js
import PermissionPrompt from "@/components/auth/PermissionPrompt";
import MetricCard from "@/components/cards/MetricCard";

export default function AnalyticsWidget() {
  // Real content when user has permission
  const actualContent = (
    <div className="grid grid-cols-3 gap-4">
      <MetricCard title="Revenue" value={realRevenue} />
      <MetricCard title="Users" value={realUsers} />
      <MetricCard title="Growth" value={realGrowth} />
    </div>
  );

  // Blurred preview with dummy data
  const blurredContent = (
    <div className="grid grid-cols-3 gap-4">
      <MetricCard title="Revenue" value="$12,450" />
      <MetricCard title="Users" value="1,234" />
      <MetricCard title="Growth" value="+23%" />
    </div>
  );

  return (
    <PermissionPrompt
      requiredLevel="PRO"
      blurredContent={blurredContent}
    >
      {actualContent}
    </PermissionPrompt>
  );
}
```

### 🔄 Data Fetching Integration

For components that need to fetch data when permission is granted:

#### **Three Patterns Available:**

1. **Full Data Fetching Pattern** - Parent component fetches and manages data
2. **Simplified Permission Pattern** - Parent grants permission, child component handles data fetching
3. **Self-Fetching Components Pattern** - Components use React Query and handle permission states internally

#### Data Fetching Integration Flow

```mermaid
flowchart TD
    A("Component mounts") --> B("dataState.initialized = false")
    B --> C("Children render showing skeletons")
    C --> D("PermissionPrompt checks permissions")
    
    D --> E{"Permission result?"}
    E -->|"No permission"| F("Hide children, show blurred + prompt")
    E -->|"Has permission"| G("Keep showing children")
    
    G --> H("onPermissionGranted() called")
    H --> I("fetchData() executes")
    I --> J("dataState.loading = true")
    J --> K("API calls")
    K --> L("dataState = { data, initialized: true }")
    L --> M("Children update to show real data")
    
    N("Auth changes") --> D
    O("Input changes") --> P("Reset dataState")
    P --> B
    
    classDef componentState fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef permissionFlow fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef dataFlow fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef decisionPoint fill:#f8f9fa,stroke:#455a64,color:#455a64
    
    class A,B,C,G,M,P componentState
    class D,F,H,N permissionFlow
    class I,J,K,L dataFlow
    class E,O decisionPoint
```

#### Implementation Pattern

```jsx
// content/solana/components/TokenHoldersSection.js
import { useState, useEffect } from "react";
import { getTokenHolders } from "@/actions/tokens/getTokenHolders";
import PermissionPrompt from "@/components/auth/PermissionPrompt";
import HoldersOverviewCard from "@/components/cards/HoldersOverviewCard";
import TopHoldersTable from "@/components/tables/TopHoldersTable";

export default function TokenHoldersSection({ tokenAddress }) {
  const [dataState, setDataState] = useState({
    loading: false,
    data: null,
    error: null,
    initialized: false  // Key flag for data fetching state
  });

  // Data fetching function - only called when user has permission
  const fetchHolderData = async () => {
    if (!tokenAddress) return;
    
    setDataState(prev => ({ ...prev, loading: true }));

    try {
      const result = await getTokenHolders(tokenAddress);
      
      setDataState({
        loading: false,
        data: result,
        error: result.success ? null : result.error,
        initialized: true
      });
    } catch (err) {
      setDataState({
        loading: false,
        data: null,
        error: "Failed to load holder data",
        initialized: true
      });
    }
  };

  // Reset data state when token address changes
  useEffect(() => {
    setDataState({
      loading: false,
      data: null,
      error: null,
      initialized: false
    });
  }, [tokenAddress]);

  // Callback for when user gains permission
  const handlePermissionGranted = () => {
    fetchHolderData();
  };

  // Blurred content with realistic dummy data
  const blurredContent = (
    <div className="space-y-6">
      <HoldersOverviewCard 
        holderData={{
          holdersCount: 1234,
          classifications: {
            whales: { supplyPercentage: 45.2 },
            shrimps: { supplyPercentage: 12.8 }
          }
        }}
      />
      <TopHoldersTable 
        holdersData={[
          { owner: "A1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0", amount: 500000 },
          { owner: "B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R8S9T0U1", amount: 350000 }
        ]}
        tokenPrice={0.00123}
        isLoading={false}
      />
    </div>
  );

  // Actual content with progressive loading
  const actualContent = (
    <div className="space-y-6">
      {dataState.data && !dataState.loading && dataState.initialized ? (
        <HoldersOverviewCard holderData={dataState.data} />
      ) : (
        <div className="animate-pulse bg-gray-200 h-32 rounded" />
      )}
      
      <TopHoldersTable 
        holdersData={dataState.initialized ? (dataState.data?.holders || []) : []}
        tokenPrice={dataState.initialized ? dataState.data?.tokenPrice : null}
        isLoading={dataState.loading || !dataState.initialized}
        error={dataState.initialized ? dataState.error : null}
      />
    </div>
  );

  return (
    <PermissionPrompt
      requiredLevel="FREE"
      blurredContent={blurredContent}
      onPermissionGranted={handlePermissionGranted}
    >
      {actualContent}
    </PermissionPrompt>
  );
}
```

#### **Alternative: Simplified Permission Pattern**

For components where the child handles its own data fetching (like KOL profile tabs):

```jsx
// content/kol-profile/components/TradesTabContent.js
const TradesTabContent = ({ kolData }) => {
  const [dataState, setDataState] = useState({
    loading: false,
    data: null,
    error: null,
    initialized: false
  });

  // Simplified permission granting - child component handles actual data fetching
  const fetchTradesData = async () => {
    if (!kolData?.wallet) return;
    
    setDataState(prev => ({ ...prev, loading: true }));

    try {
      // Just mark permission as granted - child component will handle data fetching
      setDataState({
        loading: false,
        data: true, // Indicates permission granted
        error: null,
        initialized: true
      });
    } catch (err) {
      setDataState({
        loading: false,
        data: null,
        error: "Failed to load trades data",
        initialized: true
      });
    }
  };

  // Reset when wallet changes
  useEffect(() => {
    setDataState({
      loading: false,
      data: null,
      error: null,
      initialized: false
    });
  }, [kolData?.wallet]);

  const handlePermissionGranted = () => {
    fetchTradesData();
  };

  // Use loading skeleton for blurred content when dummy data is impractical
  const blurredContent = <TabLoadingFallback />;

  const actualContent = (
    dataState.initialized ? (
      <KolTradesTableWithAPI
        kolData={kolData}
        showInfluencer={false}
        isLoading={false}
      />
    ) : (
      <TabLoadingFallback />
    )
  );

  return (
    <PermissionPrompt
      requiredLevel="PRO"
      blurredContent={blurredContent}
      promptTitle="PRO Upgrade Required"
      promptMessage="Upgrade to PRO to access detailed trading history and analysis."
      onPermissionGranted={handlePermissionGranted}
    >
      {actualContent}
    </PermissionPrompt>
  );
};
```

#### **Pattern 3: Self-Fetching Components with React Query**

For components that use React Query and need to handle permission states internally:

```jsx
// components/cards/KolSummaryCard.js
import { useQuery } from "@tanstack/react-query";

export default function KolSummaryCard({ tokenAddress, layout = "summary", className = "" }) {
  // Handle different permission states
  // tokenAddress === null → Show loading skeletons (permission checking)
  // tokenAddress === "" → Show dummy data (login prompt)
  // tokenAddress === "real" → Show real data (permission granted)
  
  const isPermissionChecking = tokenAddress === null;
  const isDummyData = tokenAddress === "";
  
  // Dummy data for login prompt (blurred content)
  const dummyData = {
    unique_kols_count: 12,
    total_transactions_count: 156,
    total_buy_volume: 45000,
    total_sell_volume: 23000
  };

  const { data, isLoading, isError } = useQuery({
    queryKey: ['kol-summary', tokenAddress],
    queryFn: async () => {
      const result = await getKolTokenStats(tokenAddress);
      if (result.status === 'success') {
        return result.data?.[0] || null;
      }
      throw new Error(result.message || 'Failed to fetch KOL stats');
    },
    enabled: !!tokenAddress && !isPermissionChecking && !isDummyData,
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 0,
    retry: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });

  // Default values while loading or empty data
  const { 
    unique_kols_count = 0, 
    total_transactions_count = 0, 
    total_buy_volume = 0, 
    total_sell_volume = 0 
  } = isLoading || !data ? {} : data;

  // Define metrics with permission-aware logic
  const metrics = [
    {
      label: "KOLs",
      value: isPermissionChecking ? null : (isDummyData ? formatNumber(dummyData.unique_kols_count, 0) : (isLoading ? null : formatNumber(unique_kols_count, 0))),
    },
    {
      label: "Total Transactions", 
      value: isPermissionChecking ? null : (isDummyData ? formatNumber(dummyData.total_transactions_count, 0) : (isLoading ? null : formatNumber(total_transactions_count, 0))),
    },
    // ... more metrics
  ];

  // Render with skeleton support
  return (
    <Card className={className}>
      <CardBody>
        {metrics.map((metric, index) => (
          <div key={index}>
            <div className="text-neutral-500 text-sm">{metric.label}</div>
            {metric.value ? (
              <div className="text-xl font-medium">{metric.value}</div>
            ) : (
              <div className="h-7 w-12 mt-1 bg-gray-200 animate-pulse rounded"></div>
            )}
          </div>
        ))}
      </CardBody>
    </Card>
  );
}
```

**Parent Component Usage:**

```jsx
// content/solana/components/TokenKolSection.js
export default function TokenKolSection({ tokenAddress }) {
  // Create loading skeleton content - pass null to show loading skeletons
  const loadingContent = (
    <div className="space-y-6">
      <KolSummaryCard tokenAddress={null} layout="cards" />
      <KolTokenTable tokenAddress={null} />
    </div>
  );

  // Create blurred content - pass empty string to show dummy data
  const blurredContent = (
    <div className="space-y-6">
      <KolSummaryCard tokenAddress="" layout="cards" />
      <KolTokenTable tokenAddress="" />
    </div>
  );

  // Actual content - pass real tokenAddress to fetch data
  const actualContent = (
    <div className="space-y-6">
      <KolSummaryCard tokenAddress={tokenAddress} layout="cards" />
      <KolTokenTable tokenAddress={tokenAddress} />
    </div>
  );

  return (
    <PermissionPrompt
      requiredLevel="FREE"
      blurredContent={blurredContent}
      loadingContent={loadingContent}
      promptTitle="Login Required"
      promptMessage="Create a free account to access KOL analytics."
    >
      {actualContent}
    </PermissionPrompt>
  );
}
```

**Key Implementation Points:**

1. **Permission State Detection**: Use `tokenAddress === null` for loading skeletons, `tokenAddress === ""` for dummy data
2. **React Query Control**: Disable query with `enabled: !!tokenAddress && !isPermissionChecking && !isDummyData`
3. **Three-State Logic**: Handle permission checking (skeletons), dummy data (blurred), and real data (fetching)
4. **No API Calls**: Ensure no network requests during permission checking or dummy data display
5. **Consistent Skeletons**: Use same skeleton structure as real content layout

**When to use each pattern:**

* **Full Data Fetching**: Simple components, single data source, easy to create dummy data
* **Simplified Permission**: Complex components, multiple data sources, child components handle their own fetching
* **Self-Fetching with React Query**: Components that manage their own data fetching and need permission-aware states

### 🔧 Component API

#### PermissionPrompt Props

| Prop                  | Type        | Default        | Description                                |
| --------------------- | ----------- | -------------- | ------------------------------------------ |
| `requiredLevel`       | `string`    | `"FREE"`       | Required permission level                  |
| `children`            | `ReactNode` | -              | Content to show when user has permission   |
| `blurredContent`      | `ReactNode` | `null`         | Content to show blurred when no permission |
| `loadingContent`      | `ReactNode` | `null`         | Loading skeleton during permission check   |
| `className`           | `string`    | `""`           | Additional CSS classes                     |
| `promptTitle`         | `string`    | Auto-generated | Custom title for the prompt                |
| `promptMessage`       | `string`    | Auto-generated | Custom message for the prompt              |
| `showBlurred`         | `boolean`   | `true`         | Whether to show blurred content            |
| `onPermissionGranted` | `Function`  | `null`         | Callback when user gains permission        |

#### Authentication State Management Flow

```mermaid
flowchart TD
    A("PermissionPrompt mounts") --> B("Set initializing: true")
    B --> C("Call checkMinimumLevel()")
    C --> D("Server permission check")
    D --> E("Set permission state + call callback")
    
    F("AuthContext changes") --> G("Wait 100ms")
    G --> H("Set checking: true")
    H --> C
    
    I("Privy auth event") --> J("Wait 500ms") 
    J --> H
    
    K("Required level changes") --> B
    
    L{"Permission State?"}
    L -->|"initializing: true"| M("Show loadingContent or blurred children")
    L -->|"checking: true"| N("Show children with spinner")
    L -->|"hasPermission: true"| O("Show children normally")
    L -->|"hasPermission: false"| P("Show blurred content + prompt")
    
    E --> L
    
    classDef triggerEvent fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef authProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef displayState fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef decisionPoint fill:#f8f9fa,stroke:#455a64,color:#455a64
    
    class A,F,I,K triggerEvent
    class B,C,D,E,G,H,J authProcess
    class M,N,O,P displayState
    class L decisionPoint
```

### 📋 Implementation Checklist

#### ✅ Data State Management

* [ ] Initialize with `initialized: false`
* [ ] Use `dataState.loading || !dataState.initialized` for loading checks
* [ ] Reset state when input props change
* [ ] Handle both success and error states

#### ✅ Blurred Content

* [ ] Use same component structure as real content
* [ ] Provide realistic dummy data OR use loading skeletons for complex scenarios
* [ ] Match styling and spacing exactly
* [ ] Test visual blur effect

**Note**: For complex components with multiple data sources, you can use loading skeletons instead of dummy data in `blurredContent`. This is especially useful for tabs or components where creating realistic dummy data is impractical.

#### ✅ Self-Fetching Components (React Query Pattern)

* [ ] Implement three-state logic: `null` (skeletons), `""` (dummy data), `"real"` (fetch data)
* [ ] Add permission state detection: `isPermissionChecking` and `isDummyData`
* [ ] Control React Query with `enabled: !!tokenAddress && !isPermissionChecking && !isDummyData`
* [ ] Provide realistic dummy data for blurred content
* [ ] Ensure loading skeletons match final content layout
* [ ] Test all three states: permission checking, login prompt, and data fetching
* [ ] Verify no API calls during permission checking or dummy data display

#### ✅ Permission Integration

* [ ] Set appropriate `requiredLevel`
* [ ] Implement `onPermissionGranted` callback
* [ ] Handle loading states during permission check
* [ ] Test all permission levels

### 🎯 Permission Level Guidelines

| Level    | Use Case                        | Examples                           |
| -------- | ------------------------------- | ---------------------------------- |
| `FREE`   | Basic features for logged users | Holder lists, basic analytics      |
| `PRO`    | Advanced features               | DCA tracking, detailed insights    |
| `LEGEND` | Premium features                | Extended history, priority support |
| `KOL`    | Influencer tools                | Advanced market insights           |

### 🚨 Common Issues & Troubleshooting

#### **Self-Fetching Components Showing Zeros Instead of Skeletons**

**Problem**: Component shows `0%`, `0 KOLs`, etc. during permission checking instead of loading skeletons.

**Cause**: When `tokenAddress={null}`, React Query's `enabled: false` makes `isLoading: false`, so component shows default values (zeros).

**Solution**: Add permission state detection:
```jsx
// ❌ Wrong - shows zeros when tokenAddress=null
const metrics = [
  {
    label: "KOLs",
    value: isLoading ? null : formatNumber(unique_kols_count, 0),
  }
];

// ✅ Correct - shows skeletons when tokenAddress=null
const isPermissionChecking = tokenAddress === null;
const metrics = [
  {
    label: "KOLs", 
    value: isPermissionChecking ? null : (isLoading ? null : formatNumber(unique_kols_count, 0)),
  }
];
```

#### **API Calls During Permission Checking**

**Problem**: Network requests fire during permission checking phase.

**Cause**: React Query `enabled: !!tokenAddress` still allows calls when `tokenAddress` has a value.

**Solution**: Add permission state checks to `enabled`:
```jsx
// ❌ Wrong - API calls during permission checking
enabled: !!tokenAddress,

// ✅ Correct - no API calls during permission checking
enabled: !!tokenAddress && !isPermissionChecking && !isDummyData,
```

#### **Conflicting Props in Parent Components**

**Problem**: Component receives conflicting props like `tokenAddress={tokenAddress}` and `data={[...]}`.

**Cause**: Parent component passes both real tokenAddress and manual dummy data.

**Solution**: Use consistent prop passing:
```jsx
// ❌ Wrong - conflicting props
<KolTokenTable 
  tokenAddress={tokenAddress}  // Real address
  data={dummyData}            // Manual dummy data
/>

// ✅ Correct - consistent prop passing
<KolTokenTable tokenAddress="" />  // Component handles dummy data internally
```

### 🚀 Result

This system transforms access control from a **barrier** into a **conversion tool** by:

* [ ] **Showcasing Value**: Users see what they're missing before upgrading
* [ ] **Smooth UX**: No jarring access denied messages
* [ ] Progressive Disclosure: Features unlock as users upgrade
* [ ] Conversion Focused: Upgrade prompts guide users to appropriate tiers
