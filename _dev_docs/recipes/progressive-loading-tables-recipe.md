# Progressive Loading Tables Recipe

A comprehensive guide for creating professional progressive loading tables using our StalkchainTableComponent, TanStack Query, and proven patterns from TopHoldersTable.

## 🎯 Overview

This recipe teaches you to build tables that:
- ✅ **Render immediately** with base data and loading placeholders
- ✅ **Load data progressively** - each cell fills in as data becomes available
- ✅ **Handle multiple API calls** efficiently with parallel requests
- ✅ **Provide smooth UX** with zero layout shifts
- ✅ **Scale to production** with proper caching and error handling

## 📚 Table of Contents

1. [Core Concepts](#core-concepts)
2. [Progressive Loading Patterns](#progressive-loading-patterns)
3. [Implementation Guide](#implementation-guide)
4. [TanStack React Query Essentials](#tanstack-react-query-essentials)
5. [Performance Optimization](#performance-optimization)
6. [Best Practices](#best-practices)
7. [Troubleshooting](#troubleshooting)

## Core Concepts

### **Progressive Loading Philosophy**
Tables load base data immediately, then fill cells progressively as async data becomes available. This creates perceived performance while maintaining data integrity.

**Key Principles:**
- ✅ **Parallel execution** - All API calls start simultaneously
- ✅ **Non-blocking renders** - Components render immediately with placeholders
- ✅ **Smart caching** - Prevent unnecessary API calls with optimized cache strategies
- ✅ **Program wallet optimization** - Skip API calls for known program wallets
- ✅ **Graceful error handling** - Show "N/A" for failures, no complex error states

### **StalkchainTableComponent Integration**
Our progressive patterns work seamlessly with `StalkchainTableComponent`:

```javascript
import StalkchainTableComponent from '@/components/tables/StalkchainTableComponent';

// Base usage with progressive cells
<StalkchainTableComponent
  data={tableData}
  columns={columns}  // Includes progressive loading columns
  isLoading={isBaseDataLoading}
  skeletonRows={10}
  className="your-table-class"
  emptyTitle="No Data Found"
  emptyMessage="No data available for this query."
/>
```

## Progressive Loading Patterns

We have three proven patterns for progressive data loading:

### **Pattern 1: Single-Cell Progressive Loading**
Each cell manages its own API call and loading state.

**Use when:**
- ✅ Each cell needs different data endpoints
- ✅ Simple one-to-one data relationships
- ✅ Independent error handling per cell

**Example: PnL Cell**
```javascript
const PnlCell = ({ walletAddress, tokenAddress, field, isProgram }) => {
  // Skip API call entirely for program wallets
  if (isProgram) {
    return <span className="text-xs text-default-400">N/A</span>;
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['pnl', walletAddress, tokenAddress],
    queryFn: async () => {
      const response = await fetch(`/api/wallet/pnl/${walletAddress}/${tokenAddress}`);
      if (!response.ok) throw new Error('Failed to fetch PnL data');
      const result = await response.json();
      if (!result.success) throw new Error(result.error);
      return result.data;
    },
    enabled: !!(walletAddress && tokenAddress),
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 0, // No cache needed
    retry: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });

  if (isLoading) {
    return <LoadingPlaceholder width="w-16" height="h-4" inline={true} />;
  }

  if (isError || !data) {
    return <span className="text-xs text-default-400">N/A</span>;
  }

  const value = data[field];
  if (typeof value !== 'number') {
    return <span className="text-xs text-default-400">N/A</span>;
  }

  return (
    <span className="text-sm text-default-900">
      {formatCurrency(value, true, "USD", 2)}
    </span>
  );
};
```

### **Pattern 2: Multi-Column Progressive Loading**
Multiple columns share data from a single API call managed at the table level.

**Use when:**
- ✅ One API call provides data for multiple columns
- ✅ Need coordinated loading states across columns
- ✅ Batch processing efficiency is important

**Example: Multi-column from useState**
```javascript
export default function YourTable({ baseData, tokenAddress }) {
  const [multiColumnData, setMultiColumnData] = useState({});
  const [isMultiColumnLoading, setIsMultiColumnLoading] = useState(false);

  useEffect(() => {
    const fetchMultiColumnData = async () => {
      if (!tokenAddress || baseData.length === 0) return;
      
      setIsMultiColumnLoading(true);
      const walletAddresses = baseData.map(item => item.walletAddress);
      
      try {
        const response = await fetch('/api/wallet/multi-data', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ addresses: walletAddresses }),
        });
        
        if (response.ok) {
          const result = await response.json();
          if (result.success) {
            setMultiColumnData(result.data);
          }
        }
      } catch (error) {
        console.error('Error fetching multi-column data:', error);
      } finally {
        setIsMultiColumnLoading(false);
      }
    };

    fetchMultiColumnData();
  }, [tokenAddress, baseData]);

  const columns = useMemo(() => [
    // Base columns...
    
    // Multi-column cells sharing the same data source
    columnHelper.display({
      id: 'field1',
      header: 'Field 1',
      cell: (info) => (
        <MultiDataCell 
          address={info.row.original.walletAddress}
          field="field1"
          data={multiColumnData}
          isLoading={isMultiColumnLoading}
        />
      ),
    }),
    
    columnHelper.display({
      id: 'field2', 
      header: 'Field 2',
      cell: (info) => (
        <MultiDataCell 
          address={info.row.original.walletAddress}
          field="field2"
          data={multiColumnData}
          isLoading={isMultiColumnLoading}
        />
      ),
    }),
  ], [multiColumnData, isMultiColumnLoading]);
}
```

### **Pattern 3: Batch Provider Pattern**
Single `useQuery` provides data for multiple columns across all rows.

**Use when:**
- ✅ Batch API endpoints are available
- ✅ Need optimal network efficiency
- ✅ All rows need the same type of additional data

**Example: Batch funding data**
```javascript
export default function YourTable({ baseData }) {
  const walletAddresses = useMemo(() => 
    baseData.map(item => item.walletAddress).filter(Boolean), 
    [baseData]
  );

  const { data: batchData, isLoading: batchLoading, isError: batchError } = useQuery({
    queryKey: ['batch-funding', walletAddresses],
    queryFn: async () => {
      const response = await fetch('/api/wallet/funding/batch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          addresses: walletAddresses,
          options: { pageSize: 20, timeout: 30000, concurrency: 3 }
        }),
      });
      if (!response.ok) throw new Error('Failed to fetch batch data');
      const result = await response.json();
      if (!result.success) throw new Error(result.error);
      return result.data || {};
    },
    enabled: !!(walletAddresses && walletAddresses.length > 0),
    staleTime: 60 * 60 * 1000, // 1 hour
    gcTime: 0,
    retry: false,
  });

  const columns = useMemo(() => [
    // Base columns...
    
    columnHelper.display({
      id: 'fundingData',
      header: 'Funding Info',
      cell: (info) => (
        <BatchDataCell 
          address={info.row.original.walletAddress}
          field="fundingAmount"
          batchData={batchData || {}}
          isLoading={batchLoading}
          isError={batchError}
        />
      ),
    }),
  ], [batchData, batchLoading, batchError]);
}
```

## TanStack React Query Essentials

TanStack React Query is the foundation for all progressive loading in our application. Understanding these core concepts is essential for building scalable, performant data loading components.

### **Core Query Lifecycle & Essential Hooks**

**The foundation of React Query revolves around three primary hooks that manage the complete data fetching lifecycle.**

#### **useQuery - Declarative Data Fetching**
```javascript
const { data, isPending, isFetching, isError, error } = useQuery({
  queryKey: ['todos', { status: 'active' }],
  queryFn: ({ queryKey }) => fetchTodos(queryKey[1]),
  enabled: !!someCondition, // Control when query runs
  staleTime: 5 * 60 * 1000, // 5 minutes fresh
  gcTime: 10 * 60 * 1000,   // 10 minutes in cache
  retry: false,             // Disable retries for table cells
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  refetchOnReconnect: false,
});
```

#### **Query States Explained**
- **`isPending`** - No data available yet (initial loading)
- **`isFetching`** - Background refetch in progress (can have data)
- **`isError`** - Query failed
- **`isSuccess`** - Query succeeded with data

#### **useMutation - Server State Changes**
```javascript
const updateMutation = useMutation({
  mutationFn: updateData,
  onSuccess: (data, variables) => {
    // Update cache immediately
    queryClient.setQueryData(['item', variables.id], data);
  },
  onError: (error, variables, context) => {
    // Handle errors gracefully
    console.error('Update failed:', error);
  },
  onSettled: () => {
    // Always refetch to ensure consistency
    queryClient.invalidateQueries({ queryKey: ['items'] });
  }
});
```

#### **useQueryClient - Direct Cache Access**
```javascript
const queryClient = useQueryClient();

// Update cache directly
queryClient.setQueryData(['item', id], newData);

// Invalidate and refetch
queryClient.invalidateQueries({ queryKey: ['items'] });

// Cancel ongoing queries
await queryClient.cancelQueries({ queryKey: ['items'] });
```

### **Cache Configuration & Stale-While-Revalidate Strategy**

**React Query's performance advantage comes from intelligent cache configuration that balances data freshness with network efficiency.**

#### **Key Configuration Options**

```javascript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,     // 5 minutes fresh
      gcTime: 1000 * 60 * 30,       // 30 minutes cached
      retry: 3,                     // Retry failed requests
      refetchOnWindowFocus: true,   // Keep data current
      refetchOnMount: true,         // Refetch when component mounts
      refetchOnReconnect: true,     // Refetch on network recovery
    },
    mutations: {
      retry: 1,                     // Retry mutations once
    },
  },
});
```

#### **staleTime vs gcTime**
- **`staleTime`** - How long data is considered "fresh" (won't refetch)
- **`gcTime`** - How long inactive data stays in memory cache
- **Default behavior**: `staleTime: 0` (always stale), `gcTime: 5 minutes`

#### **Progressive Loading Optimized Configuration**
```javascript
// For table cells - prevent excessive refetching
const tableQueryConfig = {
  staleTime: 60 * 60 * 1000, // 1 hour - prevent refetch on tab switch
  gcTime: 0,                 // No cache needed - data persists in component state
  retry: false,              // No retries - simple error handling
  refetchOnMount: false,     // Don't refetch when component mounts again
  refetchOnReconnect: false, // Don't refetch on network reconnect
  refetchOnWindowFocus: false, // Don't refetch on window focus
};
```

### **Query Keys - The Cache Foundation**

**Query keys serve as the caching foundation and must be arrays structured from general to specific.**

#### **Hierarchical Structure**
```javascript
// ✅ Good: Hierarchical structure
['todos']                    // All todos
['todos', 'list']           // Todo lists
['todos', 'detail', id]     // Individual todo

// ✅ Good: Include variables affecting the query
['todos', { status: 'active', page: 1 }]
['user', userId, 'posts', { limit: 10 }]

// ❌ Bad: Too generic or unstable
['data']                    // Too generic
['todos', Math.random()]    // Unstable
```

#### **Progressive Loading Query Keys**
```javascript
// Single cell data
['pnl', walletAddress, tokenAddress]
['balance', walletAddress]
['funding', walletAddress]

// Batch data
['funding-batch', walletAddresses] // Array of addresses
['pnl-batch', pairs]              // Array of wallet-token pairs

// Multi-column data
['multi-data', tableId, addresses]
```

### **Mutation Handling with Optimistic Updates**

**Mutations handle all data modification operations and require careful coordination with the query cache.**

#### **Basic Mutation Pattern**
```javascript
const addItemMutation = useMutation({
  mutationFn: addItem,
  onSuccess: () => {
    // Simple approach: just invalidate and refetch
    queryClient.invalidateQueries({ queryKey: ['items'] });
  }
});
```

#### **Optimistic Updates Pattern**
```javascript
const updateItemMutation = useMutation({
  mutationFn: updateItem,
  onMutate: async (newItem) => {
    // Cancel ongoing queries to prevent race conditions
    await queryClient.cancelQueries({ queryKey: ['items'] });
    
    // Snapshot current state for rollback
    const previousItems = queryClient.getQueryData(['items']);
    
    // Optimistically update cache
    queryClient.setQueryData(['items'], (old) => 
      old.map(item => item.id === newItem.id ? newItem : item)
    );
    
    // Return rollback context
    return { previousItems };
  },
  onError: (error, variables, context) => {
    // Rollback on error
    queryClient.setQueryData(['items'], context.previousItems);
  },
  onSettled: () => {
    // Always refetch to ensure consistency
    queryClient.invalidateQueries({ queryKey: ['items'] });
  }
});
```

#### **Simplified Optimistic UI (Recommended for Simple Cases)**
```javascript
const addTodoMutation = useMutation({
  mutationFn: addTodo,
  onSettled: () => queryClient.invalidateQueries({ queryKey: ['todos'] }),
});

// In component
<ul>
  {todos.map(todo => <li key={todo.id}>{todo.text}</li>)}
  {addTodoMutation.isPending && (
    <li style={{ opacity: 0.5 }}>
      {addTodoMutation.variables} {/* Show pending item */}
    </li>
  )}
</ul>
```

### **Next.js Integration Patterns & Hydration**

**Next.js integration requires careful coordination between server-side data fetching and client-side hydration.**

#### **App Router Integration Pattern**
```javascript
// app/providers.tsx - Client Component
'use client'

import {
  isServer,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query'
import { ReactQueryStreamedHydration } from '@tanstack/react-query-next-experimental'

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Prevent immediate refetching on client after SSR
        staleTime: 60 * 1000,
      },
    },
  })
}

let browserQueryClient: QueryClient | undefined = undefined

function getQueryClient() {
  if (isServer) {
    // Server: always make a new query client
    return makeQueryClient()
  } else {
    // Browser: reuse existing client or create new one
    if (!browserQueryClient) browserQueryClient = makeQueryClient()
    return browserQueryClient
  }
}

export function Providers(props: { children: React.ReactNode }) {
  const queryClient = getQueryClient()

  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryStreamedHydration>
        {props.children}
      </ReactQueryStreamedHydration>
    </QueryClientProvider>
  )
}
```

#### **Server Component Data Prefetching**
```javascript
// app/posts/page.tsx - Server Component
import { QueryClient, dehydrate, HydrationBoundary } from '@tanstack/react-query'
import PostsList from './posts-list'

export default async function PostsPage() {
  const queryClient = new QueryClient()

  // Prefetch data on server
  await queryClient.prefetchQuery({
    queryKey: ['posts'],
    queryFn: getPosts,
  })

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <PostsList />
    </HydrationBoundary>
  )
}
```

#### **Client Component Using Hydrated Data**
```javascript
// posts-list.tsx - Client Component
'use client'

import { useQuery } from '@tanstack/react-query'

export default function PostsList() {
  // This data is already available from server prefetch
  const { data, isLoading } = useQuery({
    queryKey: ['posts'],
    queryFn: getPosts,
  })

  if (isLoading) return <div>Loading...</div>
  
  return (
    <div>
      {data?.map(post => (
        <div key={post.id}>{post.title}</div>
      ))}
    </div>
  )
}
```

### **Query Invalidation & Cache Management**

**Effective cache invalidation strategies determine application performance and data consistency.**

#### **Invalidation Strategies**
```javascript
const queryClient = useQueryClient();

// Exact match invalidation
queryClient.invalidateQueries({ queryKey: ['todos', 'list'] });

// Hierarchical invalidation (affects all todo queries)
queryClient.invalidateQueries({ queryKey: ['todos'] });

// Predicate-based invalidation
queryClient.invalidateQueries({
  predicate: query => 
    query.queryKey[0] === 'todos' && 
    query.queryKey[1]?.status === 'active'
});

// Multiple invalidations
await Promise.all([
  queryClient.invalidateQueries({ queryKey: ['todos'] }),
  queryClient.invalidateQueries({ queryKey: ['users'] }),
]);
```

#### **Direct Cache Updates**
```javascript
// Update specific item
queryClient.setQueryData(['todo', id], newTodoData);

// Update list with new item
queryClient.setQueryData(['todos'], (old) => [...old, newTodo]);

// Conditional update (bail out if no data)
queryClient.setQueryData(['todo', id], (prev) => 
  prev ? { ...prev, completed: true } : undefined
);
```

### **Advanced Patterns for Progressive Loading**

#### **Dependent Queries with Conditional Fetching**
```javascript
// Fetch user first, then user's posts
const { data: user } = useQuery({
  queryKey: ['user', userId],
  queryFn: getUser,
});

const { data: posts } = useQuery({
  queryKey: ['posts', user?.id],
  queryFn: getUserPosts,
  enabled: !!user?.id, // Only fetch when user exists
});
```

#### **Parallel Queries with useQueries**
```javascript
// Dynamic parallel fetching
const userQueries = useQueries({
  queries: userIds.map(id => ({
    queryKey: ['user', id],
    queryFn: () => fetchUser(id),
    staleTime: 60 * 60 * 1000,
  }))
});

// Check if all queries are done
const allLoaded = userQueries.every(query => query.isSuccess);
const anyError = userQueries.some(query => query.isError);
```

#### **Infinite Queries for Pagination**
```javascript
const {
  data,
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage,
} = useInfiniteQuery({
  queryKey: ['posts'],
  queryFn: ({ pageParam = 0 }) => fetchPosts(pageParam),
  getNextPageParam: (lastPage) => lastPage.nextCursor,
  initialPageParam: 0,
});
```

### **Progressive Loading Best Practices**

#### **Query Configuration for Table Cells**
```javascript
// ✅ Optimized for table cells
const tableQueries = {
  staleTime: 60 * 60 * 1000,    // 1 hour fresh
  gcTime: 0,                    // No cache persistence needed
  retry: false,                 // No retries for simplicity
  refetchOnWindowFocus: false,  // Prevent excessive refetching
  refetchOnMount: false,        // Don't refetch on remount
  refetchOnReconnect: false,    // Don't refetch on reconnect
};

// ✅ Use in cells
const { data, isLoading } = useQuery({
  queryKey: ['cell-data', id],
  queryFn: fetchCellData,
  enabled: !!id,
  ...tableQueries,
});
```

#### **Error Boundaries Integration**
```javascript
import { QueryErrorResetBoundary } from '@tanstack/react-query'
import { ErrorBoundary } from 'react-error-boundary'

function TableWithErrorBoundary() {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          fallbackRender={({ resetErrorBoundary }) => (
            <div>
              Something went wrong!
              <button onClick={resetErrorBoundary}>Try again</button>
            </div>
          )}
        >
          <YourProgressiveTable />
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  )
}
```

#### **Suspense Integration (Optional)**
```javascript
import { useSuspenseQuery } from '@tanstack/react-query'

function SuspenseTable() {
  const { data } = useSuspenseQuery({
    queryKey: ['table-data'],
    queryFn: fetchTableData,
  });

  // data is always defined with useSuspenseQuery
  return <TableComponent data={data} />;
}

// Wrap with Suspense boundary
function App() {
  return (
    <Suspense fallback={<TableSkeleton />}>
      <SuspenseTable />
    </Suspense>
  )
}
```

## Implementation Guide

### **Step 1: Create Loading Placeholder Component**

```javascript
// Reusable loading placeholder
const LoadingPlaceholder = ({ width, height = "h-4", inline = false }) => (
  <span className={`${height} ${width} bg-gray-200 animate-pulse rounded ${inline ? 'inline-block' : 'block'}`}></span>
);
```

### **Step 2: Choose Your Progressive Loading Pattern**

Pick the pattern that best fits your data requirements:

#### **For Single-Cell Pattern:**
```javascript
const YourSingleCell = ({ address, field }) => {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['single-data', address, field],
    queryFn: () => fetchSingleData(address, field),
    enabled: !!address,
    staleTime: 60 * 60 * 1000,
    gcTime: 0,
    retry: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    refetchOnWindowFocus: false,
  });

  if (isLoading) return <LoadingPlaceholder width="w-16" inline={true} />;
  if (isError || !data) return <span className="text-xs text-default-400">N/A</span>;
  
  return <span className="text-sm">{data[field]}</span>;
};
```

#### **For Multi-Column Pattern:**
```javascript
const YourMultiCell = ({ address, field, data, isLoading }) => {
  if (isLoading) return <LoadingPlaceholder width="w-16" inline={true} />;
  
  const value = data[address]?.[field];
  if (!value) return <span className="text-xs text-default-400">N/A</span>;
  
  return <span className="text-sm">{value}</span>;
};
```

#### **For Batch Provider Pattern:**
```javascript
const YourBatchCell = ({ address, field, batchData, isLoading, isError }) => {
  if (isLoading) return <LoadingPlaceholder width="w-16" inline={true} />;
  if (isError) return <span className="text-xs text-default-400">N/A</span>;
  
  const value = batchData[address]?.[field];
  if (!value) return <span className="text-xs text-default-400">N/A</span>;
  
  return <span className="text-sm">{value}</span>;
};
```

### **Step 3: Implement Your API Endpoint**

Create optimized API endpoints that support your chosen pattern:

```javascript
// File: app/api/your-endpoint/batch/route.js
import { NextResponse } from 'next/server';
import { checkMinimumLevel } from '@/actions/auth/checkAuthPermission';
import DOMPurify from 'isomorphic-dompurify';

export async function POST(request) {
  try {
    const hasPermission = await checkMinimumLevel('FREE');
    if (!hasPermission) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { addresses } = body;

    if (!Array.isArray(addresses) || addresses.length === 0) {
      return NextResponse.json(
        { success: false, error: "Invalid request: 'addresses' array is required." },
        { status: 400 }
      );
    }

    if (addresses.length > 100) {
      return NextResponse.json(
        { success: false, error: "Batch size cannot exceed 100 addresses." },
        { status: 400 }
      );
    }

    const sanitizedAddresses = addresses.map(addr => DOMPurify.sanitize(addr));
    
    // Your batch fetching logic here
    const results = await fetchBatchData(sanitizedAddresses);
    
    return NextResponse.json({ 
      success: true, 
      data: results
    });
  } catch (error) {
    console.error('Batch API error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### **Step 4: Create Your Complete Table Component**

```javascript
import { useMemo } from "react";
import { createColumnHelper } from "@tanstack/react-table";
import { useQuery } from "@tanstack/react-query";
import StalkchainTableComponent from "@/components/tables/StalkchainTableComponent";

const columnHelper = createColumnHelper();

export default function YourProgressiveTable({ 
  data = [], 
  isLoading = false, 
  error = null,
  className = ""
}) {
  // Extract addresses for batch operations
  const addresses = useMemo(() => 
    data.map(item => item.address).filter(Boolean), 
    [data]
  );

  // Example: Batch provider pattern
  const { data: batchData, isLoading: batchLoading, isError: batchError } = useQuery({
    queryKey: ['batch-data', addresses],
    queryFn: async () => {
      // Your batch fetching logic here
      return {};
    },
    enabled: !!(addresses && addresses.length > 0),
    staleTime: 60 * 60 * 1000,
    gcTime: 0,
    retry: false,
  });

  // Define columns with all patterns
  const columns = useMemo(() => [
    // Base columns
    columnHelper.accessor('rank', {
      header: '#',
      cell: (info) => <span className="text-sm text-default-500">{info.getValue()}</span>,
      size: 50,
    }),
    
    columnHelper.accessor('name', {
      header: 'Name',
      cell: (info) => <span className="text-sm text-default-900">{info.getValue()}</span>,
      size: 200,
    }),

    // Pattern 1: Single-cell progressive loading
    columnHelper.display({
      id: 'singleCellData',
      header: 'Single Cell',
      cell: (info) => (
        <YourSingleCell address={info.row.original.address} />
      ),
      size: 120,
    }),

    // Pattern 3: Batch provider
    columnHelper.display({
      id: 'batchColumn',
      header: 'Batch Data',
      cell: (info) => (
        <YourBatchCell 
          address={info.row.original.address}
          field="batchField"
          batchData={batchData || {}}
          isLoading={batchLoading}
          isError={batchError}
        />
      ),
      size: 120,
    }),
  ], [batchData, batchLoading, batchError]);

  // Handle loading and error states
  if (isLoading) {
    return <StalkchainTableComponent columns={columns} data={Array(10).fill({})} isLoading={true} skeletonRows={10} />;
  }

  if (error) {
    return <StalkchainTableComponent columns={columns} data={[]} error={error} />;
  }

  return (
    <StalkchainTableComponent
      data={data}
      columns={columns}
      className={className}
      emptyTitle="No Data Found"
      emptyMessage="No data available for this query."
    />
  );
}
```

## TanStack React Query Essentials

TanStack React Query is the foundation for all progressive loading in our application. Understanding these core concepts is essential for building scalable, performant data loading components.

### **Core Query Lifecycle & Essential Hooks**

**The foundation of React Query revolves around three primary hooks that manage the complete data fetching lifecycle.**

#### **useQuery - Declarative Data Fetching**
```javascript
const { data, isPending, isFetching, isError, error } = useQuery({
  queryKey: ['todos', { status: 'active' }],
  queryFn: ({ queryKey }) => fetchTodos(queryKey[1]),
  enabled: !!someCondition, // Control when query runs
  staleTime: 5 * 60 * 1000, // 5 minutes fresh
  gcTime: 10 * 60 * 1000,   // 10 minutes in cache
  retry: false,             // Disable retries for table cells
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  refetchOnReconnect: false,
});
```

#### **Query States Explained**
- **`isPending`** - No data available yet (initial loading)
- **`isFetching`** - Background refetch in progress (can have data)
- **`isError`** - Query failed
- **`isSuccess`** - Query succeeded with data

#### **useMutation - Server State Changes**
```javascript
const updateMutation = useMutation({
  mutationFn: updateData,
  onSuccess: (data, variables) => {
    // Update cache immediately
    queryClient.setQueryData(['item', variables.id], data);
  },
  onError: (error, variables, context) => {
    // Handle errors gracefully
    console.error('Update failed:', error);
  },
  onSettled: () => {
    // Always refetch to ensure consistency
    queryClient.invalidateQueries({ queryKey: ['items'] });
  }
});
```

#### **useQueryClient - Direct Cache Access**
```javascript
const queryClient = useQueryClient();

// Update cache directly
queryClient.setQueryData(['item', id], newData);

// Invalidate and refetch
queryClient.invalidateQueries({ queryKey: ['items'] });

// Cancel ongoing queries
await queryClient.cancelQueries({ queryKey: ['items'] });
```

### **Cache Configuration & Stale-While-Revalidate Strategy**

**React Query's performance advantage comes from intelligent cache configuration that balances data freshness with network efficiency.**

#### **Key Configuration Options**

```javascript
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5,     // 5 minutes fresh
      gcTime: 1000 * 60 * 30,       // 30 minutes cached
      retry: 3,                     // Retry failed requests
      refetchOnWindowFocus: true,   // Keep data current
      refetchOnMount: true,         // Refetch when component mounts
      refetchOnReconnect: true,     // Refetch on network recovery
    },
    mutations: {
      retry: 1,                     // Retry mutations once
    },
  },
});
```

#### **staleTime vs gcTime**
- **`staleTime`** - How long data is considered "fresh" (won't refetch)
- **`gcTime`** - How long inactive data stays in memory cache
- **Default behavior**: `staleTime: 0` (always stale), `gcTime: 5 minutes`

#### **Progressive Loading Optimized Configuration**
```javascript
// For table cells - prevent excessive refetching
const tableQueryConfig = {
  staleTime: 60 * 60 * 1000,    // 1 hour fresh
  gcTime: 0,                    // No cache needed - data persists in component state
  retry: false,              // No retries - simple error handling
  refetchOnMount: false,     // Don't refetch when component mounts again
  refetchOnReconnect: false,    // Don't refetch on network reconnect
  refetchOnWindowFocus: false,    // Don't refetch on window focus
};
```

### **Query Keys - The Cache Foundation**

**Query keys serve as the caching foundation and must be arrays structured from general to specific.**

#### **Hierarchical Structure**
```javascript
// ✅ Good: Hierarchical structure
['todos']                    // All todos
['todos', 'list']           // Todo lists
['todos', 'detail', id]     // Individual todo

// ✅ Good: Include variables affecting the query
['todos', { status: 'active', page: 1 }]
['user', userId, 'posts', { limit: 10 }]

// ❌ Bad: Too generic or unstable
['data']                    // Too generic
['todos', Math.random()]    // Unstable
```

#### **Progressive Loading Query Keys**
```javascript
// Single cell data
['pnl', walletAddress, tokenAddress]
['balance', walletAddress]
['funding', walletAddress]

// Batch data
['funding-batch', walletAddresses] // Array of addresses
['pnl-batch', pairs]              // Array of wallet-token pairs

// Multi-column data
['multi-data', tableId, addresses]
```

### **Mutation Handling with Optimistic Updates**

**Mutations handle all data modification operations and require careful coordination with the query cache.**

#### **Basic Mutation Pattern**
```javascript
const addItemMutation = useMutation({
  mutationFn: addItem,
  onSuccess: () => {
    // Simple approach: just invalidate and refetch
    queryClient.invalidateQueries({ queryKey: ['items'] });
  }
});
```

#### **Optimistic Updates Pattern**
```javascript
const updateItemMutation = useMutation({
  mutationFn: updateItem,
  onMutate: async (newItem) => {
    // Cancel ongoing queries to prevent race conditions
    await queryClient.cancelQueries({ queryKey: ['items'] });
    
    // Snapshot current state for rollback
    const previousItems = queryClient.getQueryData(['items']);
    
    // Optimistically update cache
    queryClient.setQueryData(['items'], (old) => 
      old.map(item => item.id === newItem.id ? newItem : item)
    );
    
    // Return rollback context
    return { previousItems };
  },
  onError: (error, variables, context) => {
    // Rollback on error
    queryClient.setQueryData(['items'], context.previousItems);
  },
  onSettled: () => {
    // Always refetch to ensure consistency
    queryClient.invalidateQueries({ queryKey: ['items'] });
  }
});
```

#### **Simplified Optimistic UI (Recommended for Simple Cases)**
```javascript
const addTodoMutation = useMutation({
  mutationFn: addTodo,
  onSettled: () => queryClient.invalidateQueries({ queryKey: ['todos'] }),
});

// In component
<ul>
  {todos.map(todo => <li key={todo.id}>{todo.text}</li>)}
  {addTodoMutation.isPending && (
    <li style={{ opacity: 0.5 }}>
      {addTodoMutation.variables} {/* Show pending item */}
    </li>
  )}
</ul>
```

### **Next.js Integration Patterns & Hydration**

**Next.js integration requires careful coordination between server-side data fetching and client-side hydration.**

#### **App Router Integration Pattern**
```javascript
// app/providers.tsx - Client Component
'use client'

import {
  isServer,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query'
import { ReactQueryStreamedHydration } from '@tanstack/react-query-next-experimental'

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Prevent immediate refetching on client after SSR
        staleTime: 60 * 1000,
      },
    },
  })
}

let browserQueryClient: QueryClient | undefined = undefined

function getQueryClient() {
  if (isServer) {
    // Server: always make a new query client
    return makeQueryClient()
  } else {
    // Browser: reuse existing client or create new one
    if (!browserQueryClient) browserQueryClient = makeQueryClient()
    return browserQueryClient
  }
}

export function Providers(props: { children: React.ReactNode }) {
  const queryClient = getQueryClient()

  return (
    <QueryClientProvider client={queryClient}>
      <ReactQueryStreamedHydration>
        {props.children}
      </ReactQueryStreamedHydration>
    </QueryClientProvider>
  )
}
```

#### **Server Component Data Prefetching**
```javascript
// app/posts/page.tsx - Server Component
import { QueryClient, dehydrate, HydrationBoundary } from '@tanstack/react-query'
import PostsList from './posts-list'

export default async function PostsPage() {
  const queryClient = new QueryClient()

  // Prefetch data on server
  await queryClient.prefetchQuery({
    queryKey: ['posts'],
    queryFn: getPosts,
  })

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <PostsList />
    </HydrationBoundary>
  )
}
```

#### **Client Component Using Hydrated Data**
```javascript
// posts-list.tsx - Client Component
'use client'

import { useQuery } from '@tanstack/react-query'

export default function PostsList() {
  // This data is already available from server prefetch
  const { data, isLoading } = useQuery({
    queryKey: ['posts'],
    queryFn: getPosts,
  })

  if (isLoading) return <div>Loading...</div>
  
  return (
    <div>
      {data?.map(post => (
        <div key={post.id}>{post.title}</div>
      ))}
    </div>
  )
}
```

### **Query Invalidation & Cache Management**

**Effective cache invalidation strategies determine application performance and data consistency.**

#### **Invalidation Strategies**
```javascript
const queryClient = useQueryClient();

// Exact match invalidation
queryClient.invalidateQueries({ queryKey: ['todos', 'list'] });

// Hierarchical invalidation (affects all todo queries)
queryClient.invalidateQueries({ queryKey: ['todos'] });

// Predicate-based invalidation
queryClient.invalidateQueries({
  predicate: query => 
    query.queryKey[0] === 'todos' && 
    query.queryKey[1]?.status === 'active'
});

// Multiple invalidations
await Promise.all([
  queryClient.invalidateQueries({ queryKey: ['todos'] }),
  queryClient.invalidateQueries({ queryKey: ['users'] }),
]);
```

#### **Direct Cache Updates**
```javascript
// Update specific item
queryClient.setQueryData(['todo', id], newTodoData);

// Update list with new item
queryClient.setQueryData(['todos'], (old) => [...old, newTodo]);

// Conditional update (bail out if no data)
queryClient.setQueryData(['todo', id], (prev) => 
  prev ? { ...prev, completed: true } : undefined
);
```

#### **Global Mutation Callbacks**
```javascript
const queryClient = new QueryClient({
  mutationCache: new MutationCache({
    onSuccess: () => {
      // Auto-invalidate on any successful mutation
      queryClient.invalidateQueries();
    },
    onError: (error) => {
      console.error('Global mutation error:', error);
    }
  })
});
```

### **Advanced Patterns for Progressive Loading**

#### **Dependent Queries with Conditional Fetching**
```javascript
// Fetch user first, then user's posts
const { data: user } = useQuery({
  queryKey: ['user', userId],
  queryFn: getUser,
});

const { data: posts } = useQuery({
  queryKey: ['posts', user?.id],
  queryFn: getUserPosts,
  enabled: !!user?.id, // Only fetch when user exists
});
```

#### **Parallel Queries with useQueries**
```javascript
// Dynamic parallel fetching
const userQueries = useQueries({
  queries: userIds.map(id => ({
    queryKey: ['user', id],
    queryFn: () => fetchUser(id),
    staleTime: 60 * 60 * 1000,
  }))
});

// Check if all queries are done
const allLoaded = userQueries.every(query => query.isSuccess);
const anyError = userQueries.some(query => query.isError);
```

#### **Infinite Queries for Pagination**
```javascript
const {
  data,
  fetchNextPage,
  hasNextPage,
  isFetchingNextPage,
} = useInfiniteQuery({
  queryKey: ['posts'],
  queryFn: ({ pageParam = 0 }) => fetchPosts(pageParam),
  getNextPageParam: (lastPage) => lastPage.nextCursor,
  initialPageParam: 0,
});
```

### **Progressive Loading Best Practices**

#### **Query Configuration for Table Cells**
```javascript
// ✅ Optimized for table cells
const tableQueries = {
  staleTime: 60 * 60 * 1000,    // 1 hour fresh
  gcTime: 0,                    // No cache persistence needed
  retry: false,                 // No retries for simplicity
  refetchOnWindowFocus: false,  // Prevent excessive refetching
  refetchOnMount: false,        // Don't refetch on remount
  refetchOnReconnect: false,    // Don't refetch on reconnect
};

// ✅ Use in cells
const { data, isLoading } = useQuery({
  queryKey: ['cell-data', id],
  queryFn: fetchCellData,
  enabled: !!id,
  ...tableQueries,
});
```

#### **Error Boundaries Integration**
```javascript
import { QueryErrorResetBoundary } from '@tanstack/react-query'
import { ErrorBoundary } from 'react-error-boundary'

function TableWithErrorBoundary() {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          onReset={reset}
          fallbackRender={({ resetErrorBoundary }) => (
            <div>
              Something went wrong!
              <button onClick={resetErrorBoundary}>Try again</button>
            </div>
          )}
        >
          <YourProgressiveTable />
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  )
}
```

#### **Suspense Integration (Optional)**
```javascript
import { useSuspenseQuery } from '@tanstack/react-query'

function SuspenseTable() {
  const { data } = useSuspenseQuery({
    queryKey: ['table-data'],
    queryFn: fetchTableData,
  });

  // data is always defined with useSuspenseQuery
  return <TableComponent data={data} />;
}

// Wrap with Suspense boundary
function App() {
  return (
    <Suspense fallback={<TableSkeleton />}>
      <SuspenseTable />
    </Suspense>
  )
}
```

## Performance Optimization

### **Query Key Strategies**

```javascript
// ✅ Good: Stable, specific query keys
queryKey: ['pnl', walletAddress, tokenAddress]
queryKey: ['funding-batch', walletAddresses]
queryKey: ['sol-balance', walletAddress]

// ❌ Bad: Unstable or too generic query keys
queryKey: ['data', Math.random()]
queryKey: ['api']
queryKey: [walletAddress] // Too generic
```

### **Conditional Queries**

```javascript
// ✅ Good: Skip queries for program wallets
const { data } = useQuery({
  queryKey: ['pnl', walletAddress, tokenAddress],
  queryFn: fetchPnlData,
  enabled: !!(walletAddress && tokenAddress && !isProgram),
  ...optimizedQueryConfig
});

// ✅ Good: Early return for program wallets
if (isProgram) {
  return <span className="text-xs text-default-400">N/A</span>;
}
```

### **Memory Management**

```javascript
// ✅ Good: No cache needed for table cells
const optimizedConfig = {
  staleTime: 60 * 60 * 1000, // 1 hour
  gcTime: 0, // No cache - data persists in component state
  retry: false,
  refetchOnMount: false,
  refetchOnReconnect: false,
  refetchOnWindowFocus: false,
};

// ❌ Bad: Excessive caching
const badConfig = {
  staleTime: 0, // Always refetch
  gcTime: Infinity, // Never cleanup
  retry: 3, // Retry on errors
  refetchOnWindowFocus: true, // Refetch on focus
};
```

## Best Practices

### **Error Handling**
- Always return simple "N/A" for errors
- No tooltips or complex error messages
- No retry mechanisms for table cells
- Graceful degradation for missing data

### **Loading States**
- Use consistent loading placeholders
- Show loading state immediately
- Don't block table rendering
- Progressive data population

### **Program Wallet Optimization**
- Always check `isProgram` prop first
- Return "N/A" immediately without API call
- Use early return pattern for clarity

### **API Endpoint Standards**
- Use Next.js API routes in `/api/[category]/[type]/[identifier]/`
- Implement authentication with `checkMinimumLevel('FREE')`
- Sanitize inputs with `DOMPurify.sanitize()`
- Return consistent response format: `{ success: boolean, data?: any, error?: string }`

### **Column Configuration**
- Disable sorting for async columns unless specifically needed
- Set appropriate column sizes
- Use descriptive column IDs
- Pass metadata through `tableOptions.meta`

## Troubleshooting

### **Common Issues**

#### **API calls not parallel**
```javascript
// ❌ Problem: Conditional useQuery calls
if (someCondition) {
  const { data } = useQuery({ ... });
}

// ✅ Solution: Always call useQuery, use enabled option
const { data } = useQuery({
  queryKey: ['data', id],
  queryFn: fetchData,
  enabled: someCondition, // Control execution here
});
```

#### **Excessive refetching**
```javascript
// ❌ Problem: Default TanStack Query behavior
const { data } = useQuery({
  queryKey: ['data'],
  queryFn: fetchData,
  // Uses defaults: refetchOnWindowFocus: true, etc.
});

// ✅ Solution: Disable unnecessary refetching
const { data } = useQuery({
  queryKey: ['data'],
  queryFn: fetchData,
  staleTime: 60 * 60 * 1000,
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  refetchOnReconnect: false,
});
```

#### **Program wallets making API calls**
```javascript
// ❌ Problem: API call happens regardless
const { data } = useQuery({
  queryKey: ['data', walletAddress],
  queryFn: () => fetchData(walletAddress),
});

// ✅ Solution: Check isProgram first
if (isProgram) {
  return <span className="text-xs text-default-400">N/A</span>;
}

const { data } = useQuery({
  queryKey: ['data', walletAddress],
  queryFn: () => fetchData(walletAddress),
  enabled: !isProgram,
});
```

#### **Memory leaks**
```javascript
// ❌ Problem: Infinite cache time
const { data } = useQuery({
  queryKey: ['data'],
  queryFn: fetchData,
  gcTime: Infinity,
});

// ✅ Solution: Set gcTime to 0 for table cells
const { data } = useQuery({
  queryKey: ['data'],
  queryFn: fetchData,
  gcTime: 0, // No cache needed
});
```

### **DevTools Verification**

#### **Network Tab Checks**
- All API calls should start simultaneously when table loads
- No API calls should be made for program wallets (🤖)
- Tab switching should not trigger excessive refetching

#### **React Query DevTools**
- Check query states in React Query DevTools
- Verify stale times are respected
- Monitor cache sizes and cleanup

#### **Console Verification**
- No error logs for program wallets
- No "Failed to fetch" errors for expected failures
- Clean component unmounting

---

This recipe provides everything needed to implement professional progressive loading tables that scale from simple single-cell loading to complex multi-API coordination. Follow these patterns to create responsive, efficient data loading experiences that users love. 