# Active Orders Feed Integration Recipe

**Target Audience**: Junior Front-end Developers & AI Agents  
**Purpose**: Build real-time Jupiter DCA Active Orders components with optimistic UI updates  
**Prerequisites**: Basic React, understanding of TanStack Query concepts  

## Table of Contents

1. [System Overview](#system-overview)
2. [Building Blocks Available](#building-blocks-available)
3. [Real-time Architecture](#real-time-architecture)
4. [Basic Integration Steps](#basic-integration-steps)
5. [Component Patterns](#component-patterns)
6. [Advanced Features](#advanced-features)
7. [Troubleshooting](#troubleshooting)

---

## System Overview

The Active Orders Feed system provides **real-time DCA order tracking** with the following capabilities:

- ✅ **Real-time Progress Updates**: Orders update instantly when fills occur
- ✅ **Optimistic UI**: Immediate visual feedback for user actions
- ✅ **Infinite Scrolling**: Paginated data loading with cursor-based pagination
- ✅ **Smart Filtering**: Search, status, and USD amount filters
- ✅ **Sound Notifications**: Audio alerts for real-time events
- ✅ **Cross-component Sync**: Updates reflected across all active components

### Data Flow Architecture

```
PostgreSQL → Socket.IO Server → Frontend Components
     ↓              ↓                    ↓
   NOTIFY        Emit Event        React Query Cache
     ↓              ↓                    ↓
  Triggers      WebSocket            Optimistic UI
```

---

## Building Blocks Available

### 1. Hook: `useActiveOrdersFeed`
**Location**: `hooks/useActiveOrdersFeed.js`

```javascript
import { useActiveOrdersFeed } from '@/hooks/useActiveOrdersFeed';

const {
  data,                    // Paginated order data
  fetchNextPage,           // Load more orders
  hasNextPage,            // More data available?
  isFetchingNextPage,     // Loading next page?
  isLoading,              // Initial loading
  isError,                // Error state
  error,                  // Error details
  seenDcaKeys,           // Deduplication tracking
  refresh,               // Manual refresh
  getAllOrders           // Flatten all pages
} = useActiveOrdersFeed({
  search: 'wallet_address_or_token',
  status: 'active',       // 'active' | 'completed' | 'canceled' | 'overdue_expired'
  minUsd: '100',          
  maxUsd: '10000',
  soundEnabled: true,
  enabled: true
});
```

### 2. API Endpoint: `/api/activeOrdersFeed`
**Location**: `app/api/activeOrdersFeed/route.js`

Supports query parameters:
- `cursor`: Pagination cursor
- `search`: Wallet/token search
- `status`: Order status filter
- `minUsd`/`maxUsd`: USD amount filters

### 3. Table Component: `StalkchainTableComponent`
**Location**: `components/tables/StalkchainTableComponent.js`

Pre-built table with sorting, loading states, and responsive design.

### 4. Socket.IO Context: `useStream`
**Location**: `context/SocketIoContext.js`

Real-time WebSocket connection with authentication.

---

## Real-time Architecture

### Events Overview

| Event Type | Trigger | Data Updated | UI Impact |
|------------|---------|--------------|-----------|
| `order_opened` | New DCA created | New order added | Order appears at top |
| `order_filled` | Swap executed | Progress, amounts, timing | Progress bar updates |
| `order_closed` | Order completed/canceled | Status, final amounts | Status changes |
| `orders_expired` | Batch expiration | Multiple order statuses | Orders marked as overdue_expired |
| `backfill_completed` | Bulk data updates | Multiple orders updated | Cache refresh triggered |

### Real-time Event Structure

```javascript
// order_opened event
{
  type: 'order_opened',
  source: 'realtime',
  dca_key: '5BZmCarmaNZekLhK6n5ByxD...',
  user_key: 'G54YVGAWq2J28moGvtju9q...',
  order: {
    // Complete order data with calculated metrics
    dca_key: '5BZmCarmaNZekLhK6n5ByxD...',
    user_key: 'G54YVGAWq2J28moGvtju9q...',
    order_status: 'active',
    total_fills: 0,
    expected_total_fills: 10,
    progress_percentage: 0,
    remaining_input_usd: 1000.50,
    // ... all other order fields
  },
  metadata: {
    timestamp: '2024-01-15T10:30:00Z',
    notification_time: '2024-01-15T10:30:00.123Z'
  }
}

// order_filled event (optimized - contains only updated fields)
{
  type: 'order_filled',
  source: 'realtime',
  dca_key: '5BZmCarmaNZekLhK6n5ByxD...',
  orderUpdate: {
    // Only the fields that changed - optimized for performance
    total_fills: 3,
    total_input_spent: 350000,
    total_output_received: 680905.348029,
    total_input_spent_usd: 58.22,
    total_output_received_usd: 57.68,
    remaining_input_usd: 700.25,
    average_fill_price: 0.514021,
    last_fill_at: '2024-01-15T10:30:00Z',
    next_expected_fill_at: '2024-01-15T10:31:00Z',
    is_overdue: false,
    minutes_overdue: 0,
    updated_at: '2024-01-15T10:30:00Z'
  },
  fill_metadata: {
    timestamp: '2024-01-15T10:30:00Z',
    notification_time: '2024-01-15T10:30:00.123Z'
  }
}
```

---

## Basic Integration Steps

### Step 1: Create Basic Table Component

```javascript
// components/ActiveOrdersTable.js
"use client";

import { useMemo } from 'react';
import { createColumnHelper } from '@tanstack/react-table';
import { useActiveOrdersFeed } from '@/hooks/useActiveOrdersFeed';
import StalkchainTableComponent from '@/components/tables/StalkchainTableComponent';

const columnHelper = createColumnHelper();

export default function ActiveOrdersTable({ 
  search, 
  status = 'active',
  minUsd,
  maxUsd,
  soundEnabled = false 
}) {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error
  } = useActiveOrdersFeed({
    search,
    status,
    minUsd,
    maxUsd,
    soundEnabled
  });

  const columns = useMemo(() => [
    columnHelper.accessor('user_key', {
      header: 'Wallet',
      cell: (info) => (
        <span className="font-mono text-xs">
          {info.getValue()?.slice(0, 8)}...
        </span>
      ),
    }),
    columnHelper.accessor('progress_percentage', {
      header: 'Progress',
      cell: (info) => (
        <div className="flex items-center gap-2">
          <div className="w-16 bg-gray-200 rounded-full h-2">
            <div 
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${info.getValue() || 0}%` }}
            />
          </div>
          <span className="text-xs">{info.getValue()?.toFixed(1)}%</span>
        </div>
      ),
    }),
    columnHelper.accessor('total_fills', {
      header: 'Fills',
      cell: (info) => {
        const row = info.row.original;
        return (
          <span className="text-xs">
            {info.getValue()} / {row.expected_total_fills}
          </span>
        );
      },
    }),
    columnHelper.accessor('remaining_input_usd', {
      header: 'Remaining USD',
      cell: (info) => (
        <span className="text-xs font-medium">
          ${info.getValue()?.toFixed(2) || '0.00'}
        </span>
      ),
    }),
    columnHelper.accessor('order_status', {
      header: 'Status',
      cell: (info) => {
        const status = info.getValue();
        const colors = {
          active: 'bg-blue-100 text-blue-700',
          completed: 'bg-green-100 text-green-700',
          canceled: 'bg-red-100 text-red-700',
          overdue_expired: 'bg-red-100 text-red-700',
        };
        
        return (
          <span className={`px-2 py-1 rounded-full text-xs ${colors[status]}`}>
            {status}
          </span>
        );
      },
    }),
  ], []);

  // Flatten all pages for table display
  const tableData = useMemo(() => {
    return data?.pages.flatMap(page => page.results) || [];
  }, [data]);

  if (isError) {
    return <div className="text-red-500">Error: {error?.message}</div>;
  }

  return (
    <div className="space-y-4">
      <StalkchainTableComponent
        data={tableData}
        columns={columns}
        isLoading={isLoading}
        variant="default"
        size="default"
        enableSorting={true}
        // CRITICAL FIX: Provide proper row ID to prevent React reconciliation issues
        tableOptions={{
          getRowId: (row) => row.dca_key, // Use dca_key as unique identifier
        }}
        footerContent={
          hasNextPage && (
            <div className="px-4 py-3 border-t">
              <button
                onClick={() => fetchNextPage()}
                disabled={isFetchingNextPage}
                className="text-sm text-primary hover:text-primary-600 disabled:opacity-50"
              >
                {isFetchingNextPage ? 'Loading more...' : 'Load More Orders'}
              </button>
            </div>
          )
        }
      />
    </div>
  );
}
```

### Step 2: Add Infinite Scrolling

**Important**: We already have established infinite scrolling patterns in our codebase. **Follow this exact pattern** - don't reinvent the wheel!

Our infinite scrolling uses:
1. **TanStack's `useInfiniteQuery`** (already built into `useActiveOrdersFeed`)
2. **Intersection Observer API** for automatic loading
3. **Consistent pattern** used across all feed tables

```javascript
// components/ActiveOrdersInfiniteTable.js
"use client";

import { useRef, useEffect, useCallback } from 'react';
import { Spinner } from '@heroui/spinner';
import ActiveOrdersTable from './ActiveOrdersTable';

export default function ActiveOrdersInfiniteTable(props) {
  // STEP 1: Create ref for intersection observer target
  const observerTarget = useRef(null);
  
  // STEP 2: Get infinite query functions from our hook
  const { fetchNextPage, hasNextPage, isFetchingNextPage } = useActiveOrdersFeed(props);

  // STEP 3: Handle intersection callback (EXACT pattern from our codebase)
  const handleIntersection = useCallback((entries) => {
    const target = entries[0];
    if (target.isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  // STEP 4: Set up intersection observer (EXACT pattern from our codebase)
  useEffect(() => {
    const element = observerTarget.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleIntersection, {
      threshold: 0.1, // Same threshold we use everywhere
    });
    observer.observe(element);

    return () => observer.disconnect();
  }, [handleIntersection]);

  return (
    <div>
      {/* Your table component */}
      <ActiveOrdersTable {...props} />
      
      {/* STEP 5: Intersection target (EXACT pattern from our codebase) */}
      {hasNextPage && (
        <div 
          ref={observerTarget} 
          className="flex w-full justify-center p-4 h-20"
        >
          <Spinner size="sm" />
        </div>
      )}
    </div>
  );
}
```

**Why This Pattern Works**:
✅ **Proven**: Used in `FeedTableInner.js`, `TransactionsTableInner.js`, etc.  
✅ **Automatic**: No "Load More" buttons needed  
✅ **Performance**: Only triggers when target is visible  
✅ **TanStack Integration**: Works perfectly with `useInfiniteQuery`  
✅ **Consistent UX**: Same behavior across all our feeds  

**Key Points for Junior Developers**:

1. **Don't modify the intersection logic** - it's battle-tested
2. **Use exact same threshold (0.1)** as our other components
3. **Always check `element` exists** before observing
4. **Use `observer.disconnect()`** in cleanup (not `unobserve`)
5. **The `observerTarget` div must be rendered** when `hasNextPage` is true

**Common Mistakes to Avoid**:
- ❌ Don't create custom scroll listeners
- ❌ Don't change the threshold value
- ❌ Don't forget the cleanup function
- ❌ Don't render the target div when `!hasNextPage`

**Alternative: Integration with StalkchainTableComponent**

You can also integrate infinite scrolling directly with our `StalkchainTableComponent` using the `footerContent` prop:

```javascript
// components/ActiveOrdersTable.js
import { useRef, useEffect, useCallback } from 'react';
import { Spinner } from '@heroui/spinner';
import StalkchainTableComponent from '@/components/tables/StalkchainTableComponent';
import { useActiveOrdersFeed } from '@/hooks/useActiveOrdersFeed';

export default function ActiveOrdersTable(props) {
  const observerTarget = useRef(null);
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error
  } = useActiveOrdersFeed(props);

  // Same intersection observer pattern
  const handleIntersection = useCallback((entries) => {
    const target = entries[0];
    if (target.isIntersecting && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [fetchNextPage, hasNextPage, isFetchingNextPage]);

  useEffect(() => {
    const element = observerTarget.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleIntersection, {
      threshold: 0.1,
    });
    observer.observe(element);

    return () => observer.disconnect();
  }, [handleIntersection]);

  // Your columns definition...
  const columns = useMemo(() => [
    // ... your column definitions
  ], []);

  const tableData = data?.pages.flatMap(page => page.results) || [];

  return (
    <StalkchainTableComponent
      data={tableData}
      columns={columns}
      isLoading={isLoading}
      // Add infinite scroll footer directly to the table
      footerContent={
        hasNextPage && (
          <div 
            ref={observerTarget} 
            className="flex w-full justify-center p-4 h-20"
          >
            <Spinner size="sm" />
          </div>
        )
      }
    />
  );
}
```

**Choose the approach that fits your layout**:
- Use **wrapper component** if you need extra content around the table
- Use **footerContent prop** for clean table integration
```

### Step 3: Create Card Layout for Mobile

```javascript
// components/ActiveOrderCard.js
"use client";

import { Card, CardBody } from '@heroui/card';
import { Progress } from '@heroui/progress';

export default function ActiveOrderCard({ order }) {
  const {
    dca_key,
    user_key,
    order_status,
    progress_percentage,
    total_fills,
    expected_total_fills,
    remaining_input_usd,
    in_deposited_usd,
    last_fill_at,
    next_expected_fill_at,
    is_healthy
  } = order;

  const getStatusColor = (status) => {
    const colors = {
      active: 'success',
      completed: 'primary',
      canceled: 'danger',
      overdue_expired: 'warning',
    };
    return colors[status] || 'default';
  };

  return (
    <Card radius="sm" shadow="none" className="border border-neutral-200">
      <CardBody className="p-4 space-y-3">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <div className="text-xs text-gray-500">DCA Order</div>
            <div className="font-mono text-sm">
              {dca_key.slice(0, 8)}...
            </div>
          </div>
          <div className={`px-2 py-1 rounded-full text-xs bg-${getStatusColor(order_status)}-100 text-${getStatusColor(order_status)}-800`}>
            {order_status}
          </div>
        </div>

        {/* Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span>Progress</span>
            <span>{progress_percentage?.toFixed(1)}%</span>
          </div>
          <Progress 
            value={progress_percentage || 0} 
            color={getStatusColor(order_status)}
            size="sm"
            className="transition-all duration-300"
          />
          <div className="flex justify-between text-xs text-gray-500">
            <span>Fills: {total_fills}/{expected_total_fills}</span>
            <span>Remaining: ${remaining_input_usd?.toFixed(2)}</span>
          </div>
        </div>

        {/* Wallet */}
        <div className="text-xs">
          <span className="text-gray-500">Wallet: </span>
          <span className="font-mono">{user_key?.slice(0, 12)}...</span>
        </div>

        {/* Timing */}
        {next_expected_fill_at && order_status === 'active' && (
          <div className="text-xs text-gray-500">
            Next fill expected: {new Date(next_expected_fill_at).toLocaleString()}
          </div>
        )}

        {/* Health indicator */}
        {order_status === 'active' && (
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${is_healthy ? 'bg-green-500' : 'bg-yellow-500'}`} />
            <span className="text-xs text-gray-500">
              {is_healthy ? 'Healthy' : 'Overdue'}
            </span>
          </div>
        )}
      </CardBody>
    </Card>
  );
}
```

### Step 4: Responsive Layout with Cards

```javascript
// components/ActiveOrdersFeed.js
"use client";

import { useState } from 'react';
import { useActiveOrdersFeed } from '@/hooks/useActiveOrdersFeed';
import ActiveOrdersInfiniteTable from './ActiveOrdersInfiniteTable';
import ActiveOrderCard from './ActiveOrderCard';
import { useContext } from 'react';
import { LayoutContext } from '@/context/LayoutContext';

export default function ActiveOrdersFeed({ 
  search, 
  status, 
  soundEnabled = false 
}) {
  const { isMd } = useContext(LayoutContext); // Mobile detection
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    isError,
    error
  } = useActiveOrdersFeed({
    search,
    status,
    soundEnabled
  });

  const allOrders = data?.pages.flatMap(page => page.results) || [];

  if (isError) {
    return <div className="text-red-500">Error: {error?.message}</div>;
  }

  // Desktop: Table view
  if (isMd) {
    return (
      <ActiveOrdersInfiniteTable
        search={search}
        status={status}
        soundEnabled={soundEnabled}
      />
    );
  }

  // Mobile: Card view
  return (
    <div className="space-y-3">
      {isLoading ? (
        // Skeleton cards
        Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="border rounded-lg p-4 animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-2 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        ))
      ) : (
        allOrders.map((order) => (
          <ActiveOrderCard key={order.dca_key} order={order} />
        ))
      )}

      {hasNextPage && (
        <div className="py-4 text-center">
          <button
            onClick={() => fetchNextPage()}
            disabled={isFetchingNextPage}
            className="px-4 py-2 bg-primary text-white rounded-lg disabled:opacity-50"
          >
            {isFetchingNextPage ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}
    </div>
  );
}
```

---

## Component Patterns

### Pattern 1: Real-time Progress Indicators

```javascript
// components/RealTimeProgress.js
import { useEffect, useState } from 'react';
import { Progress } from '@heroui/progress';

export default function RealTimeProgress({ 
  currentValue, 
  maxValue, 
  animated = true 
}) {
  const [displayValue, setDisplayValue] = useState(currentValue);

  // Smooth animation when value changes
  useEffect(() => {
    if (!animated) {
      setDisplayValue(currentValue);
      return;
    }

    const duration = 300; // Animation duration in ms
    const steps = 20;
    const stepValue = (currentValue - displayValue) / steps;
    let currentStep = 0;

    const animate = () => {
      if (currentStep < steps) {
        setDisplayValue(prev => prev + stepValue);
        currentStep++;
        setTimeout(animate, duration / steps);
      } else {
        setDisplayValue(currentValue);
      }
    };

    if (Math.abs(currentValue - displayValue) > 0.1) {
      animate();
    }
  }, [currentValue, displayValue, animated]);

  const percentage = (displayValue / maxValue) * 100;

  return (
    <div className="space-y-1">
      <div className="flex justify-between text-xs">
        <span>Progress</span>
        <span>{percentage.toFixed(1)}%</span>
      </div>
      <Progress 
        value={percentage} 
        color="primary"
        className="transition-all duration-300"
      />
      <div className="text-xs text-gray-500">
        {displayValue.toFixed(0)} / {maxValue} fills
      </div>
    </div>
  );
}
```

### Pattern 2: Enhanced Progress Bar with Status-Aware Display

```javascript
// components/EnhancedProgressBar.js
import { useMemo } from 'react';

export default function EnhancedProgressBar({ 
  order,
  showDetails = true 
}) {
  const { progress_percentage, order_status, total_fills, expected_total_fills } = order;
  
  const statusConfig = useMemo(() => {
    const configs = {
      ACTIVE: {
        bg: 'bg-blue-100',
        text: 'text-blue-700',
        progressBg: 'bg-blue-100',
        progressFill: 'bg-blue-600',
        label: 'Active'
      },
      COMPLETED: {
        bg: 'bg-green-100', 
        text: 'text-green-700',
        progressBg: 'bg-green-100',
        progressFill: 'bg-green-600',
        label: 'Completed'
      },
      CANCELED: {
        bg: 'bg-red-100',
        text: 'text-red-700', 
        progressBg: 'bg-red-100',
        progressFill: 'bg-red-600',
        label: 'Canceled'
      },
      OVERDUE_EXPIRED: {
        bg: 'bg-red-100',
        text: 'text-red-700',
        progressBg: 'bg-red-100', 
        progressFill: 'bg-red-600',
        label: 'Overdue'
      }
    };
    return configs[order_status?.toUpperCase()] || configs.ACTIVE;
  }, [order_status]);

  const progress = progress_percentage || 0;

  // Handle different status behaviors
  if (order_status === 'completed') {
    return (
      <div className="w-full space-y-1">
        <div className={`w-full h-2 rounded-full ${statusConfig.progressBg}`}>
          <div className={`h-2 rounded-full transition-all duration-300 ${statusConfig.progressFill}`} style={{ width: '100%' }} />
        </div>
        <span className={`text-xs font-medium ${statusConfig.text}`}>{statusConfig.label}</span>
      </div>
    );
  }

  if (order_status === 'canceled' || order_status === 'overdue_expired') {
    return (
      <div className="w-full space-y-1">
        <div className={`w-full h-2 rounded-full ${statusConfig.progressBg}`}>
          <div className={`h-2 rounded-full transition-all duration-300 ${statusConfig.progressFill}`} style={{ width: `${progress}%` }} />
        </div>
        <span className={`text-xs font-medium ${statusConfig.text}`}>{statusConfig.label}</span>
      </div>
    );
  }

  // Active orders show detailed progress
  return (
    <div className="w-full space-y-1">
      <div className={`w-full h-2 rounded-full ${statusConfig.progressBg}`}>
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${statusConfig.progressFill}`} 
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>
      {showDetails && (
        <div className="flex justify-between items-center">
          <span className="text-xs">{progress.toFixed(1)}%</span>
          <span className="text-xs text-gray-500">
            {total_fills}/{expected_total_fills}
          </span>
        </div>
      )}
    </div>
  );
}
```

### Pattern 3: Live DCA Analytics Widget

```javascript
// components/LiveDcaAnalytics.js
import { useMemo } from 'react';
import { useActiveOrdersFeed } from '@/hooks/useActiveOrdersFeed';
import { Card, CardBody } from '@heroui/card';

export default function LiveDcaAnalytics() {
  const { data } = useActiveOrdersFeed({ status: 'active' });

  const analytics = useMemo(() => {
    const allOrders = data?.pages.flatMap(page => page.results) || [];
    
    return {
      totalOrders: allOrders.length,
      totalValueUsd: allOrders.reduce((sum, order) => sum + (order.in_deposited_usd || 0), 0),
      remainingValueUsd: allOrders.reduce((sum, order) => sum + (order.remaining_input_usd || 0), 0),
      averageProgress: allOrders.reduce((sum, order) => sum + (order.progress_percentage || 0), 0) / allOrders.length,
      healthyOrders: allOrders.filter(order => order.is_healthy).length,
      overdueOrders: allOrders.filter(order => !order.is_healthy && order.order_status === 'active').length,
    };
  }, [data]);

  return (
    <Card radius="sm" shadow="none" className="border">
      <CardBody className="p-4">
        <h3 className="text-lg font-semibold mb-4">Live DCA Analytics</h3>
        
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary">
              {analytics.totalOrders}
            </div>
            <div className="text-xs text-gray-500">Active Orders</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              ${analytics.totalValueUsd.toFixed(0)}K
            </div>
            <div className="text-xs text-gray-500">Total Value</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {analytics.averageProgress.toFixed(1)}%
            </div>
            <div className="text-xs text-gray-500">Avg Progress</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-green-500">
              {analytics.healthyOrders}
            </div>
            <div className="text-xs text-gray-500">Healthy</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-500">
              {analytics.overdueOrders}
            </div>
            <div className="text-xs text-gray-500">Overdue</div>
          </div>
          
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-500">
              ${analytics.remainingValueUsd.toFixed(0)}K
            </div>
            <div className="text-xs text-gray-500">Remaining</div>
          </div>
        </div>
      </CardBody>
    </Card>
  );
}
```

### Pattern 3: Optimized Real-time Event Handling

```javascript
// hooks/useOptimizedActiveOrdersFeed.js
const handleRealtimeEvent = useCallback((message) => {
  console.log(`[ActiveOrdersFeed] Received real-time event:`, message);

  const eventType = message.type;

  switch (eventType) {
    case 'order_opened':
      // Add new order to the beginning with animation
      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData?.pages) return oldData;

        const newOrder = {
          ...message.order,
          ...calculateDerivedFields(message.order),
          isNew: true // Mark for animation
        };

        const newPages = [...oldData.pages];
        newPages[0] = {
          ...newPages[0],
          results: [newOrder, ...(newPages[0]?.results || [])],
        };

        return { ...oldData, pages: newPages };
      });

      playNotificationSound(eventType);
      break;

    case 'order_filled':
      // OPTIMIZED: Use partial update from notification
      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData?.pages) return oldData;

        const { orderUpdate } = message; // Contains only changed fields
        const updatedPages = oldData.pages.map(page => ({
          ...page,
          results: page.results.map(order => {
            if (order.dca_key === orderUpdate.dca_key) {
              // Merge updates with existing data
              const updatedOrder = {
                ...order,
                // Apply all optimized updates at once
                ...orderUpdate
              };

              // Recalculate derived fields
              return {
                ...updatedOrder,
                ...calculateDerivedFields(updatedOrder)
              };
            }
            return order;
          })
        }));

        return { ...oldData, pages: updatedPages };
      });

      playNotificationSound(eventType);
      break;

    case 'order_closed':
      // Handle completion/cancellation
      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData?.pages) return oldData;

        return {
          ...oldData,
          pages: oldData.pages.map(page => ({
            ...page,
            results: page.results.map(order => {
              if (order.dca_key === message.dca_key) {
                const updatedOrder = {
                  ...order,
                  order_status: message.close_data?.order_status || 'completed',
                  closed_at: message.metadata?.timestamp,
                  remaining_input_usd: 0,
                  next_expected_fill_at: null
                };

                return {
                  ...updatedOrder,
                  ...calculateDerivedFields(updatedOrder)
                };
              }
              return order;
            })
          }))
        };
      });

      playNotificationSound(eventType);
      break;

    case 'orders_expired':
      // Handle batch expiration
      queryClient.setQueryData(queryKey, (oldData) => {
        if (!oldData?.pages || !message.expired_dca_keys?.length) return oldData;

        return {
          ...oldData,
          pages: oldData.pages.map(page => ({
            ...page,
            results: page.results.map(order => {
              if (message.expired_dca_keys.includes(order.dca_key)) {
                const expiredOrder = {
                  ...order,
                  order_status: 'overdue_expired',
                  closed_at: message.metadata?.timestamp || new Date().toISOString(),
                  next_expected_fill_at: null,
                  remaining_input_usd: 0,
                  updated_at: new Date().toISOString()
                };

                return {
                  ...expiredOrder,
                  ...calculateDerivedFields(expiredOrder)
                };
              }
              return order;
            })
          }))
        };
      });

      playNotificationSound('orders_expired');
      break;

    case 'backfill_completed':
      // Handle bulk updates by refreshing cache
      console.log(`Backfill completed: ${message.events_processed} events, ${message.dca_keys_affected} DCAs affected`);
      
      // Invalidate cache to fetch fresh data
      queryClient.invalidateQueries({ queryKey });
      
      playNotificationSound('order_filled');
      break;

    default:
      console.log(`Unknown event type: ${eventType}`, message);
  }
}, [queryClient, queryKey, calculateDerivedFields, playNotificationSound]);
```

**Key Benefits of This Pattern**:
- ✅ **Performance**: Only updates changed fields (no full order refetch on fills)
- ✅ **Real-time**: Instant UI updates using cached data + partial updates
- ✅ **Accurate**: Uses calculated data from database notifications
- ✅ **Scalable**: Handles bulk operations efficiently
- ✅ **Consistent**: Maintains data integrity across all events

---

## Advanced Features

### Feature 1: Optimistic Order Cancellation

```javascript
// hooks/useOptimisticOrderActions.js
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useActiveOrdersFeed } from './useActiveOrdersFeed';

export function useOptimisticOrderActions() {
  const queryClient = useQueryClient();

  const cancelOrderMutation = useMutation({
    mutationFn: async (dcaKey) => {
      // API call to cancel order
      const response = await fetch(`/api/cancelDcaOrder`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ dcaKey })
      });
      if (!response.ok) throw new Error('Failed to cancel order');
      return response.json();
    },
    
    onMutate: async (dcaKey) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: ['active-orders'] });

      // Snapshot previous value
      const previousData = queryClient.getQueryData(['active-orders']);

      // Optimistically update the order status
      queryClient.setQueryData(['active-orders'], (oldData) => {
        if (!oldData?.pages) return oldData;

        return {
          ...oldData,
          pages: oldData.pages.map(page => ({
            ...page,
            results: page.results.map(order => 
              order.dca_key === dcaKey 
                ? { 
                    ...order, 
                    order_status: 'canceled',
                    remaining_input_usd: 0,
                    next_expected_fill_at: null
                  }
                : order
            )
          }))
        };
      });

      return { previousData, dcaKey };
    },

    onError: (err, dcaKey, context) => {
      // Rollback on error
      if (context?.previousData) {
        queryClient.setQueryData(['active-orders'], context.previousData);
      }
    },

    onSettled: () => {
      // Refetch to ensure consistency
      queryClient.invalidateQueries({ queryKey: ['active-orders'] });
    },
  });

  return {
    cancelOrder: cancelOrderMutation.mutate,
    isCanceling: cancelOrderMutation.isPending,
  };
}
```

### Feature 2: Real-time Filtering with Search

```javascript
// components/ActiveOrdersSearch.js
import { useState, useCallback, useMemo } from 'react';
import { Input } from '@heroui/input';
import { Select, SelectItem } from '@heroui/select';
import { Switch } from '@heroui/switch';
import { useDebounce } from '@/hooks/useDebounce';

export default function ActiveOrdersSearch({ 
  onFiltersChange,
  soundEnabled,
  onSoundToggle 
}) {
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('active');
  const [minUsd, setMinUsd] = useState('');
  const [maxUsd, setMaxUsd] = useState('');

  // Debounce search to avoid excessive API calls
  const debouncedSearch = useDebounce(search, 300);

  // Notify parent when filters change
  const filters = useMemo(() => ({
    search: debouncedSearch,
    status,
    minUsd,
    maxUsd
  }), [debouncedSearch, status, minUsd, maxUsd]);

  const handleFiltersChange = useCallback(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  useEffect(() => {
    handleFiltersChange();
  }, [handleFiltersChange]);

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
      <div className="flex flex-col md:flex-row gap-4">
        {/* Search */}
        <div className="flex-1">
          <Input
            label="Search Orders"
            placeholder="Wallet address, token address, or DCA key"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            size="sm"
          />
        </div>

        {/* Status Filter */}
        <div className="w-full md:w-48">
          <Select
            label="Status"
            value={status}
            onChange={(e) => setStatus(e.target.value)}
            size="sm"
          >
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="completed">Completed</SelectItem>
            <SelectItem value="canceled">Canceled</SelectItem>
            <SelectItem value="overdue_expired">Overdue/Expired</SelectItem>
          </Select>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        {/* USD Amount Filters */}
        <div className="flex gap-2 flex-1">
          <Input
            label="Min USD"
            placeholder="100"
            value={minUsd}
            onChange={(e) => setMinUsd(e.target.value)}
            size="sm"
            type="number"
          />
          <Input
            label="Max USD"
            placeholder="10000"
            value={maxUsd}
            onChange={(e) => setMaxUsd(e.target.value)}
            size="sm"
            type="number"
          />
        </div>

        {/* Sound Toggle */}
        <div className="flex items-center gap-2">
          <Switch
            isSelected={soundEnabled}
            onValueChange={onSoundToggle}
            size="sm"
          />
          <span className="text-sm">Sound Notifications</span>
        </div>
      </div>
    </div>
  );
}
```

### Feature 3: Complete Dashboard Integration

```javascript
// pages/active-orders-dashboard.js
"use client";

import { useState } from 'react';
import ActiveOrdersSearch from '@/components/ActiveOrdersSearch';
import ActiveOrdersFeed from '@/components/ActiveOrdersFeed';
import LiveDcaAnalytics from '@/components/LiveDcaAnalytics';

export default function ActiveOrdersDashboard() {
  const [filters, setFilters] = useState({
    search: '',
    status: 'active',
    minUsd: '',
    maxUsd: ''
  });
  const [soundEnabled, setSoundEnabled] = useState(false);

  return (
    <div className="max-w-screen-xl mx-auto px-4 py-8 space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold">Active DCA Orders</h1>
        <p className="text-gray-600">Real-time Jupiter DCA order tracking</p>
      </div>

      {/* Analytics Widget */}
      <LiveDcaAnalytics />

      {/* Search and Filters */}
      <ActiveOrdersSearch
        onFiltersChange={setFilters}
        soundEnabled={soundEnabled}
        onSoundToggle={setSoundEnabled}
      />

      {/* Orders Feed */}
      <ActiveOrdersFeed
        search={filters.search}
        status={filters.status}
        minUsd={filters.minUsd}
        maxUsd={filters.maxUsd}
        soundEnabled={soundEnabled}
      />
    </div>
  );
}
```

---

## Performance Best Practices

### 1. Memoize Column Definitions

```javascript
// ✅ Good - Memoized columns
const columns = useMemo(() => [
  columnHelper.accessor('progress_percentage', {
    header: 'Progress',
    cell: (info) => <ProgressCell value={info.getValue()} />,
  }),
  // ... other columns
], []);

// ❌ Bad - Recreated on every render
const columns = [
  columnHelper.accessor('progress_percentage', {
    header: 'Progress',
    cell: (info) => <ProgressCell value={info.getValue()} />,
  }),
];
```

### 2. Optimize Real-time Updates

```javascript
// ✅ Good - Only update relevant data
const handleRealtimeEvent = useCallback((eventData) => {
  const { type, dca_key, order } = eventData;

  // Skip if order doesn't match current filters
  if (!matchesCurrentFilters(order)) {
    return;
  }

  // Only play sound for matching events
  playNotificationSound(type);

  // Update cache efficiently
  queryClient.setQueryData(queryKey, (oldData) => {
    // ... efficient cache update
  });
}, [queryKey, queryClient, filters]);
```

### 3. Debounce Search Inputs

```javascript
// Custom debounce hook
function useDebounce(value, delay) {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
```

---

## Troubleshooting

### Issue 1: Excessive API Queries on Real-time Events

**Symptoms**: API queries execute constantly even when the page is already loaded and you're receiving real-time socket updates.

**Root Cause**: The socket handler was using `refetchQueries()` for all real-time events, even though the socket already contains all the updated data we need. This caused unnecessary API calls and database queries.

**Solution**: Use socket data directly to update React Query cache instead of refetching:

```javascript
// ❌ WRONG - This causes unnecessary API calls
case 'order_filled':
  console.log(`Order filled: ${message.orderUpdate?.dca_key} - refetching current query`);
  // This triggers a full API refetch even though socket has all the data
  queryClient.refetchQueries({ queryKey });
  break;

// ✅ CORRECT - Use socket data directly
case 'order_filled':
  console.log(`Order filled: ${message.orderUpdate?.dca_key} - updating cache with socket data`);
  const { orderUpdate } = message;
  
  queryClient.setQueryData(queryKey, (oldData) => {
    if (!oldData?.pages) return oldData;

    const updatedPages = oldData.pages.map(page => ({
      ...page,
      results: page.results.map(order => {
        if (order.dca_key === orderUpdate.dca_key) {
          // Use socket data directly - no API call needed
          return {
            ...order,
            total_fills: orderUpdate.total_fills,
            total_input_spent: orderUpdate.total_input_spent,
            remaining_input_usd: orderUpdate.remaining_input_usd,
            // ... all other updates from socket
          };
        }
        return order;
      })
    }));

    return { ...oldData, pages: updatedPages };
  });
  break;
```

**Critical Filter Handling**:
```javascript
// ✅ CORRECT - Only add orders that match current filters
case 'order_opened':
  if (matchesFilters(message.order)) {
    queryClient.setQueryData(queryKey, (oldData) => {
      // ... add to cache only if it matches current filters
    });
  }
  break;

// ✅ CORRECT - Remove orders that no longer match filters
case 'order_closed':
  queryClient.setQueryData(queryKey, (oldData) => {
    return {
      ...oldData,
      pages: oldData.pages.map(page => ({
        ...page,
        results: page.results.map(order => {
          if (order.dca_key === message.dca_key) {
            const updatedOrder = { ...order, order_status: 'completed' };
            
            // Remove from current view if it no longer matches filters
            if (!matchesFilters(updatedOrder)) {
              return null;
            }
            
            return updatedOrder;
          }
          return order;
        }).filter(Boolean) // Remove null entries
      }))
    };
  });
  break;
```

**Why This Works**:
- Socket notifications contain **all the updated data** we need
- Direct cache updates are instant and accurate
- No unnecessary API calls or database queries
- Filter matching ensures only relevant orders are shown/updated
- Orders automatically appear/disappear based on current filter settings

**Performance Impact**:
- **Before**: Every event → API query → Database load → Network delay
- **After**: Socket event → Direct cache update → Instant UI update
- **Result**: 100% elimination of unnecessary API calls for real-time events

### Issue 2: Token Display Interference (SolanaToken Components)

**Symptoms**: SolanaToken components show incorrect ticker names and market caps, even though icons are correct. This happens specifically in live feed tables with React Query and TanStack Table.

**Root Cause**: React's reconciliation algorithm can't properly track which component belongs to which table row when data updates in real-time, causing component state to be reused incorrectly.

**Solution**: Provide unique, stable row identifiers to TanStack Table:

```javascript
// ✅ CORRECT - Add getRowId to table configuration
<StalkchainTableComponent
  data={tableData}
  columns={columns}
  isLoading={isLoading}
  tableOptions={{
    getRowId: (row) => row.dca_key, // Use stable unique identifier
  }}
  // ... other props
/>

// ✅ ALSO ADD - Unique keys to SolanaToken components in cells
columnHelper.accessor('input_mint', {
  header: 'Selling', 
  cell: (info) => {
    const row = info.row.original;
    const tokenAddress = info.getValue();
    return (
      <SolanaToken 
        key={`${row.dca_key}-input-${tokenAddress}`} // Unique key per row
        address={tokenAddress} 
        size="xs"
      />
    );
  },
}),
```

**Why This Works**:
- TanStack Table uses `row.dca_key` instead of array indices to track rows
- React doesn't mix up component instances when data updates
- Each SolanaToken gets a unique key tied to its specific row and token
- Real-time updates preserve component identity correctly

**Alternative Fix for Direct TanStack Table Usage**:
```javascript
const table = useReactTable({
  data: tableData,
  columns,
  getRowId: (row) => row.dca_key, // Add this line
  getCoreRowModel: getCoreRowModel(),
  // ... other options
});
```

### Issue 2: Real-time Updates Not Working

**Symptoms**: Orders don't update when fills occur

**Debugging Steps**:
1. Check Socket.IO connection:
   ```javascript
   const { isConnected } = useStream();
   console.log('Socket connected:', isConnected);
   ```

2. Verify event subscription:
   ```javascript
   useEffect(() => {
     if (!isConnected) return;
     
     const unsubscribe = subscribeToEvent("active_orders_feed", (data) => {
       console.log('Received event:', data); // Add this log
       handleRealtimeEvent(data);
     });
     
     return unsubscribe;
   }, [isConnected, subscribeToEvent, handleRealtimeEvent]);
   ```

3. Check filter matching:
   ```javascript
   const matchesFilters = useCallback((orderData) => {
     console.log('Checking filters for order:', orderData.dca_key);
     // ... filter logic
   }, [filters]);
   ```

### Issue 3: Progress Bars Not Animating

**Symptoms**: Progress updates instantly without smooth animation

**Solution**: Use CSS transitions and state management:
```javascript
// Add transition classes
<div 
  className="bg-primary h-2 rounded-full transition-all duration-500 ease-out"
  style={{ width: `${progress}%` }}
/>

// Or use a smooth animation hook
const useAnimatedValue = (targetValue, duration = 300) => {
  const [value, setValue] = useState(targetValue);
  
  useEffect(() => {
    const startValue = value;
    const startTime = Date.now();
    
    const animate = () => {
      const now = Date.now();
      const elapsed = now - startTime;
      const progress = Math.min(elapsed / duration, 1);
      
      const currentValue = startValue + (targetValue - startValue) * progress;
      setValue(currentValue);
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }, [targetValue, duration]);
  
  return value;
};
```

### Issue 4: Memory Leaks with Sound Files

**Symptoms**: Audio files consume increasing memory

**Solution**: Properly manage audio resources:
```javascript
const notificationSounds = useRef({
  order_opened: null,
  order_filled: null,
  order_closed: null,
});

useEffect(() => {
  // Initialize sounds
  notificationSounds.current = {
    order_opened: new Audio("/sounds/alert-1.mp3"),
    order_filled: new Audio("/sounds/alert-2.mp3"),
    order_closed: new Audio("/sounds/alert-3.mp3"),
  };

  // Preload
  Object.values(notificationSounds.current).forEach((audio) => {
    audio.preload = "auto";
  });

  // Cleanup on unmount
  return () => {
    Object.values(notificationSounds.current).forEach((audio) => {
      audio.pause();
      audio.src = '';
    });
  };
}, []);
```

### Issue 5: Infinite Scroll Not Working

**Symptoms**: "Load More" button doesn't appear or doesn't trigger

**Debug Steps**:
1. Check intersection observer setup:
   ```javascript
   useEffect(() => {
     console.log('Setting up intersection observer');
     const observer = new IntersectionObserver(handleIntersection, {
       threshold: 0.1,
       rootMargin: '50px' // Add some margin
     });
     
     // ... rest of setup
   }, [handleIntersection]);
   ```

2. Verify pagination state:
   ```javascript
   console.log({
     hasNextPage,
     isFetchingNextPage,
     pagesLength: data?.pages?.length,
     lastPageResults: data?.pages?.[data.pages.length - 1]?.results?.length
   });
   ```

---

## Summary

This recipe provides everything needed to build real-time Active Orders components that integrate seamlessly with our optimized backend system:

✅ **Working Building Blocks**: Hook, API, Socket.IO integration with optimized notifications  
✅ **Real-time Updates**: Efficient partial updates for fills, complete status management  
✅ **Enhanced Status Handling**: Supports `active`, `completed`, `canceled`, `overdue_expired` states  
✅ **Optimized Performance**: No full order refetch on fills - uses calculated notification data  
✅ **Responsive Design**: Table for desktop, cards for mobile with status-aware progress bars  
✅ **Production Ready**: Error handling, loading states, backfill protection, auto-completion  

## Critical Fix for Token Display Issues

**IMPORTANT**: If you're experiencing token display interference (where SolanaToken components show wrong ticker/market cap data), you MUST add the `getRowId` configuration to your table:

```javascript
// REQUIRED for all live feed tables using SolanaToken components
<StalkchainTableComponent
  data={tableData}
  columns={columns}
  tableOptions={{
    getRowId: (row) => row.dca_key, // Critical for React reconciliation
  }}
  // ... other props
/>
```

This prevents React from mixing up component instances during real-time data updates.

## Key System Features

**Optimized Real-time Architecture**:
- `order_filled` events contain pre-calculated updates (no database queries needed)
- Batch `orders_expired` handling for efficient overdue management
- `backfill_completed` events trigger cache refresh for bulk updates
- Sorting by `opened_at` (transaction time) for chronological order

**Enhanced Status Management**:
- Auto-completion when remaining USD ≤ $0.01
- Overdue detection and expiration handling
- Status-aware progress bars with different behaviors per status
- Consistent "canceled" (single L) spelling throughout

**Performance Optimizations**:
- Partial cache updates for fills (only changed fields)
- Efficient batch processing for expired orders
- Cursor-based pagination using `opened_at` timestamps
- Minimal notification payloads to reduce network overhead

**Developer Experience**:
- Clear separation between real-time and historical events
- Comprehensive error handling and edge case management
- Easy customization while maintaining performance
- Battle-tested patterns used across all feed components

Simply follow the patterns shown, use the provided building blocks, and you'll have a production-ready real-time DCA orders interface that scales efficiently and provides excellent user experience.