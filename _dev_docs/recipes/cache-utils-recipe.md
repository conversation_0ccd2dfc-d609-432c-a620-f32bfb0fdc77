# Cache Utils Recipe & Rules

## Overview

This recipe provides strict guidelines for creating consistent, performant, and reliable caching utilities. All cache utilities should follow these patterns to ensure consistency across the codebase.

### Core Principles

1. **Smart Data Sources**: Use PostgreSQL as source of truth when needed, but not all caches require database backing
2. **Atomic Operations**: Use Redis atomic operations to prevent race conditions
3. **Graceful Degradation**: Cache failures should not break application functionality
4. **Smart TTL**: Use appropriate TTL based on data characteristics (resetting vs non-resetting)
5. **Smart Sync**: Only sync when cache is empty or after cooldown periods with zero-downtime
6. **Consistent Patterns**: Follow established patterns for predictable behavior

## TTL Patterns & Data Sources

### TTL Behavior Types

#### Non-Resetting TTL
- **Commands**: `SETEX`, `SET key value EX seconds`
- **Behavior**: Sets TTL when creating key, doesn't reset TTL if key already exists
- **Use for**: Timeout flags, invalid tokens, temporary blocks
- **Example**: Token timeout cache - once set, expires naturally

#### Resetting TTL  
- **Commands**: `EXPIRE key seconds` on existing keys
- **Behavior**: Resets TTL every time it's called
- **Use for**: Session data, frequently accessed cache, "keep alive" patterns
- **Example**: API response cache that extends on access

#### Smart TTL
- **Commands**: Dynamic TTL based on data characteristics
- **Behavior**: Different TTL values based on data properties
- **Use for**: Variable-frequency data like token holders
- **Example**: Small tokens (30s), large tokens (15min)

### When to Use PostgreSQL

#### PostgreSQL Required
- **Set-based caches**: Spam tokens, CEX wallets, KOL profiles
- **Reference data**: Data that needs to be authoritative and consistent
- **Audit trails**: Data that requires persistence and history

#### PostgreSQL Optional
- **API responses**: External data that can be refetched
- **Computed data**: Data that can be recalculated
- **Session data**: Temporary user state

#### PostgreSQL Not Needed
- **Timeout flags**: Temporary state that expires naturally
- **Rate limiting**: Temporary counters
- **Invalid tokens**: Temporary blocks that can be rediscovered

## Redis Data Structures & When to Use

### 1. Redis Sets (SADD, SREM, SISMEMBER)
**Use for**: Existence checks, unique collections, fast membership testing
**Examples**: Spam tokens, CEX wallets, KOL profiles

```javascript
// Fast existence check - O(1) complexity
const isSpam = await redis.sismember('stalkchain:spam:tokens', tokenAddress);

// Bulk existence checks - single operation
const results = await redis.smismember('stalkchain:spam:tokens', tokenAddresses);

// Add multiple items atomically
await redis.sadd('stalkchain:spam:tokens', ...tokenAddresses);
```

### 2. Redis Strings (SET, GET, SETEX)
**Use for**: Simple key-value pairs, temporary flags, counters, single cache keys
**Examples**: Timeout tokens, invalid tokens, rate limiting, API responses

```javascript
// Set with expiration (non-resetting TTL)
await redis.setex('timeout:token:ABC123', 7200, '1');

// Set with expiration using SET command (non-resetting TTL)
await redis.set('key', 'value', 'EX', 3600);

// Check existence
const exists = await redis.exists('timeout:token:ABC123');

// Set expiration on existing key (resetting TTL)
await redis.expire('existing:key', 3600);
```

### 3. Redis Hashes (HSET, HGET, HMGET)
**Use for**: Related fields, object-like data, partial updates
**Examples**: User sessions, token metadata

```javascript
// Set multiple fields
await redis.hmset('user:123', {
  name: 'John',
  email: '<EMAIL>',
  lastLogin: Date.now()
});
```

### 4. JSON Strings with Metadata
**Use for**: Complex objects, versioned data, audit trails
**Examples**: Token holders, wallet data, KOL profiles

```javascript
const data = {
  ...actualData,
  cachedAt: Date.now(),
  cacheTier: 'RAPID',
  version: '1.0'
};
await redis.setex(key, ttl, JSON.stringify(data));
```

## Redis Commands Reference

### Set Operations (Atomic)
```javascript
// Add members (returns count of new members added)
const newCount = await redis.sadd('myset', 'member1', 'member2');

// Remove members (returns count of members removed)
const removedCount = await redis.srem('myset', 'member1');

// Check membership (returns 1 if exists, 0 if not)
const isMember = await redis.sismember('myset', 'member1');

// Check multiple memberships (returns array of 1s and 0s)
const results = await redis.smismember('myset', ['member1', 'member2']);

// Get all members
const allMembers = await redis.smembers('myset');

// Get member count
const count = await redis.scard('myset');
```

### String Operations
```javascript
// Set with expiration (atomic, non-resetting TTL)
await redis.setex('key', 3600, 'value');

// Alternative syntax (atomic, non-resetting TTL)
await redis.set('key', 'value', 'EX', 3600);

// Set only if not exists (atomic)
const wasSet = await redis.setnx('key', 'value');

// Set multiple keys (atomic)
await redis.mset('key1', 'value1', 'key2', 'value2');

// Get multiple keys
const values = await redis.mget('key1', 'key2');

// Check existence
const exists = await redis.exists('key');

// Set expiration on existing key (resetting TTL)
await redis.expire('key', 3600);

// TTL Behavior:
// - SETEX/SET with EX: Creates key with TTL, doesn't reset if key exists
// - EXPIRE: Resets TTL on existing key
```

### Pipeline Operations (Batch, Non-Atomic)
```javascript
const pipeline = redis.pipeline();
pipeline.sadd('set1', 'member1');
pipeline.setex('key1', 3600, 'value1');
pipeline.exists('key2');

// Execute all commands in batch
const results = await pipeline.exec();
// results is array of [error, result] pairs
```

### Transaction Operations (Atomic)
```javascript
// Atomic transaction
const results = await redis.multi()
  .sadd('set1', 'member1')
  .setex('key1', 3600, 'value1')
  .expire('set1', 7200)
  .exec();
```

### Atomic Swap Pattern (Zero Downtime)
```javascript
// Create temporary key
const tempKey = `${originalKey}:temp:${Date.now()}`;

// Populate temporary key
const pipeline = redis.pipeline();
data.forEach(item => pipeline.sadd(tempKey, item));
await pipeline.exec();

// Atomic swap
await redis.multi()
  .rename(tempKey, originalKey)
  .expire(originalKey, ttl)
  .exec();
```

### Smart Sync Pattern (Cache-First with Background Refresh)
```javascript
// Main function that implements smart sync
export async function getDataWithSmartSync() {
  try {
    // 1. Try cache first
    let cacheResult = await getAllFromCache();
    
    // 2. If cache empty, sync immediately (blocking)
    if (!cacheResult.success || cacheResult.count === 0) {
      console.log('Cache is empty, syncing from PostgreSQL...');
      const syncResult = await syncFromDatabase();
      if (syncResult.success) {
        cacheResult = await getAllFromCache();
      }
    } else {
      // 3. Cache has data, trigger background sync if cooldown passed
      triggerBackgroundSync();
    }
    
    return cacheResult;
  } catch (error) {
    console.error('Error in smart sync:', error);
    return { success: false, data: [], error: error.message };
  }
}

// Background sync with cooldown to prevent excessive syncing
async function triggerBackgroundSync() {
  const SYNC_COOLDOWN_KEY = 'entity:last_sync';
  const COOLDOWN_SECONDS = 60 * 60; // 1 hour
  
  try {
    const lastSync = await redis.get(SYNC_COOLDOWN_KEY);
    const now = Math.floor(Date.now() / 1000);
    
    if (!lastSync || (now - parseInt(lastSync)) > COOLDOWN_SECONDS) {
      // Set timestamp immediately to prevent multiple syncs
      await redis.set(SYNC_COOLDOWN_KEY, now.toString(), 'EX', COOLDOWN_SECONDS * 2);
      
      // Non-blocking background sync
      setImmediate(async () => {
        try {
          const syncResult = await syncFromDatabase();
          console.log(`Background sync completed: ${syncResult.itemsAdded} items`);
        } catch (error) {
          console.error('Background sync error:', error);
        }
      });
    }
  } catch (error) {
    console.error('Error checking sync cooldown:', error);
  }
}
```

## Cache Utility Patterns

### Pattern 1: Single Cache Keys (Non-Resetting TTL)

**Use for**: Timeout flags, invalid tokens, rate limiting, temporary state
**PostgreSQL**: Not required - pure cache pattern

```javascript
// File: utils/cache/timeoutTokens.js
import redis from '@/utils/redis.server';

export const TIMEOUT_TOKEN_CACHE_TTL = 2 * 60 * 60; // 2 hours
export const TIMEOUT_TOKEN_KEY_PREFIX = 'stalkchain:timeout:token:';

export async function addTimeoutToken(tokenAddress) {
  if (!tokenAddress || !redis) return false;
  
  try {
    const key = `${TIMEOUT_TOKEN_KEY_PREFIX}${tokenAddress.trim()}`;
    await redis.setex(key, TIMEOUT_TOKEN_CACHE_TTL, '1');
    return true;
  } catch (error) {
    console.error('Failed to add timeout token:', error);
    return false;
  }
}

export async function isTimeoutToken(tokenAddress) {
  if (!tokenAddress || !redis) return false;
  
  try {
    const key = `${TIMEOUT_TOKEN_KEY_PREFIX}${tokenAddress.trim()}`;
    const exists = await redis.exists(key);
    return exists === 1;
  } catch (error) {
    console.error('Error checking timeout token:', error);
    return false;
  }
}
```

### Pattern 2: Single Cache Keys (Resetting TTL)

**Use for**: API responses, session data, frequently accessed data
**PostgreSQL**: Optional - depends on data source

```javascript
// File: utils/cache/apiResponses.js
import redis from '@/utils/redis.server';

export const API_RESPONSE_CACHE_TTL = 5 * 60; // 5 minutes
export const API_RESPONSE_KEY_PREFIX = 'stalkchain:api:response:';

export async function getCachedApiResponse(endpoint, params = {}) {
  if (!endpoint || !redis) return null;
  
  try {
    const key = `${API_RESPONSE_KEY_PREFIX}${endpoint}:${JSON.stringify(params)}`;
    const cached = await redis.get(key);
    return cached ? JSON.parse(cached) : null;
  } catch (error) {
    console.error('Error getting cached API response:', error);
    return null;
  }
}

export async function setCachedApiResponse(endpoint, params = {}, data) {
  if (!endpoint || !data || !redis) return false;
  
  try {
    const key = `${API_RESPONSE_KEY_PREFIX}${endpoint}:${JSON.stringify(params)}`;
    await redis.setex(key, API_RESPONSE_CACHE_TTL, JSON.stringify(data));
    return true;
  } catch (error) {
    console.error('Error setting cached API response:', error);
    return false;
  }
}

// Extend TTL on access (resetting TTL pattern)
export async function extendCacheExpiry(endpoint, params = {}) {
  if (!endpoint || !redis) return false;
  
  try {
    const key = `${API_RESPONSE_KEY_PREFIX}${endpoint}:${JSON.stringify(params)}`;
    await redis.expire(key, API_RESPONSE_CACHE_TTL); // Resets TTL
    return true;
  } catch (error) {
    console.error('Error extending cache expiry:', error);
    return false;
  }
}
```

### Pattern 3: Set-Based Cache with Smart Sync

**Use for**: Spam tokens, CEX wallets, KOL profiles
**PostgreSQL**: Required - uses database as source of truth

```javascript
// File: utils/cache/spamTokens.js
import { query } from '@/utils/postgres';
import redis from '@/utils/redis.server';

export const SPAM_TOKEN_CACHE_TTL = 60 * 60 * 24 * 7; // 7 days
export const SPAM_TOKENS_KEY = 'stalkchain:spam:tokens';

export async function checkIsSpamToken(mintAddress) {
  if (!mintAddress) throw new Error('mintAddress is required');
  
  try {
    const isSpam = await redis.sismember(SPAM_TOKENS_KEY, mintAddress.trim());
    return isSpam === 1;
  } catch (error) {
    console.error('Error checking spam token:', error);
    return false; // Fail safe - don't block on cache errors
  }
}

export async function checkIsSpamTokens(mintAddresses) {
  if (!mintAddresses?.length) throw new Error('mintAddresses required');
  
  const sanitized = mintAddresses.map(addr => addr.trim());
  
  try {
    const results = await redis.smismember(SPAM_TOKENS_KEY, sanitized);
    return sanitized.reduce((acc, address, index) => {
      acc[address] = results[index] === 1;
      return acc;
    }, {});
  } catch (error) {
    console.error('Error checking spam tokens:', error);
    return sanitized.reduce((acc, address) => {
      acc[address] = false;
      return acc;
    }, {});
  }
}

// Smart sync with cooldown (used in actions)
export async function getSpamTokensWithSmartSync() {
  try {
    // Try to get from cache first
    let cacheResult = await getAllSpamTokens();
    
    // If cache is empty or failed, sync from PostgreSQL (blocking)
    if (!cacheResult.success || cacheResult.count === 0) {
      console.log('Spam tokens cache is empty, syncing from PostgreSQL...');
      
      const syncResult = await syncSpamTokens();
      if (!syncResult.success) {
        console.error('Failed to sync spam tokens:', syncResult.error);
        return { success: false, tokens: [], error: syncResult.error };
      }
      
      console.log(`Successfully synced ${syncResult.tokensAdded} spam tokens to cache`);
      cacheResult = await getAllSpamTokens();
    } else {
      // Cache has data, trigger background sync if cooldown has passed
      triggerBackgroundSync();
    }
    
    return cacheResult;
  } catch (error) {
    console.error('Error getting spam tokens:', error);
    return { success: false, tokens: [], error: error.message };
  }
}

// Background sync with 1-hour cooldown
async function triggerBackgroundSync() {
  try {
    const SYNC_COOLDOWN_KEY = 'stalkchain:spam:tokens:last_sync';
    const COOLDOWN_SECONDS = 60 * 60; // 1 hour
    
    const lastSync = await redis.get(SYNC_COOLDOWN_KEY);
    const now = Math.floor(Date.now() / 1000);
    
    if (!lastSync || (now - parseInt(lastSync)) > COOLDOWN_SECONDS) {
      // Set timestamp immediately to prevent multiple syncs
      await redis.set(SYNC_COOLDOWN_KEY, now.toString(), 'EX', COOLDOWN_SECONDS * 2);
      
      // Trigger sync in background (non-blocking)
      setImmediate(async () => {
        try {
          console.log('Starting background spam tokens sync...');
          const syncResult = await syncSpamTokens();
          if (syncResult.success) {
            console.log(`Background sync completed: ${syncResult.tokensAdded} tokens synced`);
          } else {
            console.error('Background sync failed:', syncResult.error);
          }
        } catch (error) {
          console.error('Background sync error:', error);
        }
      });
    }
  } catch (error) {
    console.error('Error checking sync cooldown:', error);
  }
}

export async function syncSpamTokens() {
  const result = { success: false, tokensAdded: 0, error: null };

  try {
    // 1. Fetch from PostgreSQL (source of truth)
    const { rows } = await query('SELECT mint_address FROM list_tokens_spam');
    
    if (rows.length === 0) {
      result.success = true;
      return result;
    }
    
    const mintAddresses = rows.map(row => row.mint_address);
    
    // 2. Create temporary key for atomic swap
    const tempKey = `${SPAM_TOKENS_KEY}:temp:${Date.now()}`;
    
    // 3. Populate temporary set using pipeline
    const pipeline = redis.pipeline();
    mintAddresses.forEach(address => pipeline.sadd(tempKey, address));
    await pipeline.exec();
    
    // 4. Atomic swap (zero downtime)
    await redis.multi()
      .rename(tempKey, SPAM_TOKENS_KEY)
      .expire(SPAM_TOKENS_KEY, SPAM_TOKEN_CACHE_TTL)
      .exec();
    
    result.success = true;
    result.tokensAdded = mintAddresses.length;
  } catch (error) {
    console.error('Error syncing spam tokens:', error);
    result.error = error.message;
  }
  
  return result;
}
```

### Pattern 4: Smart TTL Cache (Dynamic Expiration)

**Use for**: Token holders, wallet data, variable-frequency data
**PostgreSQL**: Optional - depends on data source

```javascript
// File: utils/cache/tokenHolders.js
import redis from '@/utils/redis.server';
import DOMPurify from 'isomorphic-dompurify';

export const TOKEN_HOLDERS_KEY_PREFIX = 'stalkchain:token:holders:';

// Smart TTL based on data characteristics
export const CACHE_TIERS = {
  NO_CACHE: { threshold: 1000, ttl: 0 },
  RAPID: { threshold: 5000, ttl: 30 },
  MODERATE: { threshold: 10000, ttl: 60 },
  STABLE: { threshold: 50000, ttl: 300 },
  ESTABLISHED: { threshold: 100000, ttl: 600 },
  MASSIVE: { threshold: Infinity, ttl: 900 }
};

export function getCacheTierByHolderCount(holderCount) {
  for (const tier of Object.values(CACHE_TIERS)) {
    if (holderCount < tier.threshold) return tier;
  }
  return CACHE_TIERS.MASSIVE;
}

export async function getCachedTokenHolders(tokenAddress) {
  if (!tokenAddress) throw new Error('tokenAddress is required');

  const sanitized = DOMPurify.sanitize(tokenAddress.trim());
  const key = `${TOKEN_HOLDERS_KEY_PREFIX}${sanitized}`;

  try {
    const cached = await redis.get(key);
    if (cached) {
      const data = JSON.parse(cached);
      const tier = getCacheTierByHolderCount(data.holdersCount);
      console.log(`🎯 Cache HIT: ${sanitized} (${data.holdersCount} holders, ${tier.description})`);
      return data;
    }
    console.log(`🎯 Cache MISS: ${sanitized}`);
    return null;
  } catch (error) {
    console.error('Error getting cached token holders:', error);
    return null;
  }
}

export async function setCachedTokenHolders(tokenAddress, holdersData) {
  if (!tokenAddress || !holdersData) throw new Error('tokenAddress and holdersData required');

  const sanitized = DOMPurify.sanitize(tokenAddress.trim());
  const key = `${TOKEN_HOLDERS_KEY_PREFIX}${sanitized}`;
  
  const holderCount = holdersData.holdersCount || 0;
  const tier = getCacheTierByHolderCount(holderCount);

  // Don't cache if tier says no cache
  if (tier.ttl === 0) {
    console.log(`🎯 NOT cached: ${sanitized} (${holderCount} holders, ${tier.description})`);
    return false;
  }

  try {
    const dataWithMeta = {
      ...holdersData,
      cachedAt: Date.now(),
      cacheTier: tier.description
    };
    
    await redis.setex(key, tier.ttl, JSON.stringify(dataWithMeta));
    console.log(`🎯 Cached: ${sanitized} (${holderCount} holders, TTL: ${tier.ttl}s)`);
    return true;
  } catch (error) {
    console.error('Error setting cached token holders:', error);
    return false;
  }
}
```

### Pattern 5: Dual Storage with Smart Sync (Set + Individual Records)

**Use for**: CEX wallets, KOL profiles with detailed data
**PostgreSQL**: Required - uses database as source of truth

```javascript
// File: utils/cache/cexWallets.js
import { query } from '@/utils/postgres';
import redis from '@/utils/redis.server';

export const CEX_WALLETS_CACHE_TTL = 60 * 60 * 24 * 7; // 7 days
export const CEX_WALLETS_KEY = 'stalkchain:cex:wallets';

export async function checkIsCexWallet(walletAddress) {
  if (!walletAddress) throw new Error('walletAddress is required');
  
  try {
    const isCex = await redis.sismember(CEX_WALLETS_KEY, walletAddress.trim());
    return isCex === 1;
  } catch (error) {
    console.error('Error checking CEX wallet:', error);
    return false;
  }
}

export async function getCexWallet(walletAddress) {
  if (!walletAddress) throw new Error('walletAddress is required');
  
  try {
    const key = `${CEX_WALLETS_KEY}:${walletAddress.trim()}`;
    const data = await redis.get(key);
    return data ? JSON.parse(data) : null;
  } catch (error) {
    console.error('Error getting CEX wallet:', error);
    return null;
  }
}

export async function syncCexWallets() {
  const result = { success: false, walletsAdded: 0, error: null };

  try {
    const { rows } = await query('SELECT address, cex FROM wallets_cex');
    
    if (rows.length === 0) {
      result.success = true;
      return result;
    }
    
    // Use pipeline for batch operations
    const pipeline = redis.pipeline();
    
    rows.forEach(wallet => {
      const walletData = {
        address: wallet.address,
        name: wallet.cex,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      
      // Store detailed data
      const walletKey = `${CEX_WALLETS_KEY}:${wallet.address}`;
      pipeline.setex(walletKey, CEX_WALLETS_CACHE_TTL, JSON.stringify(walletData));
      
      // Add to set for fast existence checks
      pipeline.sadd(CEX_WALLETS_KEY, wallet.address);
    });
    
    // Set expiration for the main set
    pipeline.expire(CEX_WALLETS_KEY, CEX_WALLETS_CACHE_TTL);
    
    await pipeline.exec();
    
    result.success = true;
    result.walletsAdded = rows.length;
  } catch (error) {
    console.error('Error syncing CEX wallets:', error);
    result.error = error.message;
  }
  
  return result;
}
```

## File Structure

All cache utilities must be placed in `utils/cache/` directory:

```
utils/cache/
├── spamTokens.js           # Set-based cache with smart sync
├── cexWallets.js           # Dual storage with smart sync
├── kolProfiles.js          # Dual storage with smart sync
├── tokenHolders.js         # Smart TTL cache
├── timeoutTokens.js        # Single cache keys (non-resetting TTL)
├── invalidTokens.js        # Single cache keys (non-resetting TTL)
└── metadata.js             # Single cache keys (resetting TTL)
```

**Rules:**
- Use camelCase for file names: `spamTokens.js`, `cexWallets.js`
- No `.md` files or `__tests__/` directories in utils/cache
- Each file should be self-contained with all related functions

### Standard File Template

```javascript
// File: utils/cache/[entityName].js
import { query } from '@/utils/postgres';
import redis from '@/utils/redis.server';
import DOMPurify from 'isomorphic-dompurify'; // Use when handling user input

/**
 * Cache TTL for [entity] ([duration])
 */
export const [ENTITY]_CACHE_TTL = 60 * 60 * 24; // 24 hours

/**
 * Redis key for [entity]
 */
export const [ENTITY]_KEY = 'stalkchain:[entity]:[type]';

/**
 * Checks if [entity] exists in cache
 * 
 * @param {string} identifier - The identifier to check
 * @returns {Promise<boolean>} True if exists, false otherwise
 */
export async function checkIs[Entity](identifier) {
  if (!identifier) {
    throw new Error('identifier is required');
  }
  
  try {
    // Implementation based on pattern chosen
    const result = await redis.sismember([ENTITY]_KEY, identifier.trim());
    return result === 1;
  } catch (error) {
    console.error('Error checking [entity]:', error);
    return false; // Fail safe
  }
}

/**
 * Bulk check for multiple [entities]
 * 
 * @param {string[]} identifiers - Array of identifiers to check
 * @returns {Promise<Object>} Object mapping each identifier to its status
 */
export async function checkIs[Entities](identifiers) {
  if (!identifiers?.length) {
    throw new Error('identifiers array is required and cannot be empty');
  }
  
  const sanitized = identifiers.map(id => id.trim());
  
  try {
    const results = await redis.smismember([ENTITY]_KEY, sanitized);
    return sanitized.reduce((acc, id, index) => {
      acc[id] = results[index] === 1;
      return acc;
    }, {});
  } catch (error) {
    console.error('Error checking [entities]:', error);
    return sanitized.reduce((acc, id) => {
      acc[id] = false;
      return acc;
    }, {});
  }
}

/**
 * Synchronizes Redis cache with PostgreSQL data
 * Uses PostgreSQL as the source of truth
 * 
 * @returns {Promise<Object>} Result object with sync status and counts
 */
export async function sync[Entities]() {
  const result = {
    success: false,
    [entities]Added: 0,
    error: null
  };

  try {
    // Fetch from PostgreSQL (source of truth)
    const queryText = `SELECT [columns] FROM [table]`;
    const { rows } = await query(queryText, []);
    
    if (rows.length === 0) {
      result.success = true;
      return result;
    }
    
    // Extract identifiers
    const identifiers = rows.map(row => row.[identifier_column]);
    
    // Create temporary key for atomic swap
    const tempKey = `${[ENTITY]_KEY}:temp:${Date.now()}`;
    
    // Populate temporary set using pipeline
    const pipeline = redis.pipeline();
    identifiers.forEach(id => pipeline.sadd(tempKey, id));
    await pipeline.exec();
    
    // Atomic swap (zero downtime)
    await redis.multi()
      .rename(tempKey, [ENTITY]_KEY)
      .expire([ENTITY]_KEY, [ENTITY]_CACHE_TTL)
      .exec();
    
    result.success = true;
    result.[entities]Added = identifiers.length;
  } catch (error) {
    console.error('Error synchronizing [entities]:', error);
    result.error = error.message;
  }
  
  return result;
}

/**
 * Adds [entity] to cache
 * 
 * @param {Object} [entity]Data - Data for the [entity]
 * @returns {Promise<Object>} Result object with operation status
 */
export async function add[Entity]([entity]Data) {
  if (![entity]Data?.identifier) {
    throw new Error('Missing required fields');
  }

  const result = {
    success: false,
    postgres: { success: false, error: null },
    redis: { success: false, error: null },
    identifier: [entity]Data.identifier
  };

  // Run database and cache operations in parallel
  await Promise.allSettled([
    // Store in PostgreSQL
    (async () => {
      try {
        const queryText = `
          INSERT INTO [table] ([columns])
          VALUES ([placeholders])
          ON CONFLICT ([unique_column]) 
          DO UPDATE SET [update_clause]
          RETURNING *
        `;
        const { rows } = await query(queryText, [values]);
        result.postgres.success = true;
        result.postgres.data = rows[0];
      } catch (error) {
        console.error('Failed to store [entity] in PostgreSQL:', error);
        result.postgres.error = error.message;
      }
    })(),

    // Store in Redis
    (async () => {
      try {
        await redis.sadd([ENTITY]_KEY, [entity]Data.identifier);
        await redis.expire([ENTITY]_KEY, [ENTITY]_CACHE_TTL);
        result.redis.success = true;
      } catch (error) {
        console.error('Failed to add [entity] to Redis:', error);
        result.redis.error = error.message;
      }
    })()
  ]);

  result.success = result.postgres.success || result.redis.success;
  return result;
}
```

## Naming Conventions

### File Names
- Use camelCase: `spamTokens.js`, `cexWallets.js`, `tokenHolders.js`
- Be descriptive: `timeoutTokens.js` not `timeouts.js`

### Constants
- Use SCREAMING_SNAKE_CASE: `SPAM_TOKEN_CACHE_TTL`
- Include entity name: `SPAM_TOKENS_KEY` not `TOKENS_KEY`
- Include units in TTL names: `CACHE_TTL` implies seconds

### Functions
- Use descriptive verbs: `checkIsSpamToken`, `syncSpamTokens`
- Bulk operations: `checkIsSpamTokens` (plural)
- Boolean returns: `checkIs...`, `is...`
- Actions: `add...`, `remove...`, `sync...`

### Redis Keys
- Use consistent format: `stalkchain:entity:type`
- Avoid double colons: `stalkchain::entity` (bug in generateCacheKey)
- Use hardcoded strings or template literals
- Examples:
  - `stalkchain:spam:tokens`
  - `stalkchain:cex:wallets`
  - `stalkchain:timeout:token:${address}`

## Error Handling Rules

### 1. Input Validation
```javascript
// Always validate required parameters
if (!tokenAddress) {
  throw new Error('tokenAddress is required');
}

// Validate arrays
if (!tokenAddresses?.length) {
  throw new Error('tokenAddresses array is required and cannot be empty');
}
```

### 2. Sanitization
```javascript
// Sanitize user input
const sanitized = DOMPurify.sanitize(tokenAddress.trim());

// Sanitize arrays
const sanitized = tokenAddresses.map(addr => addr.trim());
```

### 3. Graceful Degradation
```javascript
try {
  const result = await redis.sismember(key, value);
  return result === 1;
} catch (error) {
  console.error('Redis error:', error);
  return false; // Fail safe - don't block application
}
```

### 4. Parallel Operations Error Handling
```javascript
await Promise.allSettled([
  (async () => {
    try {
      // PostgreSQL operation
    } catch (error) {
      result.postgres.error = error.message;
    }
  })(),
  (async () => {
    try {
      // Redis operation
    } catch (error) {
      result.redis.error = error.message;
    }
  })()
]);

// Success if either operation succeeded
result.success = result.postgres.success || result.redis.success;
```

## Performance Optimization

### 1. Use Pipelines for Batch Operations
```javascript
// Good: Single pipeline execution
const pipeline = redis.pipeline();
items.forEach(item => pipeline.sadd(key, item));
await pipeline.exec();

// Bad: Multiple individual operations
for (const item of items) {
  await redis.sadd(key, item); // Avoid this
}
```

### 2. Use Atomic Operations for Consistency
```javascript
// Good: Atomic swap
await redis.multi()
  .rename(tempKey, finalKey)
  .expire(finalKey, ttl)
  .exec();

// Bad: Non-atomic operations
await redis.del(finalKey);
await redis.rename(tempKey, finalKey);
await redis.expire(finalKey, ttl);
```

### 3. Smart TTL Based on Data Characteristics
```javascript
// Dynamic TTL based on data size/volatility
const tier = getCacheTierByHolderCount(holderCount);
if (tier.ttl === 0) return false; // Don't cache volatile data

await redis.setex(key, tier.ttl, data);
```

### 4. Bulk Operations
```javascript
// Good: Single bulk operation
const results = await redis.smismember(key, addresses);

// Bad: Multiple individual checks
const results = {};
for (const address of addresses) {
  results[address] = await redis.sismember(key, address);
}
```

## Testing Guidelines

### Unit Test Template
```javascript
// File: utils/cache/__tests__/spamTokens.test.js
import { jest } from '@jest/globals';
import { checkIsSpamToken, syncSpamTokens } from '../spamTokens.js';

// Mock Redis
jest.mock('@/utils/redis.server', () => ({
  sismember: jest.fn(),
  sadd: jest.fn(),
  pipeline: jest.fn(() => ({
    sadd: jest.fn(),
    exec: jest.fn()
  })),
  multi: jest.fn(() => ({
    rename: jest.fn(),
    expire: jest.fn(),
    exec: jest.fn()
  }))
}));

// Mock PostgreSQL
jest.mock('@/utils/postgres', () => ({
  query: jest.fn()
}));

describe('spamTokens cache utility', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('checkIsSpamToken', () => {
    it('should return true for spam token', async () => {
      const mockRedis = require('@/utils/redis.server');
      mockRedis.sismember.mockResolvedValue(1);

      const result = await checkIsSpamToken('spam-token-address');
      expect(result).toBe(true);
      expect(mockRedis.sismember).toHaveBeenCalledWith(
        'stalkchain:spam:tokens',
        'spam-token-address'
      );
    });

    it('should handle Redis errors gracefully', async () => {
      const mockRedis = require('@/utils/redis.server');
      mockRedis.sismember.mockRejectedValue(new Error('Redis error'));

      const result = await checkIsSpamToken('test-token');
      expect(result).toBe(false); // Fail safe
    });

    it('should throw error for missing tokenAddress', async () => {
      await expect(checkIsSpamToken()).rejects.toThrow('tokenAddress is required');
    });
  });
});
```

## Common Pitfalls to Avoid

### 1. Double Colon Bug in Keys
```javascript
// ❌ Bad: Creates "stalkchain::spam:tokens"
const key = generateCacheKey('spam', 'tokens');

// ✅ Good: Creates "stalkchain:spam:tokens"
const key = 'stalkchain:spam:tokens';
const key = `${REDIS_CONFIG.cache.keys.prefix}spam${REDIS_CONFIG.cache.keys.separator}tokens`;
```

### 2. Non-Atomic Operations
```javascript
// ❌ Bad: Race condition possible
await redis.del(oldKey);
await redis.sadd(newKey, ...items);

// ✅ Good: Atomic operation
await redis.multi()
  .del(oldKey)
  .sadd(newKey, ...items)
  .exec();
```

### 3. Not Handling Cache Failures
```javascript
// ❌ Bad: Application breaks if Redis fails
const isSpam = await redis.sismember(key, token);
return isSpam === 1;

// ✅ Good: Graceful degradation
try {
  const isSpam = await redis.sismember(key, token);
  return isSpam === 1;
} catch (error) {
  console.error('Redis error:', error);
  return false; // Fail safe
}
```

### 4. Inefficient Bulk Operations
```javascript
// ❌ Bad: Multiple round trips
const results = {};
for (const token of tokens) {
  results[token] = await redis.sismember(key, token);
}

// ✅ Good: Single operation
const results = await redis.smismember(key, tokens);
```

### 5. Inconsistent Error Handling
```javascript
// ❌ Bad: Inconsistent return types
export async function checkToken(token) {
  try {
    return await redis.sismember(key, token) === 1;
  } catch (error) {
    throw error; // Breaks calling code
  }
}

// ✅ Good: Consistent boolean return
export async function checkToken(token) {
  try {
    return await redis.sismember(key, token) === 1;
  } catch (error) {
    console.error('Error checking token:', error);
    return false; // Always returns boolean
  }
}
```

## Integration with PostgreSQL

### Smart Sync Pattern (Recommended)
```javascript
// Used in actions - cache-first with smart background refresh
export async function getEntitiesWithSmartSync() {
  try {
    let cacheResult = await getAllEntities();
    
    // If cache empty, sync immediately (blocking)
    if (!cacheResult.success || cacheResult.count === 0) {
      console.log('Entities cache is empty, syncing from PostgreSQL...');
      const syncResult = await syncEntities();
      if (syncResult.success) {
        cacheResult = await getAllEntities();
      }
    } else {
      // Cache has data, trigger background sync if cooldown passed
      triggerBackgroundSync();
    }
    
    return cacheResult;
  } catch (error) {
    console.error('Error getting entities:', error);
    return { success: false, entities: [], error: error.message };
  }
}

// Background sync with 1-hour cooldown
async function triggerBackgroundSync() {
  const SYNC_COOLDOWN_KEY = 'stalkchain:entities:last_sync';
  const COOLDOWN_SECONDS = 60 * 60; // 1 hour
  
  try {
    const lastSync = await redis.get(SYNC_COOLDOWN_KEY);
    const now = Math.floor(Date.now() / 1000);
    
    if (!lastSync || (now - parseInt(lastSync)) > COOLDOWN_SECONDS) {
      await redis.set(SYNC_COOLDOWN_KEY, now.toString(), 'EX', COOLDOWN_SECONDS * 2);
      
      setImmediate(async () => {
        try {
          console.log('Starting background entities sync...');
          const syncResult = await syncEntities();
          if (syncResult.success) {
            console.log(`Background sync completed: ${syncResult.entitiesAdded} entities synced`);
          }
        } catch (error) {
          console.error('Background sync error:', error);
        }
      });
    }
  } catch (error) {
    console.error('Error checking sync cooldown:', error);
  }
}

// Zero-downtime sync from PostgreSQL
export async function syncEntities() {
  const result = { success: false, entitiesAdded: 0, error: null };

  try {
    // 1. Fetch from PostgreSQL (source of truth)
    const { rows } = await query('SELECT id FROM entities');
    
    if (rows.length === 0) {
      result.success = true;
      return result;
    }
    
    const entityIds = rows.map(row => row.id);
    
    // 2. Create temporary Redis key
    const tempKey = `${ENTITIES_KEY}:temp:${Date.now()}`;
    
    // 3. Populate temporary key
    const pipeline = redis.pipeline();
    entityIds.forEach(id => pipeline.sadd(tempKey, id));
    await pipeline.exec();
    
    // 4. Atomic swap (zero downtime)
    await redis.multi()
      .rename(tempKey, ENTITIES_KEY)
      .expire(ENTITIES_KEY, CACHE_TTL)
      .exec();
    
    result.success = true;
    result.entitiesAdded = entityIds.length;
  } catch (error) {
    console.error('Error syncing entities:', error);
    result.error = error.message;
  }
  
  return result;
}
```

### Write-Through Pattern (For Real-Time Updates)
```javascript
export async function addEntity(entityData) {
  const result = { postgres: {}, redis: {} };
  
  // Write to both PostgreSQL and Redis in parallel
  await Promise.allSettled([
    // PostgreSQL (source of truth)
    (async () => {
      try {
        const { rows } = await query('INSERT INTO entities ...', [values]);
        result.postgres.success = true;
        result.postgres.data = rows[0];
      } catch (error) {
        result.postgres.error = error.message;
      }
    })(),
    
    // Redis (cache)
    (async () => {
      try {
        await redis.sadd(ENTITIES_KEY, entityData.id);
        result.redis.success = true;
      } catch (error) {
        result.redis.error = error.message;
      }
    })()
  ]);
  
  return result;
}
```

## Monitoring and Debugging

### Logging Standards
```javascript
// Cache hits/misses
console.log(`🎯 Cache HIT: ${key} (${metadata})`);
console.log(`🎯 Cache MISS: ${key}`);

// Operations
console.log(`🎯 Cached: ${key} (TTL: ${ttl}s, ${description})`);
console.log(`🎯 NOT cached: ${key} (${reason})`);

// Sync operations
console.log(`🔄 Synced ${count} entities to Redis`);

// Errors
console.error('Redis error in [context]:', error);
```

### Performance Monitoring
```javascript
// Track operation timing
const start = Date.now();
const result = await redis.smismember(key, items);
const duration = Date.now() - start;

if (duration > 100) {
  console.warn(`Slow Redis operation: ${duration}ms for ${items.length} items`);
}
```

This recipe provides comprehensive guidelines for creating consistent, performant, and reliable cache utilities. Follow these patterns to ensure all cache utilities work seamlessly together and provide optimal performance.