# KOL Feed Functions Usage Guide

This document details how to use the database functions for retrieving and analyzing Key Opinion Leader (KOL) activity data. This guide focuses on the implementation of three primary functions for KOL data analysis.

## Table of Contents

- [Function Overview](#function-overview)
- [fn_get_kols_feed_enriched](#fn_get_kols_feed_enriched)
  - [Parameters](#parameters)
  - [Returned Data Fields](#returned-data-fields)
  - [Example Usage](#example-usage)
  - [Example Data Record](#example-data-record)
- [fn_get_kols_token_stats](#fn_get_kols_token_stats)
  - [Parameters](#parameters-1)
  - [Returned Data Fields](#returned-data-fields-1)
  - [Example Usage](#example-usage-1)
  - [Example Data Record](#example-data-record-1)
- [fn_get_kols_detailed_token_stats](#fn_get_kols_detailed_token_stats)
  - [Parameters](#parameters-2)
  - [Returned Data Fields](#returned-data-fields-2)
  - [Example Usage](#example-usage-2)
  - [Example Data Record](#example-data-record-2)
- [fn_get_kols_token_rankings](#fn_get_kols_token_rankings)
  - [Parameters](#parameters-3)
  - [Returned Data Fields](#returned-data-fields-3)
  - [Example Usage](#example-usage-3)
  - [Example Data Record](#example-data-record-3)
- [JavaScript Integration](#javascript-integration)
- [Function Definition Code](#function-definition-code)
  - [fn_get_kols_feed_enriched Function Definition](#fn_get_kols_feed_enriched-function-definition)
  - [fn_get_kols_token_stats Function Definition](#fn_get_kols_token_stats-function-definition)
  - [fn_get_kols_detailed_token_stats Function Definition](#fn_get_kols_detailed_token_stats-function-definition)
  - [fn_get_kols_token_rankings Function Definition](#fn_get_kols_token_rankings-function-definition)

## Function Overview

The functions provide comprehensive ways to analyze KOL activity:

**KOL Feed Data**: `fn_get_kols_feed_enriched(p_timeframe)` - Returns enriched transaction data including both regular transactions and DCA events for all KOLs within a specified timeframe.

**Token-Specific Aggregate Stats**: `fn_get_kols_token_stats(p_token_mint, p_timeframe)` - Provides aggregate statistics about KOL activity for a specific token, such as unique KOL count and total volume.

**Detailed Per-KOL Token Stats**: `fn_get_kols_detailed_token_stats(p_token_mint, p_timeframe, p_dust_threshold)` - Returns detailed statistics for each KOL's interaction with a specific token, including buy/sell counts, volumes, and profitability metrics.

**Token Rankings**: `fn_get_kols_token_rankings(p_timeframe, p_dust_threshold, p_limit)` - Ranks tokens based on KOL trading activity, excluding stablecoins and SOL derivatives, with metrics on trading volumes and holder counts.

## fn_get_kols_feed_enriched

This function returns comprehensive enriched transaction data for all KOLs within a specified timeframe, including both regular transactions and DCA events.

### Parameters

| Parameter Name | Data Type | Description | Default |
|---------------|-----------|-------------|---------|
| `p_timeframe` | INTERVAL | The time window to look back (e.g., '1 day', '7 days') | (required) |

### Returned Data Fields

The function returns a result set with the following columns:

| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `signature` | TEXT | Transaction signature |
| `type` | TEXT | Transaction type ('buy', 'sell', or 'dca') |
| `dca_type` | TEXT | If applicable, the DCA event type ('opened', 'closed') |
| `dca_key` | TEXT | If applicable, the unique identifier for the DCA order |
| `user_closed` | BOOLEAN | If applicable, whether DCA was closed by user or auto-completed |
| `time` | TIMESTAMPTZ | Transaction timestamp |
| `slot` | BIGINT | Blockchain slot number |
| `wallet` | TEXT | KOL wallet address |
| `input_mint` | TEXT | Address of the input (source) token |
| `input_amount` | NUMERIC | Amount of input token |
| `input_usd_price` | NUMERIC | USD price of the input token |
| `input_amount_usd` | NUMERIC | USD value of the input amount |
| `input_market_cap` | NUMERIC | Market cap of the input token |
| `input_mint_metadata` | JSONB | Metadata for the input token |
| `output_mint` | TEXT | Address of the output (target) token |
| `output_amount` | NUMERIC | Amount of output token |
| `output_usd_price` | NUMERIC | USD price of the output token |
| `output_amount_usd` | NUMERIC | USD value of the output amount |
| `output_market_cap` | NUMERIC | Market cap of the output token |
| `output_mint_metadata` | JSONB | Metadata for the output token |
| `total_usd_value` | NUMERIC | Total USD value of the transaction |
| `is_kol` | BOOLEAN | Flag indicating if the wallet belongs to a KOL |
| `kol_label` | VARCHAR(100) | KOL display name |
| `kol_type` | VARCHAR(20) | KOL type (e.g., 'sol') |
| `kol_avatar` | TEXT | URL to KOL avatar image |
| `kol_socials` | JSONB | JSON object containing KOL social media information |
| `kol_is_public` | BOOLEAN | Flag indicating if the KOL profile is public |

### Example Usage

```sql
-- Retrieve all KOL activity from the last 24 hours
SELECT * FROM fn_get_kols_feed_enriched('24 hours')
LIMIT 100;

-- Get KOL transactions from the past week for a specific KOL
SELECT * FROM fn_get_kols_feed_enriched('7 days')
WHERE kol_label = 'casino'
ORDER BY time DESC;

-- Analyze all buy transactions in the past 3 days
SELECT
    wallet,
    kol_label,
    COUNT(*) AS transaction_count,
    SUM(total_usd_value) AS total_volume_usd
FROM fn_get_kols_feed_enriched('3 days')
WHERE type = 'buy'
GROUP BY wallet, kol_label
ORDER BY total_volume_usd DESC;
```

### Example Data Record

```
signature: vkdGjEbgZ29LPd2wvH1NzGRbrUw6RmNi6Zow5BbtBYXKZEQKu6g6wpSpvQYmJ6SWuC71XKSchKga5higw7H41ft
type: sell
dca_type: 
dca_key: 
user_closed: 
time: 2025-04-26 20:47:12+00
slot: 0
wallet: 62FZUSWPMX9pofoV1uWHMdzFJRjwMa1LHgh2zhdEB7Zj
input_mint: CWY3XDTbN91sSmg4XR3ZgxAVgrhiFv4Ue8gUwwJLbonk
input_amount: 1463555.714585
input_usd_price: 0.000521
input_amount_usd: 762.903655
input_market_cap: 521267.244562
input_mint_metadata: {"icon": "", "name": "BAM by Scotty", "ticker": "BAM", "decimals": 0}
output_mint: So11111111111111111111111111111111111111112
output_amount: 5.054003305000009
output_usd_price: 149.062114
output_amount_usd: 753.360418
output_market_cap: 
output_mint_metadata: {"icon": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png", "name": "Wrapped SOL", "ticker": "SOL", "decimals": 0}
total_usd_value: 762.903655
is_kol: t
kol_label: Blosom
kol_type: sol
kol_avatar: https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/blosomcrypto.jpg
kol_socials: [{"type": "x", "handle": "BlosomCrypto", "followers": 4253, "editedTimestamp": 1745422726854}]
kol_is_public: f
```

## fn_get_kols_token_stats

This function provides aggregate statistics about KOL activity for a specific token within a specified timeframe.

### Parameters

| Parameter Name | Data Type | Description | Default |
|---------------|-----------|-------------|---------|
| `p_token_mint` | VARCHAR(44) | The token mint address to analyze | (required) |
| `p_timeframe` | INTERVAL | The time window to look back (e.g., '24 hours', '7 days') | (required) |

### Returned Data Fields

The function returns a result set with the following columns:

| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `unique_kols_count` | BIGINT | Number of distinct KOLs who traded the token |
| `total_transactions_count` | BIGINT | Total number of transactions involving the token |
| `total_buy_volume` | NUMERIC | Total USD volume of buy transactions |
| `total_sell_volume` | NUMERIC | Total USD volume of sell transactions |

### Example Usage

```sql
-- Get daily statistics for a specific token
SELECT * FROM fn_get_kols_token_stats('5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', '24 hours');

-- Compare weekly vs. monthly activity
SELECT 
    '7 days' AS timeframe,
    * 
FROM fn_get_kols_token_stats('5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', '7 days')
UNION ALL
SELECT 
    '30 days' AS timeframe,
    * 
FROM fn_get_kols_token_stats('5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', '30 days');

-- Compare multiple tokens over the same period
SELECT 
    '5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG' AS token_mint,
    * 
FROM fn_get_kols_token_stats('5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', '7 days')
UNION ALL
SELECT 
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' AS token_mint,
    * 
FROM fn_get_kols_token_stats('EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v', '7 days');
```

### Example Data Record

```
unique_kols_count: 8
total_transactions_count: 53
total_buy_volume: 33393.783209
total_sell_volume: 39370.431330
```

## fn_get_kols_detailed_token_stats

This function returns detailed statistics for each KOL's interaction with a specific token, including buy/sell counts, volumes, and profitability metrics.

### Parameters

| Parameter Name | Data Type | Description | Default |
|---------------|-----------|-------------|---------|
| `p_token_mint` | VARCHAR(44) | The token mint address to analyze | (required) |
| `p_timeframe` | INTERVAL | The time window to look back | (required) |
| `p_dust_threshold` | NUMERIC | Threshold for considering positions fully closed (as decimal) | 0.001 (0.1%) |

### Returned Data Fields

The function returns a result set with the following columns:

| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `wallet` | VARCHAR(44) | KOL wallet address |
| `kol_label` | VARCHAR(100) | KOL display name |
| `kol_avatar` | TEXT | URL to KOL avatar image |
| `kol_socials` | JSONB | JSON object with KOL social media information |
| `buy_count` | BIGINT | Number of buy transactions |
| `sell_count` | BIGINT | Number of sell transactions |
| `total_transactions` | BIGINT | Total number of transactions (buy + sell) |
| `buy_volume_usd` | NUMERIC | Total USD volume of buy transactions |
| `sell_volume_usd` | NUMERIC | Total USD volume of sell transactions |
| `net_volume_usd` | NUMERIC | Net trading volume (sell - buy) |
| `total_volume_usd` | NUMERIC | Total trading volume (buy + sell) |
| `total_bought_amount` | NUMERIC | Total amount of tokens bought |
| `total_sold_amount` | NUMERIC | Total amount of tokens sold |
| `unsold_token_amount` | NUMERIC | Remaining token amount (bought - sold) |
| `dust_filtered_amount` | NUMERIC | Remaining amount after dust filtering |
| `sell_all` | BOOLEAN | Whether the position is fully closed (considering dust) |
| `realized_pnl_percent` | NUMERIC | Realized profit/loss percentage |
| `first_buy` | TIMESTAMPTZ | Timestamp of first buy transaction |
| `first_sell` | TIMESTAMPTZ | Timestamp of first sell transaction |
| `hold_time_seconds` | BIGINT | Time between first buy and first sell (or now) |

### Example Usage

```sql
-- Get detailed KOL activity for a token with default dust threshold
SELECT * FROM fn_get_kols_detailed_token_stats(
    '5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', 
    '24 hours'
);

-- Use a custom dust threshold of 0.5%
SELECT * FROM fn_get_kols_detailed_token_stats(
    '5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', 
    '7 days',
    0.005
);

-- Find KOLs who fully exited their positions
SELECT * FROM fn_get_kols_detailed_token_stats(
    '5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', 
    '30 days'
)
WHERE sell_all = true
ORDER BY total_volume_usd DESC;

-- Find most profitable KOL trades
SELECT 
    kol_label,
    buy_count,
    sell_count,
    buy_volume_usd,
    sell_volume_usd,
    realized_pnl_percent
FROM fn_get_kols_detailed_token_stats(
    '5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG', 
    '30 days'
)
WHERE realized_pnl_percent IS NOT NULL
ORDER BY realized_pnl_percent DESC
LIMIT 10;
```

### Example Data Record

```
wallet: 8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR
kol_label: casino
kol_avatar: https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/casino616.jpg
kol_socials: [{"type": "x", "handle": "casino616", "followers": 51300, "editedTimestamp": 1745422729721}]
buy_count: 7
sell_count: 27
total_transactions: 34
buy_volume_usd: 10434.083826
sell_volume_usd: 28411.494175
net_volume_usd: 17977.410349
total_volume_usd: 38845.578001
total_bought_amount: 37137534.4490009989
total_sold_amount: 37137534.44900099923
unsold_token_amount: -0.00000000033
dust_filtered_amount: 0
sell_all: t
realized_pnl_percent: 172.29505387146005089400
first_buy: 2025-04-27 05:30:53+00
first_sell: 2025-04-27 05:31:37+00
hold_time_seconds: 44
```

## fn_get_kols_token_rankings

This function ranks tokens based on KOL trading activity within a specified timeframe. It excludes stablecoins and SOL derivatives to focus on more interesting tokens and only includes tokens where at least one KOL is currently holding a non-dust amount.

### Parameters

| Parameter Name | Data Type | Description | Default |
|---------------|-----------|-------------|---------|
| `p_timeframe` | INTERVAL | The time window to analyze (e.g., '1 day', '7 days') | '1 day' |
| `p_dust_threshold` | NUMERIC | Threshold for filtering out dust holdings (as decimal) | 0.001 (0.1%) |
| `p_limit` | INTEGER | Maximum number of tokens to return | 100 |

### Returned Data Fields

The function returns a result set with the following columns:

| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `token_mint` | VARCHAR(44) | The token's mint address |
| `unique_kols_count` | BIGINT | Number of distinct KOLs who traded the token in the timeframe |
| `buy_volume_usd` | NUMERIC | Total USD value of buys during the timeframe |
| `sell_volume_usd` | NUMERIC | Total USD value of sells during the timeframe |
| `net_volume_usd` | NUMERIC | Net volume (buy minus sell) indicating market direction |
| `total_volume_usd` | NUMERIC | Total trading volume (buy plus sell) |
| `holders_count` | BIGINT | Number of KOLs currently holding the token (based on all-time data) |
| `buy_sell_ratio` | NUMERIC | Ratio of buy volume to sell volume (market sentiment indicator) |

### Example Usage

```sql
-- Get default token rankings (last 24 hours)
SELECT * FROM fn_get_kols_token_rankings();

-- Get token rankings for the last 7 days
SELECT * FROM fn_get_kols_token_rankings(INTERVAL '7 days');

-- Get token rankings with custom dust threshold
SELECT * FROM fn_get_kols_token_rankings(INTERVAL '3 days', 0.005);

-- Get top 50 tokens from the last month
SELECT * FROM fn_get_kols_token_rankings(INTERVAL '30 days', 0.001, 50);
```

### Example Data Record

```
token_mint: DitHyRMQiSDhn5cnKMJV2CDDt6sVct96YrECiM49pump
unique_kols_count: 7
buy_volume_usd: 206251.171989
sell_volume_usd: 97574.006055
net_volume_usd: 108677.165934
total_volume_usd: 303825.178044
holders_count: 5
buy_sell_ratio: 2.1137921904399562
```

## JavaScript Integration

Here's how to integrate these functions with your JavaScript application:

```javascript
const { Pool } = require('pg');

// Create connection pool
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
});

/**
 * Get enriched KOL feed data
 * @param {string} timeframe - Time window to look back (e.g., '24 hours', '7 days')
 * @param {number} limit - Optional: Maximum number of records to return
 * @returns {Promise<Array>} List of KOL transactions
 */
async function getKolsFeedEnriched(timeframe, limit = 100) {
  const query = 'SELECT * FROM fn_get_kols_feed_enriched($1) LIMIT $2';
  const result = await pool.query(query, [timeframe, limit]);
  return result.rows || [];
}

/**
 * Get aggregate statistics for KOL activity on a specific token
 * @param {string} tokenMint - The token mint address
 * @param {string} timeframe - Time window to look back
 * @returns {Promise<Object>} Aggregate statistics
 */
async function getKolsTokenStats(tokenMint, timeframe) {
  const query = 'SELECT * FROM fn_get_kols_token_stats($1, $2)';
  const result = await pool.query(query, [tokenMint, timeframe]);
  return result.rows[0] || null;
}

/**
 * Get detailed per-KOL statistics for a specific token
 * @param {string} tokenMint - The token mint address
 * @param {string} timeframe - Time window to look back
 * @param {number} dustThreshold - Optional: Threshold for considering positions closed
 * @returns {Promise<Array>} List of detailed KOL statistics
 */
async function getKolsDetailedTokenStats(tokenMint, timeframe, dustThreshold = 0.001) {
  const query = 'SELECT * FROM fn_get_kols_detailed_token_stats($1, $2, $3)';
  const result = await pool.query(query, [tokenMint, timeframe, dustThreshold]);
  return result.rows || [];
}

// Example usage
async function example() {
  try {
    // Example 1: Get recent KOL activity
    const kolActivity = await getKolsFeedEnriched('24 hours', 50);
    console.log('Recent KOL Activity:', kolActivity);
    
    // Example 2: Get aggregate statistics for a token
    const tokenMint = '5kGVjyXaEejcywCYm1zPcVc7vFbuYm8H4mBhAz6xQBHG';
    const tokenStats = await getKolsTokenStats(tokenMint, '7 days');
    console.log('Token Stats:', tokenStats);
    
    // Example 3: Get detailed KOL stats for a token
    const detailedStats = await getKolsDetailedTokenStats(tokenMint, '7 days');
    console.log('Detailed KOL Stats:', detailedStats);
    
    // Example 4: Process multiple time periods
    const timeframes = ['24 hours', '7 days', '30 days'];
    const timeframeResults = await Promise.all(
      timeframes.map(timeframe => getKolsTokenStats(tokenMint, timeframe))
    );
    
    console.log('Stats across different timeframes:', 
      timeframes.map((timeframe, index) => ({
        timeframe,
        stats: timeframeResults[index]
      }))
    );
  } catch (error) {
    console.error('Error fetching KOL data:', error);
  }
}
```

## Function Definition Code

For reference, here is the full SQL code for the functions:

### fn_get_kols_feed_enriched Function Definition

```sql
CREATE OR REPLACE FUNCTION fn_get_kols_feed_enriched(p_timeframe INTERVAL)
RETURNS TABLE (
    signature TEXT,
    type TEXT,
    dca_type TEXT,
    dca_key TEXT,
    user_closed BOOLEAN,
    "time" TIMESTAMPTZ,
    slot BIGINT,
    wallet TEXT,
    input_mint TEXT,
    input_amount NUMERIC,
    input_usd_price NUMERIC,
    input_amount_usd NUMERIC,
    input_market_cap NUMERIC,
    input_mint_metadata JSONB,
    output_mint TEXT,
    output_amount NUMERIC,
    output_usd_price NUMERIC,
    output_amount_usd NUMERIC,
    output_market_cap NUMERIC,
    output_mint_metadata JSONB,
    total_usd_value NUMERIC,
    is_kol BOOLEAN,
    kol_label VARCHAR(100),
    kol_type VARCHAR(20),
    kol_avatar TEXT,
    kol_socials JSONB,
    kol_is_public BOOLEAN
) AS $
BEGIN
    RETURN QUERY
    
    -- Regular KOL transactions
    SELECT 
        t.signature::TEXT,
        t.type::TEXT,
        NULL AS dca_type,
        NULL AS dca_key,
        NULL AS user_closed,
        t."time",
        t.slot,
        t.wallet::TEXT,
        t.input_mint::TEXT,
        t.input_amount,
        t.input_usd_price,
        t.input_amount_usd,
        t.input_market_cap,
        t.input_mint_metadata,
        t.output_mint::TEXT,
        t.output_amount,
        t.output_usd_price,
        t.output_amount_usd,
        t.output_market_cap,
        t.output_mint_metadata,
        t.total_usd_value,
        t.is_kol,
        p.label AS kol_label,
        p.type AS kol_type,
        p.avatar AS kol_avatar,
        p.socials AS kol_socials,
        p.is_public AS kol_is_public
    FROM 
        public.stream_kol_feed_transactions_ht t
    LEFT JOIN 
        public.ref_kols_profiles p ON t.wallet = p.wallet
    WHERE 
        t.is_kol = true
        AND t."time" > NOW() - p_timeframe
    
    UNION ALL
    
    -- DCA transactions from KOLs
    SELECT
        d.signature::TEXT,
        'dca'::TEXT AS type,
        d.event_type::TEXT AS dca_type,
        d.dca_key::TEXT,
        d.user_closed,
        d."time",
        d.slot,
        d.user_key::TEXT AS wallet,
        NULL AS input_mint,
        NULL AS input_amount,
        d.input_usd_price,
        NULL AS input_amount_usd,
        NULL AS input_market_cap,
        NULL AS input_mint_metadata,
        NULL AS output_mint,
        NULL AS output_amount,
        d.output_usd_price,
        NULL AS output_amount_usd,
        NULL AS output_market_cap,
        NULL AS output_mint_metadata,
        NULL AS total_usd_value,
        true AS is_kol,
        p.label AS kol_label,
        p.type AS kol_type,
        p.avatar AS kol_avatar,
        p.socials AS kol_socials,
        p.is_public AS kol_is_public
    FROM 
        public.stream_jupiter_dca_ht d
    LEFT JOIN 
        public.ref_kols_profiles p ON d.user_key = p.wallet
    WHERE 
        d.event_type IN ('opened', 'closed')
        AND d.user_key IN (SELECT kp.wallet FROM public.ref_kols_profiles kp)
        AND d."time" > NOW() - p_timeframe;
END;
$ LANGUAGE plpgsql;
```

### fn_get_kols_token_stats Function Definition

```sql
CREATE OR REPLACE FUNCTION fn_get_kols_token_stats(p_token_mint VARCHAR(44), p_timeframe INTERVAL)
RETURNS TABLE (
    unique_kols_count BIGINT,
    total_transactions_count BIGINT,
    total_buy_volume NUMERIC,
    total_sell_volume NUMERIC
) AS $
BEGIN
    RETURN QUERY
    WITH token_transactions AS (
        SELECT
            wallet,
            "time",
            input_mint,
            output_mint,
            total_usd_value,
            -- Determine if it's a buy or sell
            CASE WHEN output_mint = p_token_mint THEN total_usd_value ELSE 0 END AS buy_volume,
            CASE WHEN input_mint = p_token_mint THEN total_usd_value ELSE 0 END AS sell_volume
        FROM 
            public.stream_kol_feed_transactions_ht
        WHERE 
            is_kol = true
            AND (input_mint = p_token_mint OR output_mint = p_token_mint)
            AND "time" > NOW() - p_timeframe
    )
    SELECT
        COUNT(DISTINCT wallet) AS unique_kols_count,
        COUNT(*) AS total_transactions_count,
        COALESCE(SUM(buy_volume), 0) AS total_buy_volume,
        COALESCE(SUM(sell_volume), 0) AS total_sell_volume
    FROM
        token_transactions;
END;
$ LANGUAGE plpgsql;
```

### fn_get_kols_detailed_token_stats Function Definition

```sql
CREATE OR REPLACE FUNCTION fn_get_kols_detailed_token_stats(
    p_token_mint VARCHAR(44), 
    p_timeframe INTERVAL,
    p_dust_threshold NUMERIC DEFAULT 0.001 -- 0.1% expressed as decimal
)
RETURNS TABLE (
    wallet VARCHAR(44),
    kol_label VARCHAR(100),
    kol_avatar TEXT,
    kol_socials JSONB,
    buy_count BIGINT,
    sell_count BIGINT,
    total_transactions BIGINT,
    buy_volume_usd NUMERIC,
    sell_volume_usd NUMERIC,
    net_volume_usd NUMERIC,
    total_volume_usd NUMERIC,
    total_bought_amount NUMERIC,
    total_sold_amount NUMERIC,
    unsold_token_amount NUMERIC,
    dust_filtered_amount NUMERIC,
    sell_all BOOLEAN,
    realized_pnl_percent NUMERIC,
    first_buy TIMESTAMPTZ,
    first_sell TIMESTAMPTZ,
    hold_time_seconds BIGINT
) AS $
BEGIN
    RETURN QUERY
    WITH kol_transactions AS (
        SELECT
            t.wallet,
            t."time",
            t.input_mint,
            t.output_mint,
            t.input_amount,
            t.output_amount,
            t.total_usd_value,
            -- Track buys and sells
            CASE WHEN t.output_mint = p_token_mint THEN 1 ELSE 0 END AS is_buy,
            CASE WHEN t.input_mint = p_token_mint THEN 1 ELSE 0 END AS is_sell,
            -- Track token amounts
            CASE WHEN t.output_mint = p_token_mint THEN t.output_amount ELSE 0 END AS bought_amount,
            CASE WHEN t.input_mint = p_token_mint THEN t.input_amount ELSE 0 END AS sold_amount,
            -- Track USD values
            CASE WHEN t.output_mint = p_token_mint THEN t.total_usd_value ELSE 0 END AS buy_value,
            CASE WHEN t.input_mint = p_token_mint THEN t.total_usd_value ELSE 0 END AS sell_value
        FROM 
            public.stream_kol_feed_transactions_ht t
        WHERE 
            t.is_kol = true
            AND (t.input_mint = p_token_mint OR t.output_mint = p_token_mint)
            AND t."time" > NOW() - p_timeframe
    ),
    kol_time_stats AS (
        SELECT
            kt_time.wallet,
            MIN(CASE WHEN kt_time.is_buy = 1 THEN kt_time."time" END) AS first_buy,
            MIN(CASE WHEN kt_time.is_sell = 1 THEN kt_time."time" END) AS first_sell
        FROM
            kol_transactions kt_time
        GROUP BY
            kt_time.wallet
    ),
    kol_aggregates AS (
        SELECT
            kt.wallet,
            SUM(kt.is_buy) AS buy_count,
            SUM(kt.is_sell) AS sell_count,
            COUNT(*) AS total_transactions,
            SUM(kt.buy_value) AS buy_volume_usd,
            SUM(kt.sell_value) AS sell_volume_usd,
            SUM(kt.sell_value) - SUM(kt.buy_value) AS net_volume_usd,  -- Corrected: sell - buy
            SUM(kt.buy_value) + SUM(kt.sell_value) AS total_volume_usd,
            SUM(kt.bought_amount) AS total_bought_amount,
            SUM(kt.sold_amount) AS total_sold_amount,
            SUM(kt.bought_amount) - SUM(kt.sold_amount) AS unsold_token_amount,
            ts.first_buy,
            ts.first_sell,
            CASE 
                WHEN ts.first_sell IS NOT NULL AND ts.first_buy IS NOT NULL THEN 
                    EXTRACT(EPOCH FROM (ts.first_sell - ts.first_buy))::BIGINT
                WHEN ts.first_buy IS NOT NULL THEN
                    EXTRACT(EPOCH FROM (NOW() - ts.first_buy))::BIGINT
                ELSE NULL
            END AS hold_time_seconds,
            -- Calculate realized PnL percent
            CASE 
                WHEN SUM(kt.buy_value) > 0 AND SUM(kt.sell_value) > 0 THEN 
                    ((SUM(kt.sell_value) / SUM(kt.sold_amount)) - (SUM(kt.buy_value) / SUM(kt.bought_amount))) / (SUM(kt.buy_value) / SUM(kt.bought_amount)) * 100
                ELSE NULL
            END AS realized_pnl_percent
        FROM 
            kol_transactions kt
        JOIN
            kol_time_stats ts ON kt.wallet = ts.wallet
        GROUP BY 
            kt.wallet, ts.first_buy, ts.first_sell
    )
    SELECT
        ka.wallet,
        p.label AS kol_label,
        p.avatar AS kol_avatar,
        p.socials AS kol_socials,
        ka.buy_count,
        ka.sell_count,
        ka.total_transactions,
        ka.buy_volume_usd,
        ka.sell_volume_usd,
        ka.net_volume_usd,
        ka.total_volume_usd,
        ka.total_bought_amount,
        ka.total_sold_amount,
        ka.unsold_token_amount,
        CASE
            -- If unsold amount is within dust threshold of total bought, consider it fully sold (zero)
            WHEN ABS(ka.unsold_token_amount) <= (ka.total_bought_amount * p_dust_threshold) THEN 0
            -- Otherwise, keep the actual unsold amount
            ELSE ka.unsold_token_amount
        END AS dust_filtered_amount,
        -- Sell all flag
        CASE
            WHEN ABS(ka.unsold_token_amount) <= (ka.total_bought_amount * p_dust_threshold) THEN TRUE
            ELSE FALSE
        END AS sell_all,
        ka.realized_pnl_percent,
        ka.first_buy,
        ka.first_sell,
        ka.hold_time_seconds
    FROM 
        kol_aggregates ka
    LEFT JOIN 
        public.ref_kols_profiles p ON ka.wallet = p.wallet
    ORDER BY 
        ka.total_transactions DESC, 
        CASE
            WHEN ABS(ka.unsold_token_amount) <= (ka.total_bought_amount * p_dust_threshold) THEN 0
            ELSE ka.unsold_token_amount
        END DESC;
END;
$ LANGUAGE plpgsql;
```

### fn_get_kols_token_rankings Function Definition

```sql
CREATE OR REPLACE FUNCTION fn_get_kols_token_rankings(
    p_timeframe INTERVAL DEFAULT INTERVAL '1 day',
    p_dust_threshold NUMERIC DEFAULT 0.001,
    p_limit INTEGER DEFAULT 100
)
RETURNS TABLE (
    token_mint VARCHAR(44),
    unique_kols_count BIGINT,
    buy_volume_usd NUMERIC,
    sell_volume_usd NUMERIC,
    net_volume_usd NUMERIC,
    total_volume_usd NUMERIC,
    holders_count BIGINT,
    buy_sell_ratio NUMERIC
) AS $$
#variable_conflict use_column
BEGIN
    RETURN QUERY
    WITH 
    -- Get excluded tokens from reference tables
    excluded_mints AS (
        SELECT address AS mint FROM public.ref_stablecoins
        UNION
        SELECT address AS mint FROM public.ref_sol_derivatives
    ),

    -- Recent transactions for the specified timeframe with non-excluded tokens
    recent_transactions AS (
        SELECT
            wallet,
            time,
            input_mint,
            output_mint,
            input_amount,
            output_amount,
            total_usd_value
        FROM 
            public.stream_kol_feed_transactions_ht t
        WHERE 
            is_kol = true
            AND time > NOW() - p_timeframe
            AND (
                -- Use EXISTS for better performance with indexes
                NOT EXISTS (SELECT 1 FROM excluded_mints e WHERE e.mint = t.input_mint)
                OR 
                NOT EXISTS (SELECT 1 FROM excluded_mints e WHERE e.mint = t.output_mint)
            )
    ),

    -- Identify token buy activity (when token is output_mint)
    token_buys AS (
        SELECT
            output_mint AS token_mint_col,
            wallet,
            total_usd_value AS buy_volume,
            output_amount AS bought_amount
        FROM 
            recent_transactions t
        WHERE 
            NOT EXISTS (SELECT 1 FROM excluded_mints e WHERE e.mint = t.output_mint)
    ),

    -- Identify token sell activity (when token is input_mint)
    token_sells AS (
        SELECT
            input_mint AS token_mint_col,
            wallet,
            total_usd_value AS sell_volume,
            input_amount AS sold_amount
        FROM 
            recent_transactions t
        WHERE 
            NOT EXISTS (SELECT 1 FROM excluded_mints e WHERE e.mint = t.input_mint)
    ),

    -- Calculate per-token metrics for the timeframe by combining buys and sells
    token_metrics AS (
        SELECT
            token_mint_col,
            COUNT(DISTINCT wallet) AS unique_kols_count,
            SUM(buy_volume) AS buy_volume_usd,
            0 AS sell_volume_usd
        FROM
            token_buys
        GROUP BY
            token_mint_col
        
        UNION ALL
        
        SELECT
            token_mint_col,
            0 AS unique_kols_count, -- We'll sum this in the next step
            0 AS buy_volume_usd,
            SUM(sell_volume) AS sell_volume_usd
        FROM
            token_sells
        GROUP BY
            token_mint_col
    ),

    -- Aggregate the metrics
    token_metrics_combined AS (
        SELECT
            token_mint_col,
            SUM(unique_kols_count) AS unique_kols_count,
            SUM(buy_volume_usd) AS buy_volume_usd,
            SUM(sell_volume_usd) AS sell_volume_usd,
            SUM(buy_volume_usd) - SUM(sell_volume_usd) AS net_volume_usd,
            SUM(buy_volume_usd) + SUM(sell_volume_usd) AS total_volume_usd,
            CASE 
                WHEN SUM(sell_volume_usd) > 0 THEN 
                    SUM(buy_volume_usd) / SUM(sell_volume_usd)
                ELSE NULL
            END AS buy_sell_ratio
        FROM
            token_metrics
        GROUP BY
            token_mint_col
        HAVING
            SUM(buy_volume_usd) + SUM(sell_volume_usd) > 0
    ),

    -- All-time buys for holding calculation (with better index usage)
    all_time_buys AS (
        SELECT
            wallet,
            output_mint AS token_mint_col,
            SUM(output_amount) AS total_bought
        FROM 
            public.stream_kol_feed_transactions_ht t
        WHERE 
            is_kol = true
            AND NOT EXISTS (SELECT 1 FROM excluded_mints e WHERE e.mint = t.output_mint)
        GROUP BY
            wallet, output_mint
    ),

    -- All-time sells for holding calculation (with better index usage)
    all_time_sells AS (
        SELECT
            wallet,
            input_mint AS token_mint_col,
            SUM(input_amount) AS total_sold
        FROM 
            public.stream_kol_feed_transactions_ht t
        WHERE 
            is_kol = true
            AND NOT EXISTS (SELECT 1 FROM excluded_mints e WHERE e.mint = t.input_mint)
        GROUP BY
            wallet, input_mint
    ),

    -- Join buys and sells to calculate net holdings
    holdings AS (
        SELECT
            COALESCE(b.wallet, s.wallet) AS wallet,
            COALESCE(b.token_mint_col, s.token_mint_col) AS token_mint_col,
            COALESCE(b.total_bought, 0) AS total_bought,
            COALESCE(s.total_sold, 0) AS total_sold,
            COALESCE(b.total_bought, 0) - COALESCE(s.total_sold, 0) AS balance
        FROM
            all_time_buys b
        FULL OUTER JOIN
            all_time_sells s ON b.wallet = s.wallet AND b.token_mint_col = s.token_mint_col
    ),

    -- Count holders (KOLs with positive balance after dust filtering)
    token_holders AS (
        SELECT
            token_mint_col,
            COUNT(DISTINCT wallet) AS holders_count
        FROM (
            SELECT
                wallet,
                token_mint_col,
                balance,
                CASE
                    -- If balance is within dust threshold of total bought, consider it dust
                    WHEN balance <= (total_bought * p_dust_threshold) OR total_bought = 0 THEN 0
                    ELSE balance
                END AS filtered_balance
            FROM
                holdings
        ) AS filtered_holdings
        WHERE
            filtered_balance > 0
        GROUP BY
            token_mint_col
    )

    -- Final result combining all metrics with distinct wallet counts
    SELECT
        tm.token_mint_col AS token_mint,
        (SELECT COUNT(DISTINCT wallet) FROM 
            (SELECT wallet FROM token_buys WHERE token_mint_col = tm.token_mint_col
             UNION
             SELECT wallet FROM token_sells WHERE token_mint_col = tm.token_mint_col) AS combined_wallets
        ) AS unique_kols_count,
        tm.buy_volume_usd,
        tm.sell_volume_usd,
        tm.net_volume_usd,
        tm.total_volume_usd,
        COALESCE(th.holders_count, 0) AS holders_count,
        tm.buy_sell_ratio
    FROM
        token_metrics_combined tm
    LEFT JOIN
        token_holders th ON tm.token_mint_col = th.token_mint_col
    WHERE
        COALESCE(th.holders_count, 0) > 0
    ORDER BY
        tm.total_volume_usd DESC,
        unique_kols_count DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql;
```
