---
description: >-
  StalkChain KOL actions provide a comprehensive interface for retrieving Key Opinion Leader data,
  profiles, trading statistics, and token analytics with built-in caching, fallback mechanisms, and real-time data enrichment.
---

# KOL Actions

**Target Audience:** Frontend and backend developers implementing KOL-related features in StalkChain applications who need to fetch KOL profiles, trading statistics, and token analytics.

## Quick Start Guide

```jsx
import { getAllKolProfiles, fetchKolApiMetadata } from "@/actions/kol-profile/fetchKolApiData";
import { getKolTokenStats, getKolDetailedTokenStats, getKolTokenHoldings } from "@/actions/kol-profile/getKolTokenStats";
import { getKolMetadata } from "@/actions/kol-profile/getKolMetadata";
import { getMinimalKolMetadata } from "@/actions/kol-profile/getMinimalKolMetadata";
import { getKolTradeStats } from "@/actions/kol-profile/getKolTradeStats";

// 1. Get all KOL profiles with caching
const allKols = await getAllKolProfiles();

// 2. Get specific KOL profile by wallet address
const kolProfile = await fetchKolApiMetadata("8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR");

// 3. Get minimal metadata for fast page loads
const minimalMetadata = await getMinimalKolMetadata("8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR");

// 4. Get complete KOL metadata for page rendering
const kolMetadata = await getKolMetadata("casino");

// 5. Get KOL trading statistics
const tradeStats = await getKolTradeStats("casino");

// 6. Get KOL token analytics
const tokenStats = await getKolTokenStats("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", "30 days");
const detailedStats = await getKolDetailedTokenStats("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", "7 days");
const holdings = await getKolTokenHoldings("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 10);
```

## Core Actions

### Profile Management

#### `getAllKolProfiles(options = {})`

Retrieves all KOL profiles with full data from Redis cache. If cache is empty, syncs from PostgreSQL first and triggers background sync every hour to keep data fresh.

- **Parameters:**
  - `options` (object, optional): Query options (currently only used for response structure)
- **Returns:** Result object with full KOL profile data

```jsx
import { getAllKolProfiles } from "@/actions/kol-profile/fetchKolApiData";

// Get all KOL profiles
const allKols = await getAllKolProfiles();
console.log(`Found ${allKols.count} KOL profiles`);
```

**How it works:**

```mermaid
flowchart TD
    A("getAllKolProfiles() called") --> B{Redis cache exists?}
    
    B -->|Yes| C("Return cached profiles")
    B -->|No| D("Sync from PostgreSQL")
    
    D --> E("Store in Redis cache")
    E --> F("Return profiles")
    
    B -->|Has data| G("Trigger background sync")
    G --> H{Cooldown passed?}
    H -->|Yes| I("Background PostgreSQL sync")
    H -->|No| J("Skip sync")
    
    I --> K("Update Redis cache")
    
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef databaseProcess fill:#f3e5f5,stroke:#6a1b9a,color:#6a1b9a
    classDef decisionPoint fill:#f8f9fa,stroke:#455a64,color:#455a64
    classDef cacheProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    
    class A,F,C serverProcess
    class D,E,I,K databaseProcess
    class B,H decisionPoint
    class G,J cacheProcess
```

**Example Response:**

```json
{
  "status": "success",
  "data": [
    {
      "wallet": "8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR",
      "label": "casino",
      "type": "sol",
      "avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/casino616.jpg",
      "socials": [
        {
          "type": "x",
          "handle": "casino616",
          "followers": 51300,
          "editedTimestamp": 1745422729721
        }
      ],
      "isPublic": false,
      "createdAt": "2024-01-15T10:30:00Z",
      "updatedAt": "2024-01-20T15:45:00Z"
    }
  ],
  "count": 1,
  "fromCache": true,
  "cacheTtl": 604800,
  "pagination": {
    "limit": null,
    "offset": 0,
    "hasMore": false
  }
}
```

#### `fetchKolApiMetadata(address, skipCache = false)`

Fetches KOL profile data, trying central API first, then PostgreSQL as fallback with Redis caching.

- **Parameters:**
  - `address` (string): The case-sensitive wallet address to fetch data for
  - `skipCache` (boolean, optional): Whether to bypass the cache (default: `false`)
- **Returns:** Result object with KOL profile data

```jsx
import { fetchKolApiMetadata } from "@/actions/kol-profile/fetchKolApiData";

// Get KOL profile by wallet address
const kolProfile = await fetchKolApiMetadata("8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR");

// Skip cache for fresh data
const freshProfile = await fetchKolApiMetadata("8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR", true);
```

**Example Response:**

```json
{
  "success": true,
  "data": {
    "wallet": "8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR",
    "label": "casino",
    "avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/casino616.jpg",
    "isPublic": false,
    "socials": [
      {
        "type": "x",
        "handle": "casino616",
        "followers": 51300,
        "editedTimestamp": 1745422729721
      }
    ],
    "found": true
  },
  "error": null,
  "cached": false,
  "status": 200
}
```

### Metadata Actions

#### `getMinimalKolMetadata(identifier)`

Fetches minimal KOL profile data primarily from Redis cache for metadata generation. Returns default metadata immediately on cache miss to ensure fast initial load.

- **Parameters:**
  - `identifier` (string): The KOL identifier (wallet address)
- **Returns:** Minimal KOL profile data for metadata

```jsx
import { getMinimalKolMetadata } from "@/actions/kol-profile/getMinimalKolMetadata";

// Get minimal metadata for fast page loads
const metadata = await getMinimalKolMetadata("8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR");
```

**Example Response:**

```json
{
  "success": true,
  "data": {
    "label": "casino",
    "description": "View trading activity and analysis for casino.",
    "avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/casino616.jpg"
  },
  "profileFound": true
}
```

#### `getKolMetadata(identifier)`

Fetches complete KOL profile data for metadata generation, merging API data with dummy trading data for page rendering.

- **Parameters:**
  - `identifier` (string): The KOL identifier (wallet address, label, or social handle)
- **Returns:** Complete KOL profile data suitable for metadata or page rendering

```jsx
import { getKolMetadata } from "@/actions/kol-profile/getKolMetadata";

// Get complete KOL metadata for page rendering
const kolMetadata = await getKolMetadata("casino");

if (kolMetadata.profileFound) {
  console.log(`KOL: ${kolMetadata.data.label}`);
  console.log(`Description: ${kolMetadata.data.description}`);
}
```

**Example Response:**

```json
{
  "success": true,
  "data": {
    "wallet": "8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR",
    "label": "casino",
    "avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/casino616.jpg",
    "description": "View detailed trading information, portfolio analysis, and performance metrics for casino.",
    "isPublic": false,
    "socials": [
      {
        "type": "x",
        "handle": "casino616",
        "followers": 51300
      }
    ],
    "tradeVolumeByYear": [
      {
        "year": "2024",
        "buy": 30000000,
        "sell": 35000000
      }
    ],
    "performance": {
      "totalReturn": 628.24,
      "spReturn": 179.93
    },
    "winRate": 68.5
  },
  "profileFound": true,
  "profileError": null
}
```

### Trading Statistics

#### `getKolTradeStats(identifier)`

Fetches KOL trader statistics based on the provided identifier with Redis caching.

- **Parameters:**
  - `identifier` (string): The KOL identifier (wallet address, label, or social handle)
- **Returns:** Object with KOL trade statistics

```jsx
import { getKolTradeStats } from "@/actions/kol-profile/getKolTradeStats";

// Get trade stats by wallet address
const tradeStats = await getKolTradeStats("8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR");

// Get trade stats by label
const statsByLabel = await getKolTradeStats("casino");
```

**Example Response:**

```json
{
  "success": true,
  "data": {
    "totalTrades": 1250,
    "dataSince": "2024-01-01T00:00:00Z",
    "totalUniqueTokens": 45,
    "totalBuys": 750,
    "totalSells": 500,
    "totalVolumeTradedUsd": 2500000.50,
    "biggestWin": 150000.75,
    "biggestLoss": -25000.30,
    "winRate": 68.5,
    "walletCount": 3
  }
}
```

### Token Analytics

#### `getKolTokenStats(tokenAddress, timePeriod = '30 days')`

Gets basic KOL token statistics using PostgreSQL function `fn_get_kols_token_stats`.

- **Parameters:**
  - `tokenAddress` (string): The token mint address
  - `timePeriod` (string): Time period for stats (PostgreSQL interval format, default: `'30 days'`)
- **Returns:** Object with KOL token stats data

```jsx
import { getKolTokenStats } from "@/actions/kol-profile/getKolTokenStats";

// Get 30-day KOL stats for a token
const stats = await getKolTokenStats("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// Get 7-day stats
const weeklyStats = await getKolTokenStats("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", "7 days");
```

**Example Response:**

```json
{
  "status": "success",
  "data": [
    {
      "unique_kols_count": 8,
      "total_transactions_count": 53,
      "total_buy_volume": 33393.783209,
      "total_sell_volume": 39370.431330
    }
  ]
}
```

#### `getKolDetailedTokenStats(tokenAddress, timePeriod = '30 days', dustFilter = 0.001)`

Gets detailed KOL token statistics with dust filtering and real-time balance enrichment.

- **Parameters:**
  - `tokenAddress` (string): The token mint address
  - `timePeriod` (string): Time period for stats (PostgreSQL interval format, default: `'30 days'`)
  - `dustFilter` (number): Minimum value to filter out dust transactions (default: `0.001`)
- **Returns:** Object with detailed KOL token stats data enriched with real-time balances

```jsx
import { getKolDetailedTokenStats } from "@/actions/kol-profile/getKolTokenStats";

// Get detailed stats with default dust filter (0.1%)
const detailedStats = await getKolDetailedTokenStats("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// Use custom dust filter of 0.5%
const customStats = await getKolDetailedTokenStats(
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 
  "7 days", 
  0.005
);
```

**Example Response:**

```json
{
  "status": "success",
  "data": [
    {
      "wallet": "8rvAsDKeAcEjEkiZMug9k8v1y8mW6gQQiMobd89Uy7qR",
      "kol_label": "casino",
      "kol_avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/casino616.jpg",
      "kol_socials": [
        {
          "type": "x",
          "handle": "casino616",
          "followers": 51300
        }
      ],
      "buy_count": 7,
      "sell_count": 27,
      "total_transactions": 34,
      "buy_volume_usd": 10434.083826,
      "sell_volume_usd": 28411.494175,
      "net_volume_usd": 17977.410349,
      "total_volume_usd": 38845.578001,
      "total_bought_amount": 37137534.4490009989,
      "total_sold_amount": 37137534.44900099923,
      "unsold_token_amount": -0.00000000033,
      "dust_filtered_amount": 0,
      "sell_all": true,
      "realized_pnl_percent": 172.29505387146005089400,
      "first_buy": "2025-04-27T05:30:53Z",
      "first_sell": "2025-04-27T05:31:37Z",
      "hold_time_seconds": 44,
      "unsold_usd_amount": 0,
      "token_price": 0.5149018166090956,
      "pnl_percentage": 172.30
    }
  ]
}
```

#### `getKolTokenHoldings(tokenAddress, minUsdValue = 5)`

Gets current KOL token holdings for a specific token with real-time balance data.

- **Parameters:**
  - `tokenAddress` (string): The token mint address
  - `minUsdValue` (number): Minimum USD value to include KOL (default: `5`)
- **Returns:** Object with KOL token holdings data

```jsx
import { getKolTokenHoldings } from "@/actions/kol-profile/getKolTokenStats";

// Get KOL holdings with minimum $5 value
const holdings = await getKolTokenHoldings("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// Get holdings with minimum $100 value
const largeHoldings = await getKolTokenHoldings("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 100);
```

**Example Response:**

```json
{
  "status": "success",
  "data": {
    "tokenAddress": "Ey59PH7Z4BFU4HjyKnyMdWt5GGN76KazTAwQihoUXRnk",
    "minUsdValue": 5,
    "kolHoldersCount": 8,
    "totalUsdValue": 2271662.484552402,
    "totalTokenAmount": 10938179.536581578,
    "tokenPrice": 0.20768195264623956,
    "holdings": [
      {
        "wallet": "FvTBarKFhrnhL9Q55bSJnMmAdXisayUb5u96eLejhMF9",
        "label": "Scooter",
        "avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/scooter_420.jpg",
        "isPublic": false,
        "amount": 5041289.201448477,
        "rawAmount": "5041289201448477",
        "decimals": 9,
        "price": 0.20768195264623956,
        "usdValue": 1046984.7852112214
      },
      {
        "wallet": "8zFZHuSRuDpuAR7J6FzwyF3vKNx4CVW3DFHJerQhc7Zd",
        "label": "pow",
        "avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/pow.jpg",
        "isPublic": true,
        "amount": 2998559.79342211,
        "rawAmount": "2998559793422110",
        "decimals": 9,
        "price": 0.20768195264623956,
        "usdValue": 622746.7530244085
      },
      {
        "wallet": "DNfuF1L62WWyW3pNakVkyGGFzVVhj4Yr52jSmdTyeBHm",
        "label": "gake (pep arc)",
        "avatar": "https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/avatars/kol/ga__ke.jpg",
        "isPublic": true,
        "amount": 2007620.328839919,
        "rawAmount": "2007620328839919",
        "decimals": 9,
        "price": 0.20768195264623956,
        "usdValue": 416946.51006576
      }
    ]
  }
}
```

## Implementation Patterns

### Fast Page Loading with Metadata

Use `getMinimalKolMetadata` for fast initial page loads and SEO metadata, then fetch full data asynchronously:

```jsx
// app/(dashboard)/kol-profile/[identifier]/page.js
import { getMinimalKolMetadata } from "@/actions/kol-profile/getMinimalKolMetadata";
import { getKolMetadata } from "@/actions/kol-profile/getKolMetadata";

// Generate metadata for SEO (fast)
export async function generateMetadata({ params }) {
  const { identifier } = params;
  
  const metadata = await getMinimalKolMetadata(identifier);
  
  return {
    title: `${metadata.data.label} - KOL Profile | StalkChain`,
    description: metadata.data.description,
    openGraph: {
      title: `${metadata.data.label} - KOL Profile`,
      description: metadata.data.description,
      images: metadata.data.avatar ? [metadata.data.avatar] : [],
    },
  };
}

// Async component to fetch FULL KOL data
async function KolProfileData({ identifier }) {
  const kolProfileData = await getKolMetadata(identifier);

  return (
    <KolProfilePage
      kolData={kolProfileData.data}
      profileFound={kolProfileData.profileFound}
      profileError={kolProfileData.profileError}
    />
  );
}

export default function Page({ params }) {
  const { identifier } = params;
  
  return <KolProfileData identifier={identifier} />;
}
```

### KOL Profile Header Component

```jsx
// content/kol-profile/components/KolProfileHeader.js
import { useState, useEffect } from "react";
import { Avatar } from "@heroui/avatar";
import { Card, CardBody } from "@heroui/card";
import { getKolTradeStats } from "@/actions/kol-profile/getKolTradeStats";

export default function KolProfileHeader({ kolData, walletBalance }) {
  const [tradeStats, setTradeStats] = useState(null);
  const [loadingTradeStats, setLoadingTradeStats] = useState(false);

  useEffect(() => {
    const fetchTradeStats = async () => {
      setLoadingTradeStats(true);
      try {
        if (kolData?.wallet || kolData?.label) {
          const identifier = kolData.wallet || kolData.label;
          const response = await getKolTradeStats(identifier);

          if (response.success && response.data) {
            setTradeStats(response.data);
          }
        }
      } catch (error) {
        console.error("Error fetching trade stats:", error);
      } finally {
        setLoadingTradeStats(false);
      }
    };

    fetchTradeStats();
  }, [kolData?.wallet, kolData?.label]);

  return (
    <Card className="w-full border border-default-200 rounded-xl">
      <CardBody className="p-6">
        <div className="flex flex-col items-center mb-6">
          <Avatar
            src={kolData.avatar}
            alt={kolData.label}
            className="w-28 h-28 mb-4"
          />
          <h1 className="text-2xl font-bold">{kolData.label}</h1>
          
          {kolData.socials?.map((social) => (
            <a
              key={social.type}
              href={`https://${social.type === 'x' ? 'x.com' : 't.me'}/${social.handle}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-primary hover:underline"
            >
              @{social.handle}
            </a>
          ))}
        </div>

        {loadingTradeStats ? (
          <div className="animate-pulse">
            <div className="h-4 bg-default-200 rounded mb-2"></div>
            <div className="h-4 bg-default-200 rounded"></div>
          </div>
        ) : tradeStats ? (
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-default-600">Total Trades:</span>
              <span className="font-medium">{tradeStats.totalTrades}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-default-600">Win Rate:</span>
              <span className="font-medium">{tradeStats.winRate}%</span>
            </div>
            <div className="flex justify-between">
              <span className="text-default-600">Total Volume:</span>
              <span className="font-medium">${tradeStats.totalVolumeTradedUsd?.toLocaleString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-default-600">Tracking Since:</span>
              <span className="font-medium">
                {new Date(tradeStats.dataSince).toLocaleDateString()}
              </span>
            </div>
          </div>
        ) : null}
      </CardBody>
    </Card>
  );
}
```

### Token Analytics Implementation Example

```jsx
// Example implementation for token analytics dashboard
import { useState, useEffect } from "react";
import { Card, CardBody } from "@heroui/card";
import { Tabs, Tab } from "@heroui/tabs";
import { 
  getKolTokenStats, 
  getKolDetailedTokenStats, 
  getKolTokenHoldings 
} from "@/actions/kol-profile/getKolTokenStats";

export default function TokenAnalyticsExample({ tokenAddress }) {
  const [activeTab, setActiveTab] = useState("overview");
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState({
    overview: null,
    detailed: null,
    holdings: null
  });

  useEffect(() => {
    const fetchAllData = async () => {
      setLoading(true);
      try {
        const [overviewResult, detailedResult, holdingsResult] = await Promise.all([
          getKolTokenStats(tokenAddress, "30 days"),
          getKolDetailedTokenStats(tokenAddress, "30 days"),
          getKolTokenHoldings(tokenAddress, 10)
        ]);

        setData({
          overview: overviewResult.status === 'success' ? overviewResult.data[0] : null,
          detailed: detailedResult.status === 'success' ? detailedResult.data : [],
          holdings: holdingsResult.status === 'success' ? holdingsResult.data : null
        });
      } catch (error) {
        console.error("Error fetching KOL token analytics:", error);
      } finally {
        setLoading(false);
      }
    };

    if (tokenAddress) {
      fetchAllData();
    }
  }, [tokenAddress]);

  // Implementation details...
  return (
    <Card>
      <CardBody className="p-6">
        <h2 className="text-xl font-semibold mb-4">KOL Token Analytics</h2>
        {/* Render analytics data */}
      </CardBody>
    </Card>
  );
}
```

## Caching Strategy

The KOL actions use a sophisticated multi-layer caching strategy:

| Data Type | Cache Duration | Cache Layer | TTL Reset | Rationale |
|-----------|----------------|-------------|-----------|-----------|
| All KOL Profiles | 1 week | Redis SET + Individual Keys | No | Stable data, background sync |
| Individual KOL Profile | 24 hours | Redis | No | Moderate update frequency |
| KOL Trade Stats | 1 hour | Redis | No | Dynamic trading data |
| Token Analytics | No cache | Real-time | N/A | Always fresh for accuracy |

### Background Sync Process

The `getAllKolProfiles` function implements an intelligent background sync:

1. **Initial Load**: If cache is empty, sync from PostgreSQL (blocking)
2. **Subsequent Loads**: Return cached data immediately
3. **Background Sync**: Trigger non-blocking sync every hour
4. **Cooldown Protection**: Prevent multiple simultaneous syncs

## Database Schema

### PostgreSQL Functions

The KOL actions utilize several PostgreSQL functions for analytics:

#### `fn_get_kols_token_stats(p_token_mint, p_timeframe)`

Returns aggregate KOL statistics for a token:
- `unique_kols_count`: Number of distinct KOLs who traded
- `total_transactions_count`: Total number of transactions
- `total_buy_volume`: Total USD volume of buy transactions
- `total_sell_volume`: Total USD volume of sell transactions

#### `fn_get_kols_detailed_token_stats(p_token_mint, p_timeframe, p_dust_threshold)`

Returns detailed per-KOL statistics:
- Trading counts and volumes
- PnL calculations with dust filtering
- Hold time analysis
- Position status (open/closed)

### Redis Schema

```
# KOL Profiles Set
stalkchain:kol:profiles -> SET of wallet addresses

# Individual KOL Profile Data
stalkchain:kol:profiles:{wallet} -> JSON profile data

# KOL Label Cache
kol:label:{address} -> JSON profile data (24h TTL)

# Trade Stats Cache
kol:trade:stats:{identifier} -> JSON trade statistics (1h TTL)

# Background Sync Cooldown
stalkchain:kol:profiles:last_sync -> timestamp (1h TTL)
```

## Best Practices

### Performance Optimization

1. **Use getAllKolProfiles() for bulk operations:**
   ```jsx
   // Good: Single call for all KOL data
   const allKols = await getAllKolProfiles();
   const kolMap = allKols.data.reduce((acc, kol) => {
     acc[kol.wallet] = kol;
     return acc;
   }, {});
   
   // Avoid: Multiple individual calls
   // const kols = await Promise.all(addresses.map(addr => fetchKolApiMetadata(addr)));
   ```

2. **Parallel data fetching for analytics:**
   ```jsx
   // Fetch multiple analytics in parallel
   const [stats, detailed, holdings] = await Promise.all([
     getKolTokenStats(tokenAddress, "30 days"),
     getKolDetailedTokenStats(tokenAddress, "30 days"),
     getKolTokenHoldings(tokenAddress, 10)
   ]);
   ```

3. **Use appropriate time periods:**
   ```jsx
   // Common time periods
   const timePeriods = ['1 hour', '24 hours', '7 days', '30 days'];
   
   // PostgreSQL interval format
   await getKolTokenStats(tokenAddress, '7 days');
   await getKolDetailedTokenStats(tokenAddress, '1 month');
   ```

### Error Handling

1. **Graceful degradation:**
   ```jsx
   const kolProfile = await fetchKolApiMetadata(address);
   
   if (!kolProfile.success) {
     // Show fallback UI or default data
     return <KolProfileNotFound message={kolProfile.error} />;
   }
   ```

2. **Handle missing data:**
   ```jsx
   const stats = await getKolTokenStats(tokenAddress);
   
   if (stats.status !== 'success' || !stats.data.length) {
     return <div>No KOL activity found for this token</div>;
   }
   ```

### Component Integration

1. **Use memoization for expensive operations:**
   ```jsx
   const MemoizedKolProfile = memo(({ kolData }) => {
     const [tradeStats, setTradeStats] = useState(null);
     
     useEffect(() => {
       // Only fetch if kolData changes
       if (kolData?.wallet) {
         getKolTradeStats(kolData.wallet).then(setTradeStats);
       }
     }, [kolData?.wallet]);
     
     return <KolProfileDisplay kolData={kolData} tradeStats={tradeStats} />;
   });
   ```

2. **Implement proper loading states:**
   ```jsx
   const [loading, setLoading] = useState(true);
   const [data, setData] = useState(null);
   
   useEffect(() => {
     const fetchData = async () => {
       setLoading(true);
       try {
         const result = await getKolDetailedTokenStats(tokenAddress);
         setData(result.data);
       } finally {
         setLoading(false);
       }
     };
     
     fetchData();
   }, [tokenAddress]);
   ```

## Common Patterns

### KOL Profile Lookup

```jsx
// Multi-source KOL lookup with fallbacks
async function findKolProfile(identifier) {
  // Try direct wallet address first
  if (identifier.length === 44) {
    return await fetchKolApiMetadata(identifier);
  }
  
  // Try by label or social handle
  const allKols = await getAllKolProfiles();
  const kol = allKols.data.find(k => 
    k.label === identifier || 
    k.socials?.some(s => s.handle === identifier)
  );
  
  return kol ? { success: true, data: kol } : { success: false, error: 'KOL not found' };
}
```

### Token Analytics Dashboard

```jsx
// Comprehensive token analytics with KOL data
async function getTokenAnalytics(tokenAddress) {
  const [basicStats, detailedStats, holdings] = await Promise.all([
    getKolTokenStats(tokenAddress, '30 days'),
    getKolDetailedTokenStats(tokenAddress, '30 days', 0.001),
    getKolTokenHoldings(tokenAddress, 5)
  ]);
  
  return {
    overview: basicStats.data?.[0],
    kolPerformance: detailedStats.data || [],
    currentHoldings: holdings.data || { holdings: [] },
    summary: {
      activeKols: basicStats.data?.[0]?.unique_kols_count || 0,
      totalVolume: (basicStats.data?.[0]?.total_buy_volume || 0) + (basicStats.data?.[0]?.total_sell_volume || 0),
      holdersCount: holdings.data?.kolHoldersCount || 0,
      holdingsValue: holdings.data?.totalUsdValue || 0
    }
  };
}
```

## Troubleshooting

### Common Issues and Solutions

1. **Cache miss on getAllKolProfiles**
   - The function automatically syncs from PostgreSQL if cache is empty
   - Check PostgreSQL connection and `ref_kols_profiles` table

2. **fetchKolApiMetadata returns 404**
   - Function automatically falls back to PostgreSQL
   - Verify wallet address format and case sensitivity

3. **Real-time balance enrichment fails**
   - `getKolDetailedTokenStats` gracefully handles balance fetch failures
   - Returns 0 for `unsold_usd_amount` if balance unavailable

4. **Background sync not triggering**
   - Check Redis connection and cooldown key
   - Verify `setImmediate` is available in the environment

### Debugging Tips

- Enable detailed logging by checking console outputs in KOL actions
- Use Redis CLI to inspect cache keys and TTL values
- Monitor PostgreSQL function performance with `EXPLAIN ANALYZE`
- Check network connectivity for external API calls

## Related Resources

- [Wallet Balance Actions](./wallet.md) - Used for real-time balance enrichment
- [PostgreSQL KOL Functions Documentation](../../_dev_docs/Get%20KOL%20Feed%20history%20and%20stats.md)
- [Redis Caching Utilities](../../utils/cache/kolProfiles.js)
- [StalkChain Central API Documentation](TBD)
