---
description: >-
  StalkChain token actions provide a reliable interface for retrieving token data from
  the Solana blockchain, with built-in caching, fallback mechanisms, and price tracking.
---

# Token Actions

**Target Audience:** Frontend and backend developers implementing token-related features in StalkChain applications who need to fetch token data, prices, and charts.

## Quick Start Guide

```jsx
import { getTokenDetails, getTokenSupply } from "@/actions/tokens/getTokenDetails";
import { getTokenPrice, getTokenPrices, getSolPrice, getTokenPriceChart } from "@/actions/tokens/getTokenPrice";

// 1. Get token details (metadata, name, symbol, etc.)
const tokenData = await getTokenDetails("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// 2. Get token supply information (UI-adjusted amount)
const supply = await getTokenSupply("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// 3. Get current token price
const price = await getTokenPrice("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// 4. Get prices for multiple tokens efficiently
const tokenAddresses = [
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr",
  "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
];
const pricesData = await getTokenPrices(tokenAddresses);

// 5. Get current SOL price
const solPrice = await getSolPrice();

// 6. Get price chart for token (1d = 1 day)
const chart = await getTokenPriceChart("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", "1d");
```

## Data Flow Architecture

```mermaid
flowchart TB
    client[Client Request] --> action[Server Action]
    action --> cache{Redis Cache?}
    
    cache -->|Yes| fetchCache[Fetch from Cache]
    cache -->|No| dataFetch[Fetch Fresh Data]
    
    dataFetch --> primary{Primary API Success?}
    primary -->|Yes| process[Process Data]
    primary -->|No| fallback[Try Fallback API]
    
    fallback --> process
    fetchCache --> process
    
    process --> response[Response to Client]
    
    classDef client fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef action fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef cache fill:#fff8e1,stroke:#ff8f00,color:#ff8f00
    classDef data fill:#f3e5f5,stroke:#7b1fa2,color:#7b1fa2
    classDef process fill:#fce4ec,stroke:#c2185b,color:#c2185b
    
    class client client
    class action action
    class cache cache
    class fetchCache,dataFetch,primary,fallback data
    class process,response process
```

## Available Actions

### Token Information & Metadata

#### `getTokenDetails(mintAddress, format = "basic")`

Retrieves complete token information from the Solana Tracker API.

- **Parameters:**
  - `mintAddress` (string): The token's mint address
  - `format` (string, optional): Format of the response ("basic" or "full")
- **Returns:** Token metadata including name, symbol, icon, and other details

```jsx
import { getTokenDetails } from "@/actions/tokens/getTokenDetails";

// Example for POPCAT token
const tokenData = await getTokenDetails("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");
console.log(tokenData);
```

**Example Response:**

```json
{
  "name": "POPCAT",
  "symbol": "POPCAT",
  "mint": "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr",
  "uri": "https://arweave.net/IiX6OFxiM1wb8DOSidDSn_6KVHqCpwnshUzU8RU5EN8",
  "decimals": 9,
  "description": "POPCAT POP POP POP POP",
  "image": "https://image.solanatracker.io/proxy?url=https%3A%2F%2Farweave.net%2FA1etRNMKxhlNGTf-gNBtJ75QJJ4NJtbKh_UXQTlLXzI",
  "creators": {
    "name": "modsiw",
    "site": "czlabs"
  },
  "hasFileMetaData": true,
  "strictSocials": {}
}
```

#### `fetchTokenSymbol(address, options = {})`

Fetches just the token symbol with Redis caching (8-hour TTL).

- **Parameters:**
  - `address` (string): The token's mint address
  - `options` (object, optional): 
    - `useCache` (boolean): Whether to use Redis caching (default: `true`)
    - `resetTTL` (boolean): Whether to reset TTL when accessed (default: `true`)
- **Returns:** String containing the token symbol

```jsx
import { fetchTokenSymbol } from "@/actions/tokens/getTokenDetails";

// Basic usage
const symbol = await fetchTokenSymbol("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// With options
const symbolWithOptions = await fetchTokenSymbol(
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 
  {
    useCache: true,  // Whether to use Redis caching
    resetTTL: true   // Whether to reset TTL when accessed
  }
);
```

**Example Response:**

```
"POPCAT"
```

#### `getTokenSupply(mintAddress, options = {})`

Gets token supply information with 8-hour caching that resets on access. Returns UI-adjusted amount that already accounts for decimals.

- **Parameters:**
  - `mintAddress` (string): The token's mint address
  - `options` (object, optional): 
    - `useCache` (boolean): Whether to use Redis caching (default: `true`)
    - `resetTTL` (boolean): Whether to reset TTL when accessed (default: `true`)
- **Returns:** Object containing token supply data with both raw and UI-adjusted amounts

```jsx
import { getTokenSupply } from "@/actions/tokens/getTokenDetails";

// Basic usage
const supply = await getTokenSupply("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// With options
const supplyWithOptions = await getTokenSupply(
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 
  {
    useCache: true,  // Whether to use Redis caching
    resetTTL: true   // Whether to reset TTL when accessed
  }
);

if (supply.success) {
  console.log(`Total supply: ${supply.supply.uiAmount.toLocaleString()}`);
  console.log(`Decimals: ${supply.supply.decimals}`);
}
```

**Example Response:**

```json
{
  "success": true,
  "supply": {
    "amount": "999992095683905000",
    "decimals": 9,
    "uiAmount": 999992095.683905
  }
}
```

#### `getTokenMarketCap(address, options = {})`

Gets token market cap with Onchain API as primary source and Solana Tracker as fallback.

- **Parameters:**
  - `address` (string): The token's mint address
  - `options` (object, optional): 
    - `useCache` (boolean): Whether to use Redis caching (default: `true`)
- **Returns:** Number representing the token's market cap in USD

```jsx
import { getTokenMarketCap } from "@/actions/tokens/getTokenPrice";

// Basic usage
const marketCap = await getTokenMarketCap("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// With options
const marketCapWithOptions = await getTokenMarketCap(
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 
  {
    useCache: true  // Whether to use Redis caching
  }
);
```

**Example Response:**

```
504239708.69517875
```

### Price Data

#### `getSolPrice()`

Gets the current SOL price in USD from the Onchain API.

- **Parameters:** None
- **Returns:** Number representing the current SOL price in USD, or null if unavailable

```jsx
import { getSolPrice } from "@/actions/tokens/getTokenPrice";

const solPrice = await getSolPrice();
console.log(`Current SOL price: $${solPrice}`);
```

**Example Response:**

```
102.45
```

#### `getTokenPrice(tokenAddress)`

Gets current token price with automatic fallback mechanism.

- **Parameters:**
  - `tokenAddress` (string): The token's mint address
- **Returns:** Number representing the token price in USD, or null if unavailable

```jsx
import { getTokenPrice } from "@/actions/tokens/getTokenPrice";

// Example for POPCAT token
const price = await getTokenPrice("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");
console.log(`Current price: $${price}`);
```

**Example Response:**

```
0.5149018166090956
```

#### `getTokenPrices(tokenAddresses, options = {})`

Gets prices for multiple tokens in parallel batches with automatic spam token detection.

- **Parameters:**
  - `tokenAddresses` (string[]): Array of token mint addresses
  - `options` (object, optional): 
    - `noRetry` (boolean): If true, disables retry logic for API requests (faster but less reliable)
    - `timeout` (number): Request timeout in milliseconds (default: 3000ms)
- **Returns:** Object with prices and detailed processing statistics

```jsx
import { getTokenPrices } from "@/actions/tokens/getTokenPrice";

// Basic usage with multiple tokens
const tokenAddresses = [
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", // POPCAT
  "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", // BONK
  "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"  // USDC
];

const pricesResult = await getTokenPrices(tokenAddresses);
console.log(pricesResult.prices);

// With custom timeout (faster responses)
const fastPrices = await getTokenPrices(tokenAddresses, { timeout: 2000 });
```

**Example Response:**

```json
{
  "success": true,
  "prices": {
    "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr": 0.5149018166090956,
    "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263": 0.000031825918,
    "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v": 1.0002
  },
  "failed": [],
  "spamTokens": [
    {
      "mintAddress": "3TfzUdSGUidj8BfcnAJUiwmNZGEXGvWfxH7UTnGHwCMH",
      "ticker": "SCAM",
      "source": "solana-tracker-api",
      "reason": "Risk score 10 - bulk price check",
      "isManual": false
    }
  ],
  "stats": {
    "totalTokens": 3,
    "successCount": 3,
    "failedCount": 0,
    "spamTokensCount": 1,
    "onchainSuccessCount": 2,
    "trackerSuccessCount": 1,
    "batchesProcessed": 1,
    "processingTimeMs": 1250
  }
}
```

### Price Charts

#### `getTokenPriceChart(tokenAddress, period = '1d')`

Gets token price chart data for a specific time period.

- **Parameters:**
  - `tokenAddress` (string): The token's mint address
  - `period` (string, optional): Time period for the chart (default: `'1d'`)
    - Valid options: `'1h'`, `'1d'`, `'1w'`, `'1m'`, `'1y'`
- **Returns:** Object with chart data for the specified period

```jsx
import { getTokenPriceChart } from "@/actions/tokens/getTokenPrice";

// Get 1-day chart for POPCAT token
const dayChart = await getTokenPriceChart(
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 
  "1d"
);

// Get 1-week chart (different time interval)
const weekChart = await getTokenPriceChart(
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 
  "1w"
);
```

**Example Response:**

```json
{
  "period": "1d",
  "interval": "30m",
  "data": [
    {
      "open": 512052534.46161336,
      "close": 509987899.06604177,
      "low": 509987899.06604177,
      "high": 512052534.46161336,
      "volume": 8433.894382836628,
      "time": 1746873000
    },
    // ... additional data points
    {
      "open": 500034843.90904033,
      "close": 503399159.8571196,
      "low": 500034843.90904033,
      "high": 503399159.8571196,
      "volume": 49977.95080902943,
      "time": 1746959400
    }
  ],
  "dataPoints": 49
}
```

**Chart Time Periods:**

| Period | Description | Interval   | Data Points | Use Case                   |
| ------ | ----------- | ---------- | ----------- | -------------------------- |
| `1h`   | 1 hour      | 1 minute   | ~60         | Short-term price movements |
| `1d`   | 1 day       | 30 minutes | ~48         | Daily trading patterns     |
| `1w`   | 1 week      | 2 hours    | ~84         | Medium-term trends         |
| `1m`   | 1 month     | 12 hours   | ~60         | Monthly performance        |
| `1y`   | 1 year      | 1 week     | ~52         | Long-term analysis         |

#### `getTokenPriceCharts(tokenAddress, periods = ['1h', '1d', '1w', '1m', '1y'])`

Gets token price charts for multiple periods in parallel, improving performance.

- **Parameters:**
  - `tokenAddress` (string): The token's mint address
  - `periods` (array, optional): Array of time periods to fetch
    - Default: `['1h', '1d', '1w', '1m', '1y']`
- **Returns:** Object with chart data for each requested period

```jsx
import { getTokenPriceCharts } from "@/actions/tokens/getTokenPrice";

// Get all default charts
const allCharts = await getTokenPriceCharts("7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr");

// Get only specific charts (more efficient)
const specificCharts = await getTokenPriceCharts(
  "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr", 
  ["1h", "1d"]  // Only hourly and daily charts
);
```

**Example Response:**

```json
{
  "tokenAddress": "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr",
  "charts": {
    "1h": {
      "period": "1h",
      "interval": "1m",
      "data": [/* hourly data points */],
      "dataPoints": 60
    },
    "1d": {
      "period": "1d",
      "interval": "30m",
      "data": [/* daily data points */],
      "dataPoints": 49
    }
    // ... other periods if requested
  },
  "availablePeriods": ["1h", "1d"]
}
```

## Real-World Implementation Examples

### TokenPriceChart Component

The `TokenPriceChart` component is used throughout the application to display token market cap charts. It demonstrates how to use the `getTokenPriceChart` function with period selection.

```jsx
// components/charts/TokenPriceChart.js (partial)
import React, { useState, useEffect } from "react";
import { Chart as ChartJS, /* other imports */ } from "chart.js";
import { Line } from "react-chartjs-2";
import { Button, ButtonGroup } from "@heroui/button";
import { Spinner } from "@heroui/spinner";

import { formatCurrency } from "@/helpers/TextFormat";
import { getTokenPriceChart } from "@/actions/tokens/getTokenPrice";

// Time period options
const timePeriods = [
  { key: '1h', label: '1H' },
  { key: '1d', label: '1D' },
  { key: '1w', label: '1W' },
  { key: '1m', label: '1M' },
  { key: '1y', label: '1Y' },
];

const TokenPriceChart = ({ tokenAddress }) => {
  const [activePeriod, setActivePeriod] = useState('1d');
  const [chartData, setChartData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchChartData = async () => {
      if (!tokenAddress) return;
      
      setLoading(true);
      setError(null);
      
      try {
        // Fetch chart data for the selected period
        const data = await getTokenPriceChart(tokenAddress, activePeriod);
        
        if (data && data.data && data.data.length > 0) {
          // Process chart data for display
          // ...
          setChartData(chartDataset);
        } else {
          setError('No chart data available for this period');
        }
      } catch (err) {
        console.error('Error fetching chart data:', err);
        setError('Failed to load chart data');
      } finally {
        setLoading(false);
      }
    };

    fetchChartData();
  }, [tokenAddress, activePeriod]);

  return (
    <div className="w-full bg-white rounded-lg p-3 mb-2">
      {/* Period selection buttons */}
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-sm font-medium text-default-700">Market Cap</h3>
        <ButtonGroup size="sm" variant="flat">
          {timePeriods.map((period) => (
            <Button
              key={period.key}
              className={`px-2 min-w-8 ${activePeriod === period.key ? 'bg-default-100' : ''}`}
              onClick={() => setActivePeriod(period.key)}
            >
              {period.label}
            </Button>
          ))}
        </ButtonGroup>
      </div>
      
      {/* Chart display */}
      <div className="relative h-[300px]">
        {loading ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <Spinner size="sm" />
          </div>
        ) : error ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-default-500 text-sm">{error}</p>
          </div>
        ) : chartData ? (
          <Line data={chartData} options={chartOptions} height={300} />
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
            <p className="text-default-500 text-sm">No market cap data available</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TokenPriceChart;
```

### Wallet Balance Example

This is how `getTokenPrice` is used in the `getWalletBalance.js` action to calculate USD values for token balances:

```jsx
// actions/wallet/getWalletBalance.js (partial)
"use server";

import { getTokenBalancesForWallets } from "@/utils/helius";
import { getTokenPrice } from "@/actions/tokens/getTokenPrice";
import DOMPurify from 'isomorphic-dompurify';

/**
 * Get token balances for multiple wallets with USD value calculations
 * @param {string} mintAddress - The mint address of the token
 * @param {string[]} walletAddresses - Array of wallet addresses to check
 * @returns {Promise<Object>} Token balances with USD values for each wallet
 */
export async function getTokenUsdBalanceForWallets(mintAddress, walletAddresses) {
  // Sanitize inputs
  mintAddress = DOMPurify.sanitize(mintAddress);
  walletAddresses = walletAddresses.map(address => DOMPurify.sanitize(address));
  
  try {
    // Get token price first (we only need to do this once)
    const price = await getTokenPrice(mintAddress);
    
    // Process wallets in batches
    const processBatch = async (walletBatch) => {
      // Get token balances from Helius
      const batchBalanceData = await getTokenBalancesForWallets(mintAddress, walletBatch);
      
      // Calculate USD values for each wallet balance
      return batchBalanceData.balances.map(balance => {
        return {
          ...balance,
          price: price,
          usdValue: price ? balance.amount * price : null
        };
      });
    };
    
    // Process wallets and calculate totals
    // ...
  } catch (error) {
    console.error("Error in getTokenUsdBalanceForWallets:", error);
    return { success: false, error: error.message };
  }
}
```

### SolanaWallet Modal

The wallet balance modal uses token data to display the user's portfolio:

```jsx
// modals/SolanaWallet/walletBalance.js (partial)
'use client';

import { useEffect, useState } from "react";
import { Card, CardBody } from "@heroui/card";

import { SolanaToken } from "@/components/UI/SolanaAddress";
import { formatCurrency, formatNumber } from "@/helpers/TextFormat";

const TokenCard = ({ token }) => (
  <Card radius="sm" shadow="none" classNames={{ base: "border border-neutral-200" }}>
    <CardBody className="p-3">
      <div className="flex flex-col gap-1">
        <div className="flex flex-row items-center justify-between">
          <SolanaToken
            address={token.mint}
            savedSymbol={token.metadata?.ticker}
            savedLogo={token.metadata?.icon}
            savedMcap={token.marketCap}
            showMarketCap={true}
            showControls={true}
            size="small"
          />
          <div className="flex flex-col items-end">
            <span className="text-sm text-black">{formatCurrency(token.valueUsd || 0)}</span>
            <span className="text-xs text-neutral-500">
              {formatNumber(token.amount || 0, true, 2)}
            </span>
          </div>
        </div>
      </div>
    </CardBody>
  </Card>
);

export default function WalletBalance({ triggers }) {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch wallet balance data
    // ... token prices are already included in the API response
  }, [triggers.walletAddress]);

  // Render portfolio data
  // ...
}
```

## Caching Strategy

The token actions use a sophisticated caching strategy to optimize performance:

| Data Type      | Cache Duration | TTL Reset on Access | Rationale                                     |
|----------------|----------------|---------------------|-----------------------------------------------|
| Token Symbols  | 8 hours        | Yes                 | Rarely changes, keep frequently used in cache |
| Token Supply   | 8 hours        | Yes                 | Rarely changes, keep frequently used in cache |
| Market Cap     | 1 minute       | No                  | Volatile data needs regular updates           |
| Price Data     | 5 minutes      | No                  | Balance between freshness and performance     |
| SOL Price      | 10 minutes     | No                  | Updates frequently but caching improves perf  |

## Best Practices

### Performance Optimization

1. **Batch Requests:**
   - Use `getTokenPrices()` for fetching multiple token prices instead of individual `getTokenPrice()` calls
   - Use `getTokenPriceCharts()` instead of multiple `getTokenPriceChart()` calls
   - Fetch only the time periods you need

2. **Parallel Processing:**
   - Use `Promise.all()` for independent data fetching
   - Example: `const [details, price] = await Promise.all([getTokenDetails(addr), getTokenPrice(addr)]);`

3. **Smart Caching:**
   - Let the system handle caching; no need for client-side caching
   - Consider stale-while-revalidate patterns for UI responsiveness

4. **Optimize API Requests:**
   - Use shorter timeout values (2000-3000ms) for non-critical token price lookups
   - For bulk operations, set `{ timeout: 3000 }` in options to get faster responses

### Error Handling

1. **Graceful Degradation:**
   - All functions return `null` on error instead of throwing exceptions
   - Always handle potential `null` returns in your UI

2. **Provide Fallbacks:**
   ```jsx
   // Show fallback when data is missing
   <div>Price: {price ? `$${price.toFixed(6)}` : 'Price unavailable'}</div>
   ```

3. **Progressive Enhancement:**
   - Show core content even if some data fails to load
   - Use skeleton loaders during data fetching

### Common Patterns

#### Multiple Token Price Fetching

```jsx
'use client';

import { useState } from 'react';
import { getTokenPrices } from '@/actions/tokens/getTokenPrice';

export default function TokenPricesExample() {
  const [prices, setPrices] = useState({});
  const [loading, setLoading] = useState(false);
  const [stats, setStats] = useState(null);
  
  async function fetchPrices() {
    // Example list of popular tokens
    const popularTokens = [
      "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263", // BONK
      "7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU", // SAMO
      "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v", // USDC
      "Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB", // USDT
      "7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr"  // POPCAT
    ];
    
    setLoading(true);
    
    try {
      // Fetch all prices in one call with a short timeout for faster responses
      const result = await getTokenPrices(popularTokens, { timeout: 3000 });
      
      if (result.success) {
        setPrices(result.prices);
        setStats(result.stats);
      }
    } catch (error) {
      console.error('Error fetching token prices:', error);
    } finally {
      setLoading(false);
    }
  }
  
  // Render price table
  // ...
}
```

#### Token List with Search

```jsx
'use client';

import { useState } from 'react';
import { getTokenDetails } from '@/actions/tokens/getTokenDetails';
import { getTokenPrice } from '@/actions/tokens/getTokenPrice';

export default function TokenSearch() {
  const [query, setQuery] = useState('');
  const [tokenData, setTokenData] = useState(null);
  const [loading, setLoading] = useState(false);
  
  async function searchToken(e) {
    e.preventDefault();
    if (!query) return;
    
    setLoading(true);
    
    try {
      // Try getting token by address first
      const token = await getTokenDetails(query);
      
      if (token) {
        // If successful, also get the price
        const price = await getTokenPrice(query);
        setTokenData({ ...token, price });
      } else {
        setTokenData(null);
      }
    } catch (error) {
      console.error('Error searching for token:', error);
      setTokenData(null);
    } finally {
      setLoading(false);
    }
  }
  
  // Render search form and results
  // ...
}
```

## Troubleshooting

### Common Issues and Solutions

1. **Token price returns null**
   - Check if the token has liquidity on supported DEXes
   - Verify the token address is correct
   - Some tokens may not have price data available

2. **Slow data loading**
   - Implement loading states in your UI
   - Consider prefetching data for frequently accessed tokens
   - Use parallel data fetching with Promise.all()

3. **Market cap unavailable**
   - Not all tokens have market cap data
   - SOL, USDC, and USDT intentionally skip market cap fetching

### Debugging Tips

- Add `console.log()` statements to track data flow
- Check browser network tab for API call errors
- Remember that all functions return `null` on error rather than throwing exceptions

## Related Resources

- [Onchain API Documentation](../../../docs/api/stalkchain-onchain-api.md)
- [Solana Tracker API Documentation](https://docs.solanatracker.io/)
- [Redis Caching Strategy](TBD)

<!--
KEY PRINCIPLES:
* Clarity and Conciseness: Avoid technical jargon and ensure all documentation is easy to understand, using diagrams, flowcharts, and screenshots where appropriate. 
* Accuracy and Timeliness: Regularly update documentation to reflect changes in code, processes, or environments. Establish clear ownership and review processes to maintain accuracy.
* Lightweight and Focused: Avoid overly detailed documentation. Focus on what developers need to know to get their job done efficiently.

DOCUMENTATION RULES:
1. Add placeholders for screenshots ONLY if they will enhance the dev docs
2. Follow the key principles outlined above
3. Use "Docs for Developers" framework - task-based organization, progressive disclosure
4. Use example outputs, formatted in JSON, when applicable
5. Explain data structures, like columns of a postgres table, or returns from an API
6. Show imports on code blocks for imported components (e.g., include the import for StripeCheckoutButton.js when it's used in a code block)
7. For code blocks, only include file paths as comments (e.g., // components/Button.js) for ACTUAL files that exist in the codebase. Do NOT include file paths for fictional examples or hypothetical implementations.

FLOWCHART RULES:
1. Stick to "Visual Explanations" by Edward Tufte methodology
2. Use conceptual grouping judiciously - Group related elements into subgraphs only when complexity requires it; prefer a flat structure for simpler flows.
3. Establish clear visual hierarchy - Apply subtle color differences to distinguish process types; limit palette to 3-5 colors with consistent meaning.
4. Maintain directional clarity - Design flow in one primary direction (top-to-bottom or left-to-right) to minimize line crossings.
5. Balance information density - Include only essential details relevant to the current level of abstraction; create separate diagrams for deeper dives.
6. Apply consistent symbolism - Use standard shapes purposefully (diamonds for decisions, rectangles for processes) across all related diagrams.

AI RULES:
1. Gather all relevant information first before applying.
2. Stick to proven methods over theory.

TEMPLATE:
The code block below describes the top of every .md file.

```
---
description: >-
  Write a page description with a max length of 200 characters.
---

**Target audience:** Write 1-2 lines who this document is intended for and to do what with it.
```
-->