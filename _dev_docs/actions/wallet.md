---
description: >-
  StalkChain wallet actions provide robust interfaces for retrieving wallet portfolios, token balances,
  and asset valuations from the Solana blockchain with spam filtering and price calculation.
---

# Wallet Actions

**Target Audience:** Frontend and backend developers implementing wallet-related features in StalkChain applications who need to fetch wallet balances, portfolio data, and track token valuations.

## Quick Start Guide

```jsx
import { getWalletPortfolio, getTokenUsdBalanceForWallets } from "@/actions/wallet/getWalletBalance";

// 1. Get complete wallet portfolio with all tokens, prices, and valuations
const portfolio = await getWalletPortfolio("8NNgmNC75qCxrWuuZ7aaFhAXY5BhnrHyAcPvpGziD73A");

// 2. Get specific token balances across multiple wallets
const tokenAddress = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"; // USDC
const walletAddresses = [
  "8NNgmNC75qCxrWuuZ7aaFhAXY5BhnrHyAcPvpGziD73A",
  "86AEJExyjeNNgcp7GrAvCXTDicf5aGWgoERbXFiG1EdD"
];
const tokenBalances = await getTokenUsdBalanceForWallets(tokenAddress, walletAddresses);
```

## Available Actions

### Wallet Portfolio

#### `getWalletPortfolio(walletAddress)`

Retrieves a complete portfolio for a wallet including all tokens with accurate price information. The function automatically filters out spam tokens and low-value holdings (less than $5).

- **Parameters:**
  - `walletAddress` (string): The Solana wallet address to fetch portfolio for
- **Returns:** Comprehensive wallet portfolio with native SOL balance, token balances, prices, and USD values

##### Portfolio Data Flow

```mermaid
flowchart TB
    client[Client Request] --> action[Server Action]
    action --> helius[Fetch Wallet Data]
    
    helius --> tokenList[Token List]
    tokenList --> spam{Filter Spam}
    
    spam -->|Non-Spam Tokens| prices[Fetch Token Prices]
    spam -->|Spam Tokens| filter[Filter Out]
    
    prices --> lowValue{Check Value}
    lowValue -->|Value >= $5| portfolio[Build Portfolio]
    lowValue -->|Value < $5| filter
    
    filter --> portfolio
    portfolio --> response[Response to Client]
    
    classDef client fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef action fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef filter fill:#fff8e1,stroke:#ff8f00,color:#ff8f00
    classDef data fill:#f3e5f5,stroke:#7b1fa2,color:#7b1fa2
    classDef process fill:#fce4ec,stroke:#c2185b,color:#c2185b
    
    class client client
    class action,helius action
    class spam,lowValue filter
    class tokenList,prices data
    class portfolio,response,filter process
```

```jsx
import { getWalletPortfolio } from "@/actions/wallet/getWalletBalance";

// Get complete portfolio for a wallet
const portfolio = await getWalletPortfolio("8NNgmNC75qCxrWuuZ7aaFhAXY5BhnrHyAcPvpGziD73A");

// Access portfolio data
console.log(`Total Portfolio Value: $${portfolio.totalUsdValue.toFixed(2)}`);
console.log(`SOL Balance: ${portfolio.nativeBalance} SOL ($${portfolio.nativeSolUsdValue.toFixed(2)})`);
console.log(`Number of tokens: ${portfolio.tokens.length}`);
console.log(`Spam tokens filtered: ${portfolio.spamTokensCount}`);
```

**Example Response:**

```json
{
  "success": true,
  "walletAddress": "8NNgmNC75qCxrWuuZ7aaFhAXY5BhnrHyAcPvpGziD73A",
  "nativeBalance": 30.966276319,
  "nativeSolUsdValue": 5566.2708470021325,
  "tokens": [
    {
      "name": "USD Coin",
      "symbol": "USDC",
      "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      "amount": 5273.128895,
      "rawAmount": 5273128895,
      "supply": 8599077742.92447,
      "rawSupply": 8599077742924471,
      "decimals": 6,
      "frozen": false,
      "logo": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png",
      "description": null,
      "extensions": null,
      "price": 1.000197277765517,
      "usdValue": 5274.169166085689
    },
    {
      "name": "Moo Deng",
      "symbol": "MOODENG",
      "mint": "ED5nyyWEzpPPiWimP8vYm7sD7TD3LAt3Q3gRTWHzPJBY",
      "amount": 52909.586197,
      "rawAmount": 52909586197,
      "supply": 989940886.934326,
      "rawSupply": 989940886934326,
      "decimals": 6,
      "frozen": false,
      "logo": "https://ipfs.io/ipfs/Qmf1g7dJZNDJHRQru7E7ENwDjcvu7swMUB6x9ZqPXr4RV2",
      "description": "just a viral lil hippo",
      "extensions": null,
      "price": 0.2508631770649557,
      "usdValue": 13273.066890571547
    },
    // ... additional tokens
  ],
  "spamTokensCount": 33,
  "lowValueTokensCount": 73,
  "solPrice": 179.7526699581516,
  "tokensUsdValue": 43955.18163693242,
  "tokensSolValue": 244.53145339741363,
  "totalUsdValue": 49521.452483934554,
  "totalSolValue": 275.49772971641363
}
```

### Token Balances Across Multiple Wallets

#### `getTokenUsdBalanceForWallets(mintAddress, walletAddresses)`

Retrieves token balances for a specific token across multiple wallets, with USD value calculations.

- **Parameters:**
  - `mintAddress` (string): The token's mint address
  - `walletAddresses` (string[]): Array of wallet addresses to check
- **Returns:** Token balances with USD values for each wallet

```jsx
import { getTokenUsdBalanceForWallets } from "@/actions/wallet/getWalletBalance";

// Get USDC balances across multiple wallets
const usdcMint = "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v";
const wallets = [
  "8NNgmNC75qCxrWuuZ7aaFhAXY5BhnrHyAcPvpGziD73A",
  "86AEJExyjeNNgcp7GrAvCXTDicf5aGWgoERbXFiG1EdD",
  "9VxJw5ngvTfv3SkBZnfn2bMk8H29QXMgA6MfGtuHkZhx"
];

const balances = await getTokenUsdBalanceForWallets(usdcMint, wallets);
console.log(`Total USDC across wallets: ${balances.totalAmount.toFixed(2)}`);
console.log(`Total USD Value: $${balances.totalUsdValue.toFixed(2)}`);
```

**Example Response:**

```json
{
  "success": true,
  "mintAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
  "decimals": 6,
  "price": 1.000197277765517,
  "totalAmount": 5273.128895,
  "totalUsdValue": 5274.169166085689,
  "balances": [
    {
      "wallet": "8NNgmNC75qCxrWuuZ7aaFhAXY5BhnrHyAcPvpGziD73A",
      "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      "amount": 5273.128895,
      "rawAmount": "5273128895",
      "decimals": 6,
      "tokenProgram": "Token",
      "price": 1.000197277765517,
      "usdValue": 5274.169166085689
    },
    {
      "wallet": "86AEJExyjeNNgcp7GrAvCXTDicf5aGWgoERbXFiG1EdD",
      "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      "amount": 0,
      "rawAmount": "0",
      "decimals": 6,
      "tokenProgram": null,
      "price": 1.000197277765517,
      "usdValue": 0
    },
    {
      "wallet": "9VxJw5ngvTfv3SkBZnfn2bMk8H29QXMgA6MfGtuHkZhx",
      "mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
      "amount": 0,
      "rawAmount": "0",
      "decimals": 6,
      "tokenProgram": null,
      "price": 1.000197277765517,
      "usdValue": 0
    }
  ]
}
```

## Processing and Filtering Pipeline

The wallet actions implement a sophisticated processing pipeline to ensure data quality and performance:

| Stage | Description | Implementation |
|-------|-------------|----------------|
| Data Retrieval | Fetch raw wallet data from Helius API | Uses batched requests for multiple wallets |
| Spam Filtering | Filter out known spam tokens | Checks against spam token database and frozen tokens |
| Timeout Filtering | Skip tokens that previously timed out | Improves performance by avoiding slow API calls |
| Price Fetching | Get token prices from multiple sources | Uses bulk price fetching with fallbacks |
| Value Filtering | Filter out low-value tokens (< $5) | Reduces noise in portfolio views |
| Portfolio Building | Calculate totals and format response | Provides both token and native SOL values |

## Real-World Implementation Examples

### Wallet Overview Component

The `WalletOverview` component is used to display a user's portfolio in the wallet page:

```jsx
// components/wallet/WalletOverview.js
'use client';

import { useState, useEffect } from 'react';
import { Card, CardBody } from '@heroui/card';
import { Spinner } from '@heroui/spinner';
import { formatCurrency, formatNumber } from '@/helpers/TextFormat';
import { getWalletPortfolio } from '@/actions/wallet/getWalletBalance';
import { TokenCard } from '@/components/wallet/TokenCard';

const WalletOverview = ({ walletAddress }) => {
  const [portfolio, setPortfolio] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPortfolio = async () => {
      if (!walletAddress) return;
      
      setLoading(true);
      setError(null);
      
      try {
        const data = await getWalletPortfolio(walletAddress);
        
        if (data.success) {
          setPortfolio(data);
        } else {
          setError(data.error || 'Failed to load wallet portfolio');
        }
      } catch (err) {
        console.error('Error fetching wallet portfolio:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchPortfolio();
  }, [walletAddress]);

  if (loading) {
    return (
      <div className="flex justify-center p-8">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardBody className="p-4">
          <div className="text-danger">{error}</div>
        </CardBody>
      </Card>
    );
  }

  if (!portfolio) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Portfolio Summary */}
      <Card className="overflow-hidden">
        <CardBody className="p-4">
          <h2 className="text-xl font-semibold mb-2">Portfolio Summary</h2>
          <div className="flex flex-col gap-2">
            <div className="flex justify-between">
              <span className="text-default-600">Total Value:</span>
              <span className="font-medium">{formatCurrency(portfolio.totalUsdValue)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-default-600">SOL Balance:</span>
              <div className="text-right">
                <div>{formatNumber(portfolio.nativeBalance)} SOL</div>
                <div className="text-sm text-default-500">
                  {formatCurrency(portfolio.nativeSolUsdValue)}
                </div>
              </div>
            </div>
            <div className="flex justify-between">
              <span className="text-default-600">Tokens Value:</span>
              <span>{formatCurrency(portfolio.tokensUsdValue)}</span>
            </div>
          </div>
        </CardBody>
      </Card>

      {/* Tokens List */}
      <div className="space-y-3">
        <h2 className="text-xl font-semibold">Tokens ({portfolio.tokens.length})</h2>
        
        {portfolio.tokens.map(token => (
          <TokenCard key={token.mint} token={token} />
        ))}
        
        {portfolio.tokens.length === 0 && (
          <Card>
            <CardBody className="p-4 text-center text-default-500">
              No tokens found in this wallet
            </CardBody>
          </Card>
        )}
        
        {(portfolio.spamTokensCount > 0 || portfolio.lowValueTokensCount > 0) && (
          <div className="text-sm text-default-500 mt-2">
            {portfolio.spamTokensCount > 0 && (
              <div>{portfolio.spamTokensCount} spam tokens filtered</div>
            )}
            {portfolio.lowValueTokensCount > 0 && (
              <div>{portfolio.lowValueTokensCount} low value tokens hidden (< $5)</div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default WalletOverview;
```

### KOL Token Holdings Table

The `KolTokenTable` component uses `getTokenUsdBalanceForWallets` to display Key Opinion Leader (KOL) holdings for a specific token. This is a critical feature for showing which influencers are holding a particular token and their position sizes:

```jsx
// actions/kol-profile/getKolTokenStats.js
export async function getKolDetailedTokenStats(tokenAddress, timePeriod = '30 days', dustFilter = 0.001) {
  try {
    // Database query to get KOL trading activity
    const result = await query('SELECT * FROM fn_get_kols_detailed_token_stats($1, $2, $3)', [
      sanitizedToken, 
      sanitizedPeriod,
      dustFilterValue
    ]);
    
    // Get all KOL wallet addresses for balance check
    const walletAddresses = result.rows.map(row => row.wallet).filter(Boolean);
    
    // Use getTokenUsdBalanceForWallets to get current token balances for all KOLs
    let walletBalances = {};
    if (walletAddresses.length > 0) {
      const balanceResult = await getTokenUsdBalanceForWallets(sanitizedToken, walletAddresses);
      
      if (balanceResult.success && balanceResult.balances) {
        // Create a lookup map of wallet to balance data
        walletBalances = balanceResult.balances.reduce((acc, balance) => {
          acc[balance.wallet] = balance;
          return acc;
        }, {});
      }
    }

    // Add real-time balance data to each row
    const enrichedData = result.rows.map(row => {
      // Get wallet balance from our balance lookup
      const walletBalance = walletBalances[row.wallet] || null;
      
      // Use real-time balance data if available, otherwise use 0
      const unsoldUsdAmount = walletBalance ? walletBalance.usdValue : 0;
      const tokenPrice = walletBalance ? walletBalance.price : 0;
      
      // Calculate PnL percentage including remaining tokens
      let pnlPercentage = null;
      if (buyVolumeUsd > 0) {
        pnlPercentage = ((sellVolumeUsd + unsoldUsdAmount) / buyVolumeUsd - 1) * 100;
      }
      
      return {
        ...row,
        unsold_usd_amount: unsoldUsdAmount,
        token_price: tokenPrice || 0,
        pnl_percentage: pnlPercentage !== null ? parseFloat(pnlPercentage.toFixed(2)) : null
      };
    });

    return {
      status: 'success',
      data: enrichedData
    };
  } catch (error) {
    // Error handling
  }
}
```

The `KolTokenTable` component displays this data in a clean, interactive table:

```jsx
// components/tables/kolTokenTable.js
"use client";

import { useEffect, useState } from "react";
import { getKolDetailedTokenStats } from "@/actions/kol-profile/getKolTokenStats";

const KolTokenTable = ({ tokenAddress = null, data = null, isLoading: externalLoading = false }) => {
  const [tableData, setTableData] = useState(data);
  const [isLoading, setIsLoading] = useState(externalLoading || !data);
  const [error, setError] = useState(null);

  // Fetch data if tokenAddress is provided but no data
  useEffect(() => {
    const fetchData = async () => {
      // If no token address, can't fetch data
      if (!tokenAddress) {
        return;
      }

      try {
        setIsLoading(true);
        
        // Fetch detailed stats which internally uses getTokenUsdBalanceForWallets
        const detailedResult = await getKolDetailedTokenStats(tokenAddress);
        
        if (detailedResult) {
          setTableData(detailedResult.data);
          setIsLoading(false);
        }
      } catch (err) {
        console.error("Error fetching KOL data:", err);
        setError(err instanceof Error ? err.message : "An unknown error occurred");
        setIsLoading(false);
      }
    };

    fetchData();
  }, [tokenAddress, data, externalLoading]);

  // Render KOL token table
  // ...
};
```

This table is used in the token modal to show KOL holdings:

```jsx
// modals/SolanaToken/tokenKol.js
export default function TokenKol({ triggers }) {
  // ...
  
  return (
    <div className="container px-0 py-0 mb-0 space-y-6">
      {/* Stats Card - Progressively Load */}
      <SummaryCard 
        data={basicStats?.data?.[0]} 
        isLoading={statsLoading} 
      />

      {/* Use the KolTokenTable component that uses getTokenUsdBalanceForWallets internally */}
      <KolTokenTable 
        tokenAddress={triggers.tokenAddress}
        isModal={true}
      />
    </div>
  );
}
```

### Wallet Balance Modal

The Solana Wallet modal uses the `getWalletPortfolio` function to display the user's token balances:

```jsx
// modals/SolanaWallet/walletBalance.js
const RenderOutput = ({ data }) => {
  if (!data) return null;
  
  // Extract the portfolio data from the API response
  const portfolioData = data;
  
  // Sort tokens by USD value in descending order
  const sortedTokens = [...(portfolioData.tokens || [])].sort((a, b) => 
    (b.usdValue || 0) - (a.usdValue || 0)
  );
  
  return (
    <div className="space-y-4">
      {/* Total Portfolio Value Card */}
      <Card className="relative overflow-hidden border border-neutral-200">
        <CardBody className="p-6 relative">
          <h2 className="text-neutral-500 text-base">Total Portfolio Value</h2>
          <div className="my-1">
            <span className="text-xl text-black break-words">
              {formatCurrency(portfolioData.totalUsdValue ?? 0)}
            </span>
            <span className="ml-2 text-sm text-neutral-500 break-words">
              ({formatNumber(portfolioData.totalSolValue ?? 0, false, 3)} SOL)
            </span>
          </div>
        </CardBody>
      </Card>

      {/* Tokens Grid */}
      <div className="grid grid-cols-1 gap-2">
        {sortedTokens.map(token => (
          <TokenCard key={token.mint} token={token} />
        ))}
      </div>
    </div>
  );
};
```

## Best Practices

### Performance Optimization

1. **Batch Processing:**
   - The wallet actions automatically batch process requests to avoid rate limits
   - For large-scale operations, consider implementing your own pagination

2. **Data Filtering:**
   - The default filter for low-value tokens is $5 (MIN_TOKEN_BALANCE_USD constant)
   - This can be adjusted if needed by modifying the constant in your own implementation

3. **Timeout Handling:**
   - Tokens that timeout during price fetching are automatically tracked and skipped in future requests
   - Default API timeout is 2.5 seconds to balance speed with thoroughness

4. **Caching Considerations:**
   - Consider implementing front-end caching for wallet data that doesn't change frequently
   - Implement data refresh buttons for users to manually trigger updates

### Error Handling

1. **Graceful Degradation:**
   - All functions return a success status and error message when applicable
   - Structure your UI to show partial data even if some aspects fail

2. **Handling Missing Data:**
   ```jsx
   // Show fallback for missing USD values
   <div>
     Value: {balance.usdValue ? formatCurrency(balance.usdValue) : 'Price unavailable'}
   </div>
   ```

3. **Loading States:**
   - Implement proper loading states for asynchronous data fetching
   - Consider skeleton loaders for a better user experience

### Common Patterns

#### Wallet and Token Selection

```jsx
'use client';

import { useState } from 'react';
import { getWalletPortfolio } from '@/actions/wallet/getWalletBalance';

export default function WalletViewer() {
  const [walletAddress, setWalletAddress] = useState('');
  const [portfolio, setPortfolio] = useState(null);
  const [loading, setLoading] = useState(false);
  
  async function fetchWalletData(e) {
    e.preventDefault();
    if (!walletAddress) return;
    
    setLoading(true);
    
    try {
      const data = await getWalletPortfolio(walletAddress);
      setPortfolio(data);
    } catch (error) {
      console.error('Error fetching wallet data:', error);
    } finally {
      setLoading(false);
    }
  }
  
  // Render form and results
  // ...
}
```

## Troubleshooting

### Common Issues and Solutions

1. **Wallet data returns empty tokens array**
   - Check if the wallet has any tokens with value above $5
   - Verify the wallet address is correct
   - Some tokens may be filtered out as spam or low value

2. **Token prices are null or inaccurate**
   - Newly created tokens may not have price data yet
   - Tokens with low liquidity may have unreliable price data
   - Check if the token is marked as spam

3. **Large wallet portfolios load slowly**
   - Wallets with many tokens require more API calls
   - Consider implementing a loading state with progress indicators
   - Cache results when appropriate

### Debugging Tips

- Check the `success` property in responses to determine if the operation completed successfully
- Review the `spamTokensCount` and `lowValueTokensCount` values to understand filtered tokens
- Implement detailed logging for tracking data flow through your application

## Related Resources

- [Token Actions Documentation](./tokens.md)
- [Helius API Documentation](https://docs.helius.xyz/)
- [Solana Web3.js Documentation](https://solana-labs.github.io/solana-web3.js/) 