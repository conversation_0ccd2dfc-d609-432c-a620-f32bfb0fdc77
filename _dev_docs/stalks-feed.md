# Stalks Feed Transactions Usage Guide

This document details how to use the database functions for managing Stalks feed transactions. The Stalks system provides similar functionality to the KOL feed system but for user-defined wallets of interest.

## Table of Contents

- [Function Overview](#function-overview)
- [Stalks Management Functions](#stalks-management-functions)
  - [Adding Stalks Transactions](#adding-stalks-transactions)
  - [Adding DCA Transactions](#adding-dca-transactions)
  - [Retrieving Stalks Transactions](#retrieving-stalks-transactions)
  - [Retrieving Stalks DCA Transactions](#retrieving-dca-transactions)
  - [Adding or Updating Stalks](#adding-or-updating-stalks)
  - [Deleting Stalk Wallets](#deleting-stalk-wallets)
- [JavaScript Integration](#javascript-integration)

## Function Overview

The Stalks system provides the following core functions for tracking and managing wallet data:

**Transaction Management**: Function to record transaction data for tracked wallets.

**Wallet Management**: Functions to add, update, and remove tracked wallets from a user's portfolio.

## Stalks Management Functions

### Adding Stalks Transactions

This function inserts transaction records into the Stalks feed system.

```sql
fn_insert_stream_stalks_feed_transactions(
    p_signature varchar, 
    p_type varchar, 
    p_time timestamptz, 
    p_slot int8, 
    p_wallet varchar, 
    p_input_mint varchar, 
    p_input_amount numeric, 
    p_output_mint varchar, 
    p_output_amount numeric, 
    p_input_usd_price numeric, 
    p_input_amount_usd numeric, 
    p_input_market_cap numeric, 
    p_input_mint_metadata jsonb, 
    p_output_usd_price numeric, 
    p_output_amount_usd numeric, 
    p_output_market_cap numeric, 
    p_output_mint_metadata jsonb, 
    p_total_usd_value numeric, 
    p_is_kol bool
)
```

#### Parameters

| Parameter Name | Data Type | Description |
|---------------|-----------|-------------|
| `p_signature` | VARCHAR | Transaction signature |
| `p_type` | VARCHAR | Transaction type ('buy' or 'sell') |
| `p_time` | TIMESTAMPTZ | Transaction timestamp |
| `p_slot` | INT8 | Blockchain slot number |
| `p_wallet` | VARCHAR | Wallet address |
| `p_input_mint` | VARCHAR | Address of the input (source) token |
| `p_input_amount` | NUMERIC | Amount of input token |
| `p_output_mint` | VARCHAR | Address of the output (target) token |
| `p_output_amount` | NUMERIC | Amount of output token |
| `p_input_usd_price` | NUMERIC | USD price of the input token |
| `p_input_amount_usd` | NUMERIC | USD value of the input amount |
| `p_input_market_cap` | NUMERIC | Market cap of the input token |
| `p_input_mint_metadata` | JSONB | Metadata for the input token |
| `p_output_usd_price` | NUMERIC | USD price of the output token |
| `p_output_amount_usd` | NUMERIC | USD value of the output amount |
| `p_output_market_cap` | NUMERIC | Market cap of the output token |
| `p_output_mint_metadata` | JSONB | Metadata for the output token |
| `p_total_usd_value` | NUMERIC | Total USD value of the transaction |
| `p_is_kol` | BOOLEAN | Flag indicating if the wallet belongs to a KOL |

### Adding DCA Transactions

This function inserts Dollar Cost Averaging (DCA) transaction records into the Stalks feed system. DCA transactions represent automated recurring purchases of an asset over time.

```sql
fn_insert_stalks_dca_transaction(
    p_signature varchar,
    p_wallet varchar,
    p_time timestamptz,
    p_trx_type varchar,
    p_cycle_frequency integer,
    p_min_out_amount numeric,
    p_max_out_amount numeric,
    p_start_at integer,
    p_sol_price numeric,
    p_in_amount_in_sol numeric,
    p_input_mint varchar,
    p_input_amount numeric,
    p_input_usd_price numeric,
    p_input_amount_usd numeric,
    p_input_market_cap numeric,
    p_input_mint_metadata jsonb,
    p_input_amount_per_cycle numeric,
    p_input_market_impact numeric,
    p_input_price_in_sol numeric,
    p_output_mint varchar,
    p_output_amount numeric,
    p_output_usd_price numeric,
    p_output_amount_usd numeric,
    p_output_market_cap numeric,
    p_output_mint_metadata jsonb,
    p_output_market_impact numeric,
    p_output_price_in_sol numeric,
    p_total_usd_value numeric,
    p_is_kol bool
)
```

#### Parameters

| Parameter Name | Data Type | Description |
|---------------|-----------|-------------|
| `p_signature` | VARCHAR | Transaction signature (unique identifier) |
| `p_wallet` | VARCHAR | Wallet address initiating the DCA |
| `p_time` | TIMESTAMPTZ | Transaction timestamp |
| `p_trx_type` | VARCHAR | Transaction type (e.g., 'openDcaV2') |
| `p_cycle_frequency` | INTEGER | DCA cycle frequency in minutes |
| `p_min_out_amount` | NUMERIC | Minimum output amount per cycle |
| `p_max_out_amount` | NUMERIC | Maximum output amount per cycle |
| `p_start_at` | INTEGER | Start time as Unix timestamp |
| `p_sol_price` | NUMERIC | SOL price at transaction time |
| `p_in_amount_in_sol` | NUMERIC | Input amount converted to SOL |
| `p_input_mint` | VARCHAR | Address of the input token |
| `p_input_amount` | NUMERIC | Total amount of input token |
| `p_input_usd_price` | NUMERIC | USD price of the input token |
| `p_input_amount_usd` | NUMERIC | USD value of the input amount |
| `p_input_market_cap` | NUMERIC | Market cap of the input token |
| `p_input_mint_metadata` | JSONB | Metadata for the input token (symbol, name, image) |
| `p_input_amount_per_cycle` | NUMERIC | Amount of input token per DCA cycle |
| `p_input_market_impact` | NUMERIC | Market impact of the input token |
| `p_input_price_in_sol` | NUMERIC | Price of input token in SOL |
| `p_output_mint` | VARCHAR | Address of the output token |
| `p_output_amount` | NUMERIC | Expected amount of output token |
| `p_output_usd_price` | NUMERIC | USD price of the output token |
| `p_output_amount_usd` | NUMERIC | Expected USD value of the output amount |
| `p_output_market_cap` | NUMERIC | Market cap of the output token |
| `p_output_mint_metadata` | JSONB | Metadata for the output token (symbol, name, image) |
| `p_output_market_impact` | NUMERIC | Market impact of the output token |
| `p_output_price_in_sol` | NUMERIC | Price of output token in SOL |
| `p_total_usd_value` | NUMERIC | Total USD value of the DCA transaction |
| `p_is_kol` | BOOLEAN | Flag indicating if the wallet belongs to a KOL |

#### DCA Transactions Table Structure

The DCA transactions are stored in a partitioned table optimized for time-based queries:

```sql
CREATE TABLE stream_stalks_dca_transactions (
    -- Common transaction identifiers
    signature VARCHAR,
    wallet VARCHAR NOT NULL,
    "time" TIMESTAMPTZ NOT NULL,
    
    -- DCA specific fields
    transaction_type VARCHAR NOT NULL DEFAULT 'dca',
    trx_type VARCHAR NOT NULL DEFAULT 'openDcaV2',
    cycle_frequency INTEGER,
    min_out_amount NUMERIC,
    max_out_amount NUMERIC,
    start_at INTEGER NOT NULL DEFAULT 0,
    sol_price NUMERIC,
    in_amount_in_sol NUMERIC,
    
    -- Token input data
    input_mint VARCHAR,
    input_amount NUMERIC,
    input_usd_price NUMERIC,
    input_amount_usd NUMERIC,
    input_market_cap NUMERIC,
    input_mint_metadata JSONB,
    input_amount_per_cycle NUMERIC,
    input_market_impact NUMERIC,
    input_price_in_sol NUMERIC,
    
    -- Token output data
    output_mint VARCHAR,
    output_amount NUMERIC,
    output_usd_price NUMERIC,
    output_amount_usd NUMERIC,
    output_market_cap NUMERIC,
    output_mint_metadata JSONB,
    output_market_impact NUMERIC,
    output_price_in_sol NUMERIC,
    
    -- Transaction metadata
    total_usd_value NUMERIC,
    is_kol BOOLEAN DEFAULT FALSE,
    modified_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    PRIMARY KEY (signature, "time")
) PARTITION BY RANGE ("time");
```

The table is partitioned by the `time` field to ensure efficient querying over specific time ranges.

### Retrieving Stalks Transactions

This function retrieves transaction records from the Stalks feed system with filtering options.

```sql
fn_get_stalks_feed(
    p_user_id UUID,
    p_wallets TEXT[] DEFAULT NULL,
    p_tracking_type TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_from_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_to_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_min_amount NUMERIC DEFAULT NULL,
    p_token_symbol TEXT DEFAULT NULL,
    p_transaction_type TEXT DEFAULT NULL
)
```

#### Parameters

| Parameter Name | Data Type | Description |
|---------------|-----------|-------------|
| `p_user_id` | UUID | The user ID to retrieve transactions for |
| `p_wallets` | TEXT[] | Optional array of specific wallet addresses to filter by |
| `p_tracking_type` | TEXT | Transaction tracking type to filter by (e.g., 'swap') |
| `p_limit` | INTEGER | Maximum number of records to return (default: 50) |
| `p_offset` | INTEGER | Offset for pagination (default: 0) |
| `p_from_date` | TIMESTAMPTZ | Start date for filtering transactions |
| `p_to_date` | TIMESTAMPTZ | End date for filtering transactions |
| `p_min_amount` | NUMERIC | Minimum USD value for transactions |
| `p_token_symbol` | TEXT | Filter by token symbol (matches input or output token) |
| `p_transaction_type` | TEXT | Filter by transaction type ('buy' or 'sell') |

#### Return Values

| Column Name | Data Type | Description |
|---------------|-----------|-------------|
| `signature` | TEXT | Transaction signature |
| `wallet_address` | TEXT | Wallet address |
| `custom_label` | TEXT | User's custom label for the wallet |
| `tracking` | TEXT[] | Tracking options configured for this wallet |
| `is_kol` | BOOLEAN | Whether this is a KOL wallet |
| `transaction_type` | TEXT | Transaction type ('buy' or 'sell') |
| `input_mint` | TEXT | Address of the input token |
| `input_mint_metadata` | JSONB | Metadata for the input token |
| `output_mint` | TEXT | Address of the output token |
| `output_mint_metadata` | JSONB | Metadata for the output token |
| `input_amount` | NUMERIC | Amount of input token |
| `output_amount` | NUMERIC | Amount of output token |
| `input_amount_usd` | NUMERIC | USD value of the input amount |
| `output_amount_usd` | NUMERIC | USD value of the output amount |
| `total_usd_value` | NUMERIC | Total USD value of the transaction |
| `time` | TIMESTAMPTZ | Transaction timestamp |
| `slot` | BIGINT | Blockchain slot number |

### Retrieving DCA Transactions

This function retrieves DCA transaction records with comprehensive filtering options:

```sql
fn_get_stalks_dca_feed(
    p_user_id UUID,
    p_wallets TEXT[] DEFAULT NULL,
    p_tracking_type TEXT DEFAULT NULL,
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0,
    p_from_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_to_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_min_amount NUMERIC DEFAULT NULL,
    p_token_symbol TEXT DEFAULT NULL
)
```

#### Parameters

| Parameter Name | Data Type | Description |
|---------------|-----------|-------------|
| `p_user_id` | UUID | User ID requesting transactions |
| `p_wallets` | TEXT[] | Optional wallet addresses to filter by |
| `p_tracking_type` | TEXT | Transaction tracking type (e.g., 'dca') |
| `p_limit` | INTEGER | Maximum records to return (default: 50) |
| `p_offset` | INTEGER | Pagination offset (default: 0) |
| `p_from_date` | TIMESTAMPTZ | Start date for filtering transactions |
| `p_to_date` | TIMESTAMPTZ | End date for filtering transactions |
| `p_min_amount` | NUMERIC | Minimum USD value threshold |
| `p_token_symbol` | TEXT | Filter by input or output token symbol |

#### Return Values

| Column Name | Data Type | Description |
|---------------|-----------|-------------|
| `signature` | TEXT | Transaction signature |
| `wallet_address` | TEXT | Wallet address |
| `custom_label` | TEXT | User's custom label for the wallet |
| `tracking` | TEXT[] | Tracking options configured for this wallet |
| `is_kol` | BOOLEAN | Whether this is a KOL wallet |
| `transaction_type` | TEXT | Transaction type ('dca') |
| `trx_type` | TEXT | Specific DCA transaction type (e.g., 'openDcaV2') |
| `cycle_frequency` | INTEGER | DCA cycle frequency in minutes |
| `min_out_amount` | NUMERIC | Minimum output amount for DCA |
| `max_out_amount` | NUMERIC | Maximum output amount for DCA |
| `start_at` | INTEGER | Start time as Unix timestamp |
| `sol_price` | NUMERIC | SOL price at transaction time |
| `in_amount_in_sol` | NUMERIC | Input amount converted to SOL |
| `input_mint` | TEXT | Address of the input token |
| `input_mint_metadata` | JSONB | Metadata for the input token |
| `output_mint` | TEXT | Address of the output token |
| `output_mint_metadata` | JSONB | Metadata for the output token |
| `input_amount` | NUMERIC | Amount of input token |
| `output_amount` | NUMERIC | Amount of output token |
| `input_amount_usd` | NUMERIC | USD value of the input amount |
| `output_amount_usd` | NUMERIC | USD value of the output amount |
| `input_amount_per_cycle` | NUMERIC | Amount of input token per DCA cycle |
| `input_market_impact` | NUMERIC | Market impact of the input token |
| `input_price_in_sol` | NUMERIC | Price of input token in SOL |
| `output_market_impact` | NUMERIC | Market impact of the output token |
| `output_price_in_sol` | NUMERIC | Price of output token in SOL |
| `total_usd_value` | NUMERIC | Total USD value of the transaction |
| `time` | TIMESTAMPTZ | Transaction timestamp |

### Adding or Updating Stalks

This function adds a new wallet to track or updates an existing tracked wallet's configuration.

```sql
fn_upsert_stalks_wallets(
    p_user_id uuid, 
    p_wallet_address text, 
    p_custom_label text, 
    p_alerts jsonb, 
    p_is_kol bool, 
    p_tracking _text
)
```

#### Parameters

| Parameter Name | Data Type | Description |
|---------------|-----------|-------------|
| `p_user_id` | UUID | The user ID who is tracking this wallet |
| `p_wallet_address` | TEXT | The wallet address to track |
| `p_custom_label` | TEXT | Custom label for the wallet |
| `p_alerts` | JSONB | Alert configuration for this wallet |
| `p_is_kol` | BOOLEAN | Flag indicating if this is a KOL wallet |
| `p_tracking` | TEXT[] | Array of tracking options |

### Deleting Stalk Wallets

This function removes a wallet from a user's tracked Stalks.

```sql
fn_delete_stalks_wallets(
    p_user_id uuid, 
    p_wallet_address text
)
```

#### Parameters

| Parameter Name | Data Type | Description |
|---------------|-----------|-------------|
| `p_user_id` | UUID | The user ID who is tracking this wallet |
| `p_wallet_address` | TEXT | The wallet address to remove from tracking |

## JavaScript Integration

Here's how to integrate these functions with your JavaScript application:

```javascript
const { Pool } = require('pg');

// Create connection pool
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
});

/**
 * Add or update a wallet to track
 * @param {string} userId - User ID
 * @param {string} walletAddress - Wallet address to track
 * @param {string} customLabel - Optional custom label
 * @param {Object} alerts - Alert configuration
 * @param {boolean} isKol - Whether this is a KOL wallet
 * @param {Array<string>} tracking - Tracking options
 * @returns {Promise<Object>} Result of the operation
 */
async function upsertStalksWallet(userId, walletAddress, customLabel = null, alerts = {}, isKol = false, tracking = []) {
  const query = `
    SELECT fn_upsert_stalks_wallets(
      $1::uuid, $2::text, $3::text, $4::jsonb, $5::boolean, $6::_text
    )
  `;
  const result = await pool.query(query, [
    userId, walletAddress, customLabel, alerts, isKol, tracking
  ]);
  return result.rows[0] || null;
}

/**
 * Insert a DCA transaction
 * @param {Object} txData - DCA transaction data containing all required fields
 * @returns {Promise<void>}
 */
async function insertStalksDcaTransaction(txData) {
  const query = `
    SELECT fn_insert_stalks_dca_transaction(
      $1::varchar, $2::varchar, $3::timestamptz, 
      $4::varchar, $5::integer, $6::numeric, $7::numeric, 
      $8::integer, $9::numeric, $10::numeric,
      $11::varchar, $12::numeric, $13::numeric, $14::numeric,
      $15::numeric, $16::jsonb, $17::numeric, $18::numeric, $19::numeric,
      $20::varchar, $21::numeric, $22::numeric, $23::numeric,
      $24::numeric, $25::jsonb, $26::numeric, $27::numeric,
      $28::numeric, $29::boolean
    )
  `;
  
  await pool.query(query, [
    txData.signature,
    txData.wallet,
    txData.time,
    txData.trxType || 'openDcaV2',
    txData.cycleFrequency,
    txData.minOutAmount,
    txData.maxOutAmount,
    txData.startAt || 0,
    txData.solPrice,
    txData.inAmountInSOL,
    
    // Input token data
    txData.tokenIn?.tokenAdress,
    txData.tokenIn?.amount,
    txData.tokenIn?.price,
    txData.tokenIn?.tokenInAmountUsd,
    txData.tokenIn?.marketCap,
    JSON.stringify({
      symbol: txData.tokenIn?.symbol,
      name: txData.tokenIn?.name,
      image: txData.tokenIn?.logo
    }),
    txData.tokenIn?.amountPerCycle,
    txData.tokenIn?.marketImpact,
    txData.tokenIn?.priceInSol,
    
    // Output token data
    txData.tokenOut?.tokenAdress,
    txData.tokenOut?.amount,
    txData.tokenOut?.price,
    txData.tokenOut?.tokenOutAmountUsd,
    txData.tokenOut?.marketCap,
    JSON.stringify({
      symbol: txData.tokenOut?.symbol,
      name: txData.tokenOut?.name,
      image: txData.tokenOut?.logo
    }),
    txData.tokenOut?.marketImpact,
    txData.tokenOut?.priceInSol,
    
    txData.totalUsdNumber || txData.totalUsd,
    txData.isKol || false
  ]);
}

/**
 * Get DCA transactions with filtering options
 * @param {Object} params - Query parameters
 * @param {string} params.userId - User ID
 * @param {Array<string>} [params.wallets] - Optional specific wallets to filter by
 * @param {string} [params.trackingType] - Transaction tracking type to filter by
 * @param {number} [params.limit=50] - Number of results to return
 * @param {number} [params.offset=0] - Offset for pagination
 * @param {Date} [params.fromDate] - Start date for filtering
 * @param {Date} [params.toDate] - End date for filtering
 * @param {number} [params.minAmount] - Minimum transaction amount in USD
 * @param {string} [params.tokenSymbol] - Filter by token symbol
 * @returns {Promise<Array>} - Array of DCA transaction objects
 */
async function getStalksDcaTransactions(params) {
  const {
    userId,
    wallets = null,
    trackingType = null,
    limit = 50,
    offset = 0,
    fromDate = null,
    toDate = null,
    minAmount = null,
    tokenSymbol = null
  } = params;

  const query = `
    SELECT * FROM fn_get_stalks_dca_feed(
      $1, $2, $3, $4, $5, $6, $7, $8, $9
    )
  `;

  const result = await pool.query(query, [
    userId,
    wallets,
    trackingType,
    limit,
    offset,
    fromDate,
    toDate,
    minAmount,
    tokenSymbol
  ]);

  return result.rows.map(row => ({
    id: row.signature,
    tblLabel: row.custom_label,
    tblType: 'dca',
    timestamp: row.time,
    tblAmount: row.input_amount,
    tblAmountUsd: row.total_usd_value || 0,
    tblPrice: row.input_usd_price,
    tblMarketCap: row.input_market_cap,
    tblTokenAddress: row.input_mint,
    tblSymbol: row.input_mint_metadata?.symbol,
    tblLogo: row.input_mint_metadata?.image,
    walletAddress: row.wallet_address,
    tx: row.signature,
    totalUsd: row.total_usd_value,
    totalUsdNumber: row.total_usd_value,
    chain: 'solana',
    isKol: row.is_kol,
    transactionType: 'dca',
    trxType: row.trx_type,
    cycleFrequency: row.cycle_frequency,
    minOutAmount: row.min_out_amount,
    maxOutAmount: row.max_out_amount,
    startAt: row.start_at,
    solPrice: row.sol_price,
    inAmountInSOL: row.in_amount_in_sol,
    tokenIn: {
      tokenAddress: row.input_mint,
      symbol: row.input_mint_metadata?.symbol,
      name: row.input_mint_metadata?.name,
      logo: row.input_mint_metadata?.image,
      amount: row.input_amount,
      price: row.input_usd_price,
      tokenAmountString: row.input_amount?.toString(),
      tokenInAmountUsd: row.input_amount_usd,
      marketCap: row.input_market_cap,
      amountPerCycle: row.input_amount_per_cycle,
      marketImpact: row.input_market_impact,
      priceInSol: row.input_price_in_sol
    },
    tokenOut: {
      tokenAddress: row.output_mint,
      symbol: row.output_mint_metadata?.symbol,
      name: row.output_mint_metadata?.name,
      logo: row.output_mint_metadata?.image,
      amount: row.output_amount,
      price: row.output_usd_price,
      tokenAmountString: row.output_amount?.toString(),
      tokenOutAmountUsd: row.output_amount_usd,
      marketCap: row.output_market_cap,
      marketImpact: row.output_market_impact,
      priceInSol: row.output_price_in_sol
    }
  }));
}

/**
 * Delete a tracked wallet
 * @param {string} userId - User ID
 * @param {string} walletAddress - Wallet address to untrack
 * @returns {Promise<Object>} Result of the operation
 */
async function deleteStalksWallet(userId, walletAddress) {
  const query = 'SELECT fn_delete_stalks_wallets($1::uuid, $2::text)';
  const result = await pool.query(query, [userId, walletAddress]);
  return result.rows[0] || null;
}

// Example usage
async function example() {
  try {
    // Example: Track a new wallet then remove it
    const userId = '123e4567-e89b-12d3-a456-************';
    const walletToTrack = 'GgPpEZERpzCFqJj2qwJY1wP8qKpVpQgZUMj8zGiGQXnu';
    
    await upsertStalksWallet(
      userId, 
      walletToTrack,
      'My Trading Wallet',
      { priceChange: true, largeTransaction: true },
      false,
      ['transactions', 'holdings']
    );
    
    // Example: Insert a DCA transaction
    await insertStalksDcaTransaction({
      signature: "2rhenK2u2L5vzgDvTvjcnAWYQNoUtpEHzfnUKWX4BSYWCk1QGqWoFAuo16LjaWogNAHWnw4ndnu8yuUzHwJvopkJ",
      wallet: "BqQYUeLsABXf685SdEP7qdYAv2fZQoz1Qtf8uhsrnKtG",
      time: new Date("2025-05-20T03:13:42.569Z"),
      trxType: "openDcaV2",
      cycleFrequency: 480,
      solPrice: 171.69238,
      inAmountInSOL: 16.324763943681617,
      totalUsd: 2804,
      totalUsdNumber: 2804.099821,
      isKol: true,
      tokenIn: {
        symbol: "NOODLE",
        name: "Noodle",
        logo: "https://ipfs.io/ipfs/bafkreieshxzjxc3hn3h2zvi4cl4vv52emy2xumv4qkpgbknwalz3eh25gq",
        tokenAmountString: "747.64K",
        amount: 747637.556325173,
        tokenInAmountUsd: 2804.099821,
        price: 0.003751,
        tokenAdress: "7cEgQdp8JTXvBrpjSzji7bLEeCHWENRwX62B2Ep97k5H",
        amountPerCycle: 14952.751126503,
        marketCap: 3748898.689609583,
        marketImpact: 0.0007479796210678206,
        priceInSol: 0.0000218351309475703
      },
      tokenOut: {
        symbol: "USDC",
        name: "USD Coin",
        logo: "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png",
        tokenAmountString: "0",
        amount: null,
        tokenOutAmountUsd: 0,
        price: 1.000485,
        tokenAdress: "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        marketCap: 9120022107.801289,
        marketImpact: 3.074663403367401e-7,
        priceInSol: 0.005824574159901563
      }
    });
    
    // Example: Get DCA transactions
    const dcaTransactions = await getStalksDcaTransactions({
      userId,
      limit: 10,
      minAmount: 100
    });
    console.log(`Found ${dcaTransactions.length} DCA transactions`);
    
    // Example: Get swap transactions from tracked wallets
    const swapTransactions = await getStalksTransactions({
      userId,
      limit: 20,
      minAmount: 100, // Only transactions worth $100 or more
      trackingType: 'swap'
    });
    console.log(`Found ${swapTransactions.length} swap transactions`);
    
    // Later, remove the wallet from tracking
    await deleteStalksWallet(userId, walletToTrack);
    
  } catch (error) {
    console.error('Error managing Stalks data:', error);
  }
}
```

For more detailed examples and related functionality, refer to the [KOL Feed Functions Usage Guide](../kol-feed/Get%20KOL%20Feed%20history%20and%20stats.md), noting that the Stalks system follows similar patterns with different function names.
