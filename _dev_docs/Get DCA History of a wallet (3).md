# How to get the DCA history of a wallet

This document details the implementation of the `fn_get_user_dca_history` function that provides comprehensive historical data about a user's Dollar-Cost Averaging (DCA) orders on Jupiter. This function returns detailed information about both active and closed DCA orders, making it ideal for building dashboards, order history views, and performance analytics.

## Function Overview

The `fn_get_user_dca_history` function retrieves the complete history of DCA orders for a specified user, including:

- Basic order information (tokens, timestamps)
- Order status (open, closed, canceled)
- Financial metrics (deposits, fulfillment amounts, USD values)
- Performance data (entry price, average price)
- Progress tracking (percent fulfilled, number of fills)

Active (open) DCA orders are presented first, followed by closed orders, with both groups sorted by creation date (newest first).

## Database Implementation

The function is implemented as a PostgreSQL function in TimescaleDB:

```sql
CREATE OR REPLACE FUNCTION fn_get_user_dca_history(p_user_key TEXT)
RETURNS TABLE (
    dca_key TEXT,
    user_key TEXT,
    input_mint TEXT,
    output_mint TEXT,
    opened_time TIMESTAMPTZ,
    open_signature TEXT,
    is_closed BOOLEAN,
    was_canceled BOOLEAN,
    closed_time TIMESTAMPTZ,
    close_signature TEXT,
    in_deposited NUMERIC,
    in_deposited_usd NUMERIC,
    total_input_fulfilled NUMERIC,
    total_output_fulfilled NUMERIC,
    total_input_fulfilled_usd NUMERIC,
    total_output_fulfilled_usd NUMERIC,
    percent_fulfilled NUMERIC,
    num_fills BIGINT,
    total_expected_fills NUMERIC,
    entry_price NUMERIC,
    entry_price_inverse NUMERIC,
    avg_price_per_token NUMERIC,
    avg_price_inverse NUMERIC,
    cycle_frequency TEXT,
    in_amount_per_cycle NUMERIC,
    total_in_withdrawn NUMERIC,
    total_out_withdrawn NUMERIC,
    unfilled_amount NUMERIC,
    input_usd_price_at_open NUMERIC,
    output_usd_price_at_open NUMERIC,
    avg_input_usd_price NUMERIC,
    avg_output_usd_price NUMERIC
)
LANGUAGE SQL
AS $ 
    -- SQL implementation here (returns maximum 50 results)
    -- See full code in database
$;
```

## JavaScript Integration

Here's how to call the function from JavaScript:

```javascript
const { Pool } = require('pg');

// Create connection pool
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME
});

// Function to get user DCA history
async function getUserDcaHistory(userKey) {
  const query = 'SELECT * FROM fn_get_user_dca_history($1)';
  const result = await pool.query(query, [userKey]);
  return result.rows;
}

// Example usage
async function example() {
  try {
    const userKey = 'DjK2PteBSQVkcYMTJYQVBPyRQtWd1LALf4x9vTBxVhuU';
    const dcaHistory = await getUserDcaHistory(userKey);
    console.log(`Found ${dcaHistory.length} DCA records`);
    console.log(dcaHistory);
  } catch (error) {
    console.error('Error:', error);
  }
}
```

## Returned Data Fields

The function returns a table with the following columns:

| Field Name | Data Type | Description |
|------------|-----------|-------------|
| `dca_key` | TEXT | Unique identifier for the DCA order |
| `user_key` | TEXT | User's public key (wallet address) |
| `input_mint` | TEXT | Address of the input (source) token |
| `output_mint` | TEXT | Address of the output (target) token |
| `opened_time` | TIMESTAMPTZ | When the DCA order was created |
| `open_signature` | TEXT | Signature of the DCA open order |
| `is_closed` | BOOLEAN | Whether the DCA order is closed (`true`) or still active (`false`) |
| `was_canceled` | BOOLEAN | Whether the DCA was canceled by the user (`true`) or completed naturally (`false`) |
| `closed_time` | TIMESTAMPTZ | When the DCA was closed (null if still open) |
| `close_signature` | TEXT | Signature of the DCA closed order (null if still open) |
| `in_deposited` | NUMERIC | Total amount of input token deposited when creating the DCA |
| `in_deposited_usd` | NUMERIC | USD value of the total deposit at time of creation |
| `total_input_fulfilled` | NUMERIC | Amount of input token that has been swapped so far |
| `total_output_fulfilled` | NUMERIC | Amount of output token received from swaps so far |
| `total_input_fulfilled_usd` | NUMERIC | USD value of input token swapped |
| `total_output_fulfilled_usd` | NUMERIC | USD value of output token received |
| `percent_fulfilled` | NUMERIC | Percentage of total deposit that has been swapped (0-100) |
| `num_fills` | BIGINT | Number of swap fill events completed |
| `total_expected_fills` | NUMERIC | Expected total number of fills for the DCA (calculated) |
| `entry_price` | NUMERIC | Initial swap price ratio (input/output) |
| `entry_price_inverse` | NUMERIC | Initial swap price ratio (output/input) |
| `avg_price_per_token` | NUMERIC | Average price across all swaps (input/output) |
| `avg_price_inverse` | NUMERIC | Average price across all swaps (output/input) |
| `cycle_frequency` | TEXT | Time between swap cycles (formatted as "X seconds") |
| `in_amount_per_cycle` | NUMERIC | Amount of input token to swap per cycle |
| `total_in_withdrawn` | NUMERIC | Amount of input token withdrawn when closed (for closed DCAs) |
| `total_out_withdrawn` | NUMERIC | Amount of output token withdrawn when closed (for closed DCAs) |
| `unfilled_amount` | NUMERIC | Unfilled/remaining amount when DCA was closed (for closed DCAs) |
| `input_usd_price_at_open` | NUMERIC | USD price of the input token at the time of DCA creation |
| `output_usd_price_at_open` | NUMERIC | USD price of the output token at the time of DCA creation |
| `avg_input_usd_price` | NUMERIC | Average USD price of the input token across all fills |
| `avg_output_usd_price` | NUMERIC | Average USD price of the output token across all fills |

### Important Notes About the Data:

1. **Token Amounts**: All token amounts are in **human-readable format** (adjusted for decimals)
2. **USD Values**: All USD values are calculated at the time of the event
3. **Prices**:
   - Entry price: Price at first fill event
   - Average price: Average across all fills
   - Both standard (input/output) and inverse (output/input) prices are provided
4. **Status**:
   - `is_closed=false`: DCA is still active
   - `is_closed=true` and `was_canceled=true`: DCA was canceled by the user
   - `is_closed=true` and `was_canceled=false`: DCA completed naturally
5. **Progress**:
   - `percent_fulfilled`: Shows overall progress as a percentage
   - `num_fills` vs `total_expected_fills`: Shows progress in terms of cycles

## Example Data Records

Below are three examples of records returned by the function, each representing a different status:

### Example 1: Active DCA Order

```json
{
  "dca_key": "5wr9zY1wbbaSpr8AKRzdGCjniD9hP5GADRLtLryEfWZ3",
  "user_key": "4XZ3YUeS9YFoxhwTHv24xpY8rTJ4VLSwEGsryCvUqjSa",
  "input_mint": "C3DwDjT17gDvvCYC2nsdGHxDHVmQRdhKfpAdqQ29pump",
  "output_mint": "So11111111111111111111111111111111111111112",
  "opened_time": "2025-04-16T01:21:26.000Z",
  "open_signature": "4nYHfWWDbKAiYxPuzM7LeTNtbeurfhvKG4ujHqtpSxcPfw7tKh4TkuwNZRWLBEj7Aj6W2sZpvniJmtaLyquVnhPe",
  "is_closed": false,
  "was_canceled": null,
  "closed_time": null,
  "close_signature": null,
  "in_deposited": 1660190.236827000000,
  "in_deposited_usd": 140870.175586177650,
  "total_input_fulfilled": 69174.593200000000,
  "total_output_fulfilled": 46.097631418000,
  "total_input_fulfilled_usd": 5839.967093687666,
  "total_output_fulfilled_usd": 5821.075513883449,
  "percent_fulfilled": 4.17,
  "num_fills": 5,
  "total_expected_fills": 121,
  "entry_price": 1498.085566355354,
  "entry_price_inverse": 0.000667518613,
  "avg_price_per_token": 1500.622289474918,
  "avg_price_inverse": 0.00066639541,
  "cycle_frequency": "120 seconds",
  "in_amount_per_cycle": 13834.918640000000,
  "total_in_withdrawn": null,
  "total_out_withdrawn": null,
  "unfilled_amount": null,
  "input_usd_price_at_open": 0.08485516724086853,
  "output_usd_price_at_open": 126.27954809188843,
  "avg_input_usd_price": 0.08443289461874651,
  "avg_output_usd_price": 126.65732148976214
}
```

### Example 2: Completed DCA Order (Finished Naturally)

```json
{
  "dca_key": "3i2cmFRvcCZuBHUkqxHJ1dTnczv2F5MnqN5eabDkfLcZ",
  "user_key": "4XZ3YUeS9YFoxhwTHv24xpY8rTJ4VLSwEGsryCvUqjSa",
  "input_mint": "C3DwDjT17gDvvCYC2nsdGHxDHVmQRdhKfpAdqQ29pump",
  "output_mint": "So11111111111111111111111111111111111111112",
  "opened_time": "2025-04-15T17:50:44.000Z",
  "open_signature": "2JuBYiGCEWesZUdY6CPvTGVHvw5CwH6Mzg76piddN4pwDcCdBjdmiaLF1LYn7hiQcs8e5vhddTMW7eVspMnJQWCh",
  "is_closed": true,
  "was_canceled": false,
  "closed_time": "2025-04-15T18:50:48.000Z",
  "close_signature": "5WbjikT7XdzKfRYS7ULTAtnr9M5vFGPTwPTLPsipMQz85EQFQP2NbgBLY4zqx3XeEM8vCCU8izyZNEWvKzr6NH2T",
  "in_deposited": 1458155.148661000000,
  "in_deposited_usd": 126857.395562977730,
  "total_input_fulfilled": 1458155.148661000000,
  "total_output_fulfilled": 952.803459100000,
  "total_input_fulfilled_usd": 122349.725456556396,
  "total_output_fulfilled_usd": 121984.555353340304,
  "percent_fulfilled": 100.00,
  "num_fills": 60,
  "total_expected_fills": 61,
  "entry_price": 1493.610289523565,
  "entry_price_inverse": 0.000669518687,
  "avg_price_per_token": 1530.7947415414866833,
  "avg_price_inverse": 0.00065343078206666667,
  "cycle_frequency": "60 seconds",
  "in_amount_per_cycle": 24302.585811000000,
  "total_in_withdrawn": null,
  "total_out_withdrawn": 952.803459100000,
  "unfilled_amount": null,
  "input_usd_price_at_open": 0.08699837267648396,
  "output_usd_price_at_open": 128.01354215723649,
  "avg_input_usd_price": 0.08392138689174612,
  "avg_output_usd_price": 127.93573128541232
}
```

### Example 3: Canceled DCA Order

```json
{
  "dca_key": "BxZ3ggejL9Lc1o97JhCcB6tDJBGpt22G88fvKkG7LhBz",
  "user_key": "BanXxY8v51QQgjBH7a51FMr6RpWPJwLYYpsfLmMfS14P",
  "input_mint": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
  "output_mint": "3WfVmcRpEGAkoGCxgyaXTDRDDyPZCXxAhdLxeX2ypump",
  "opened_time": "2025-04-14T02:29:23.000Z",
  "open_signature": "ZzqzdFQY7xCzdCfgAWjMMeReFnAHYbWNGTkgDN2ssM1N5ZNh6gMyK9ysTkn5RCL5iyowJBJxiyUxAB5HjYujdZ3",
  "is_closed": true,
  "was_canceled": true,
  "closed_time": "2025-04-14T02:30:25.000Z",
  "close_signature": "5yHK13FbAets5Ez5ZXjUhJ9zSePMeLWwna8nqN3CEtTLTK8Ax7dmQxaZxLRfCn8J8JxoDkkW6tCgqJ6c54Bo9TAv",
  "in_deposited": 1000.000000000000,
  "in_deposited_usd": 1000.000000000000,
  "total_input_fulfilled": 142.857142000000,
  "total_output_fulfilled": 4889601.230606000000,
  "total_input_fulfilled_usd": 142.857142000000,
  "total_output_fulfilled_usd": 142.714284858010,
  "percent_fulfilled": 14.29,
  "num_fills": 1,
  "total_expected_fills": 8,
  "entry_price": 0.000029216522,
  "entry_price_inverse": 34227.208819605250,
  "avg_price_per_token": 0.000029216522000000000000,
  "avg_price_inverse": 34227.208819605250,
  "cycle_frequency": "60 seconds",
  "in_amount_per_cycle": 142.857142000000,
  "total_in_withdrawn": 857.142858000000,
  "total_out_withdrawn": 4889601.230606000000,
  "unfilled_amount": 857.142858000000,
  "input_usd_price_at_open": 1.0000000000000000,
  "output_usd_price_at_open": 0.0000291875642853148,
  "avg_input_usd_price": 1.0000000000000000,
  "avg_output_usd_price": 0.0000291875642853148
}
```

## Interpreting the Example Records

### Active Order Example

This record represents an active (ongoing) DCA order:

- **Basic Info**: 
  - Pump token (`C3DwDjT17gDvvCYC2nsdGHxDHVmQRdhKfpAdqQ29pump`) to SOL (`So11111111111111111111111111111111111111112`)
  - Created on April 16, 2025, and still running

- **Configuration**:
  - 1,660,190.24 tokens deposited (worth ~$140,870 at time of deposit)
  - Set to swap 13,834.92 tokens every 120 seconds
  - Expected to complete in 121 cycles

- **Current Status**:
  - 5 fills completed out of 121 expected (4.17% progress)
  - 69,174.59 pump tokens swapped for 46.10 SOL
  - Entry price: 1,498.09 pump tokens per SOL
  - Average price so far: 1,500.62 pump tokens per SOL
  - The price is slightly moving against the user (requiring more input tokens per SOL)

- **Price Data**:
  - Input token price at open: $0.0849 per token
  - Output token price at open: $126.28 per SOL
  - Average input token price during DCA: $0.0844 per token
  - Average output token price during DCA: $126.66 per SOL

### Completed Order Example

This record represents a DCA order that completed normally:

- **Basic Info**: 
  - Pump token (`C3DwDjT17gDvvCYC2nsdGHxDHVmQRdhKfpAdqQ29pump`) to SOL (`So11111111111111111111111111111111111111112`)
  - Created on April 15, 2025, and completed about 1 hour later

- **Configuration**:
  - 1,458,155.15 tokens deposited (worth ~$126,857 at time of deposit)
  - Set to swap 24,302.59 tokens every 60 seconds
  - Expected to complete in 61 cycles

- **Completion Details**:
  - Fully completed (100% fulfilled) with 60 fills
  - All 1,458,155.15 pump tokens were swapped for 952.80 SOL
  - Entry price: 1,493.61 pump tokens per SOL
  - Final average price: 1,530.79 pump tokens per SOL
  - The price moved slightly against the user over the DCA period
  - The user received 952.80 SOL upon completion

- **Price Data**:
  - Input token price at open: $0.0870 per token
  - Output token price at open: $128.01 per SOL
  - Average input token price during DCA: $0.0839 per token
  - Average output token price during DCA: $127.94 per SOL
  - Token price relationships changed slightly over the DCA period

### Canceled Order Example

This record represents a DCA order that was canceled by the user:

- **Basic Info**: 
  - USDC (`EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v`) to a pump token (`3WfVmcRpEGAkoGCxgyaXTDRDDyPZCXxAhdLxeX2ypump`)
  - Created on April 14, 2025, and canceled about a minute later

- **Configuration**:
  - 1,000 USDC deposited (worth $1,000 at time of deposit)
  - Set to swap 142.86 USDC every 60 seconds
  - Expected to complete in 8 cycles

- **Cancellation Details**:
  - Completed 1 fill out of 8 expected (14.29% progress) before canc



## Error Handling

The function will return an empty result set if:
- The user has no DCA history
- The provided user_key doesn't exist

Handle this case in your UI by showing appropriate empty state messaging.

## Function (full)

The full function `fn_get_user_dca_history` in postgres for backup and as reference:

```sql
CREATE OR REPLACE FUNCTION "public"."fn_get_user_dca_history"("p_user_key" text)
  RETURNS TABLE(
    "dca_key" text, 
    "user_key" text, 
    "input_mint" text, 
    "output_mint" text, 
    "opened_time" timestamptz, 
    "open_signature" text,
    "is_closed" bool, 
    "was_canceled" bool, 
    "closed_time" timestamptz, 
    "close_signature" text,
    "in_deposited" numeric, 
    "in_deposited_usd" numeric, 
    "total_input_fulfilled" numeric, 
    "total_output_fulfilled" numeric, 
    "total_input_fulfilled_usd" numeric, 
    "total_output_fulfilled_usd" numeric, 
    "percent_fulfilled" numeric, 
    "num_fills" int8, 
    "total_expected_fills" numeric, 
    "entry_price" numeric, 
    "entry_price_inverse" numeric, 
    "avg_price_per_token" numeric, 
    "avg_price_inverse" numeric, 
    "cycle_frequency" text, 
    "in_amount_per_cycle" numeric, 
    "total_in_withdrawn" numeric, 
    "total_out_withdrawn" numeric, 
    "unfilled_amount" numeric, 
    "input_usd_price_at_open" numeric, 
    "output_usd_price_at_open" numeric, 
    "avg_input_usd_price" numeric, 
    "avg_output_usd_price" numeric
  ) AS $BODY$
    WITH 
    -- Get all DCAs for this user
    user_dcas AS (
      SELECT DISTINCT dca_key 
      FROM stream_jupiter_dca_ht
      WHERE user_key = p_user_key
    ),
    
    -- Get opening details for each DCA
    dca_opened AS (
      SELECT 
        dca_key,
        user_key,
        time AS opened_time,
        signature AS open_signature,
        in_deposited,
        in_amount_per_cycle,
        cycle_frequency,
        input_mint,
        output_mint,
        input_usd_price,
        output_usd_price,
        in_deposited_usd
      FROM stream_jupiter_dca_ht
      WHERE event_type = 'opened'
      AND dca_key IN (SELECT dca_key FROM user_dcas)
    ),
    
    -- Get closing details for each DCA, if closed
    dca_closed AS (
      SELECT 
        dca_key,
        time AS closed_time,
        signature AS close_signature,
        user_closed AS was_canceled,
        total_in_withdrawn,
        total_out_withdrawn,
        unfilled_amount
      FROM stream_jupiter_dca_ht
      WHERE event_type = 'closed'
      AND dca_key IN (SELECT dca_key FROM user_dcas)
    ),
    
    -- Get first fill for each DCA (for entry price)
    first_fills AS (
      SELECT DISTINCT ON (dca_key)
        dca_key,
        price_per_token AS entry_price,
        price_inverse AS entry_price_inverse,
        time AS first_fill_time
      FROM stream_jupiter_dca_ht
      WHERE event_type = 'fill'
      AND dca_key IN (SELECT dca_key FROM user_dcas)
      ORDER BY dca_key, time ASC
    ),
    
    -- Calculate fulfilled amount and average price from all fill events
    dca_fills AS (
      SELECT
        dca_key,
        COUNT(*) AS num_fills,
        SUM(input_amount) AS total_input_filled,
        SUM(output_amount) AS total_output_filled,
        SUM(input_amount_usd) AS total_input_filled_usd,
        SUM(output_amount_usd) AS total_output_filled_usd,
        -- Get average of price_per_token from fill events
        AVG(price_per_token) AS avg_price_per_token,
        -- Get average of price_inverse from fill events
        AVG(price_inverse) AS avg_price_inverse,
        -- Calculate average USD prices of tokens across all fills
        AVG(input_usd_price) AS avg_input_usd_price,
        AVG(output_usd_price) AS avg_output_usd_price
      FROM stream_jupiter_dca_ht
      WHERE event_type = 'fill'
      AND dca_key IN (SELECT dca_key FROM user_dcas)
      GROUP BY dca_key
    )
    
    -- Combine all the information
    SELECT
      o.dca_key,
      o.user_key,
      o.input_mint,
      o.output_mint,
      o.opened_time,
      o.open_signature,
      -- Is it closed?
      CASE WHEN c.dca_key IS NULL THEN false ELSE true END AS is_closed,
      -- Was it canceled? (null if not closed)
      c.was_canceled,
      -- When was it closed? (null if not closed)
      c.closed_time,
      -- Transaction signature for closing (null if not closed)
      c.close_signature,  -- Added this field
      
      -- Deposited and fulfilled amounts
      o.in_deposited,
      o.in_deposited_usd,
      COALESCE(f.total_input_filled, 0) AS total_input_fulfilled,
      COALESCE(f.total_output_filled, 0) AS total_output_fulfilled,
      COALESCE(f.total_input_filled_usd, 0) AS total_input_fulfilled_usd,
      COALESCE(f.total_output_filled_usd, 0) AS total_output_fulfilled_usd,
      
      -- Progress calculation
      CASE 
        WHEN o.in_deposited > 0 THEN ROUND((COALESCE(f.total_input_filled, 0) / o.in_deposited) * 100, 2)
        ELSE 0 
      END AS percent_fulfilled,
      
      -- Fills information
      COALESCE(f.num_fills, 0) AS num_fills,
      CASE 
        WHEN o.in_amount_per_cycle > 0 THEN FLOOR(o.in_deposited / o.in_amount_per_cycle)
        ELSE NULL 
      END AS total_expected_fills,
      
      -- Price information
      ff.entry_price,
      ff.entry_price_inverse,
      f.avg_price_per_token,
      f.avg_price_inverse,
      
      -- DCA configuration
      o.cycle_frequency || ' seconds' AS cycle_frequency,
      o.in_amount_per_cycle,
      
      -- For closed DCAs
      c.total_in_withdrawn,
      c.total_out_withdrawn,
      c.unfilled_amount,
      
      -- New fields: USD prices at opening
      o.input_usd_price AS input_usd_price_at_open,
      o.output_usd_price AS output_usd_price_at_open,
      
      -- New fields: Average USD prices across fills
      f.avg_input_usd_price,
      f.avg_output_usd_price
      
    FROM dca_opened o
    LEFT JOIN dca_closed c ON o.dca_key = c.dca_key
    LEFT JOIN first_fills ff ON o.dca_key = ff.dca_key
    LEFT JOIN dca_fills f ON o.dca_key = f.dca_key
    
    -- Sort by open/closed status (open first) then by opened time descending
    ORDER BY o.opened_time DESC
    LIMIT 100
$BODY$
  LANGUAGE sql VOLATILE
  COST 100
  ROWS 50
```
