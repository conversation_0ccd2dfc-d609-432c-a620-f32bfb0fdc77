---
description: >-
  Complete developer guide for StalkChain's spam and timeout token management system: automated filtering,
  caching strategies, and real-world integration patterns for clean portfolio management.
---

# Token Filtering System: Spam, Timeout & Invalid Token Management

**Target Audience:** Frontend and backend developers who need to implement token filtering in StalkChain applications, understand the spam detection system, optimize API performance, and maintain clean portfolio data.

## System Overview

StalkChain's token filtering system protects users from spam tokens and optimizes API performance through a multi-layered approach:

### Core Components
1. **Spam Token Management** - Persistent filtering of fraudulent/worthless tokens
2. **Timeout Token Tracking** - Performance optimization by avoiding slow API calls  
3. **Invalid Token Handling** - Skip non-existent tokens (500 errors from APIs)
4. **Multi-Source Detection** - Automated spam identification from risk scores
5. **Background Synchronization** - Keeps caches fresh without user impact

### Key Benefits
- **Fast Lookups**: O(1) Redis SET operations for instant spam detection
- **Performance Protection**: Timeout tracking prevents slow API responses
- **Error Prevention**: Invalid token cache prevents repeated 500 errors
- **Zero-Downtime Updates**: Atomic Redis operations ensure continuous service
- **Resilient Architecture**: Continues working if individual components fail
- **Automated Learning**: Risk-based spam detection improves over time

## Quick Start Guide

### Client-Side Usage

```jsx
'use client';
import { isSpamToken } from '@/actions/tokens/spamTokens';

async function TokenDisplayComponent({ tokenAddress }) {
  const isSpam = await isSpamToken(tokenAddress);
  
  if (isSpam) {
    return <div className="text-red-500">Token flagged as spam</div>;
  }
  
  return <TokenCard address={tokenAddress} />;
}
```

### Server-Side Bulk Operations

```jsx
// actions/wallet/getWalletBalance.js
import { checkIsSpamTokens } from '@/utils/cache/spamTokens';
import { checkTimeoutTokens } from '@/utils/cache/timeoutTokens';
import { checkInvalidTokens } from '@/utils/cache/invalidTokens';

// Filter tokens using bulk operations
const [spamMap, timeoutMap, invalidMap] = await Promise.all([
  checkIsSpamTokens(tokenAddresses),
  checkTimeoutTokens(tokenAddresses),
  checkInvalidTokens(tokenAddresses)
]);

const cleanTokens = tokens.filter(token => 
  !spamMap[token.mint] && !timeoutMap[token.mint] && !invalidMap[token.mint]
);
```

## System Architecture

```mermaid
flowchart TD
    A[Token Request] --> B{Is Frozen?}
    B -->|Yes| C[Add to Spam List]
    B -->|No| D{In Spam Cache?}
    D -->|Yes| E[Filter Out]
    D -->|No| F{In Timeout Cache?}
    F -->|Yes| G[Skip API Call]
    F -->|No| H{In Invalid Cache?}
    H -->|Yes| I[Skip API Call]
    H -->|No| J[Fetch Price/Risk Data]
    J --> K{Risk Score = 10?}
    K -->|Yes| L[Auto-Flag as Spam]
    K -->|No| M{API Timeout?}
    M -->|Yes| N[Add to Timeout Cache]
    M -->|No| O{500 Error?}
    O -->|Yes| P[Add to Invalid Cache]
    O -->|No| Q[Process Normally]
    
    classDef spamProcess fill:#ff6b6b,stroke:#d63031,color:#fff
    classDef timeoutProcess fill:#ffa726,stroke:#f57c00,color:#fff
    classDef invalidProcess fill:#ff7043,stroke:#e64a19,color:#fff
    classDef normalProcess fill:#66bb6a,stroke:#388e3c,color:#fff
    classDef decisionPoint fill:#fff,stroke:#455a64,color:#455a64
    
    class C,E,L spamProcess
    class G,N timeoutProcess
    class I,P invalidProcess
    class Q normalProcess
    class B,D,F,H,K,M,O decisionPoint
```

### Data Flow in Wallet Portfolio

The wallet portfolio system demonstrates the complete token filtering pipeline:

```mermaid
flowchart TD
    A[Helius API] --> B[Raw Token List]
    B --> C{Filter Frozen Tokens}
    C -->|Frozen| D[Spam Collection]
    C -->|Not Frozen| E[Bulk Spam Check]
    E --> F[Bulk Timeout Check]
    F --> G[Bulk Invalid Check]
    G --> H[Price Fetching with Risk Assessment]
    H --> I{High Risk Detected?}
    I -->|Yes| J[Auto-Add to Spam]
    I -->|No| K{API Timeout?}
    K -->|Yes| L[Add to Timeout Cache]
    K -->|No| M{500 Error?}
    M -->|Yes| N[Add to Invalid Cache]
    M -->|No| O[Final Spam Recheck]
    O --> P[Value Filtering]
    P --> Q[Clean Portfolio]
    
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef externalProcess fill:#fff3e0,stroke:#e65100,color:#e65100
    classDef spamProcess fill:#ff6b6b,stroke:#d63031,color:#fff
    classDef normalProcess fill:#66bb6a,stroke:#388e3c,color:#fff
    
    class A externalProcess
    class B,E,F,G,H,O,P serverProcess
    class D,J spamProcess
    class Q normalProcess
    class C,I,K,M decisionPoint
```

## Spam Token Management

### Detection Methods

#### 1. Manual Flagging
Tokens reported by users or administrators:

```js
// utils/cache/spamTokens.js
import { addSpamToken } from '@/utils/cache/spamTokens';

await addSpamToken({
  mintAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
  ticker: 'SCAM',
  source: 'manual-review',
  reason: 'Fraudulent activity reported by users',
  isManual: true
});
```

#### 2. Automated Risk Assessment
High-risk tokens automatically flagged during price fetching:

```js
// utils/solanaTracker.js
if (data?.risk?.score === 10) {
  await addSpamToken({
    mintAddress: tokenAddress,
    ticker: data.token?.symbol || null,
    source: 'solana-tracker-api',
    reason: 'Risk score 10 - automatically flagged',
    isManual: false
  });
}
```

#### 3. Frozen Token Detection
Tokens with frozen accounts are immediately flagged:

```js
// actions/wallet/getWalletBalance.js
for (const token of tokens) {
  if (token.frozen) {
    spamTokens.push(token);
  } else {
    filteredTokens.push(token);
  }
}
```

#### 4. Bulk Risk Screening
During bulk price operations, tokens with risk score 10 are automatically added:

```js
// actions/tokens/getTokenPrice.js
if (trackerData?.risk?.score === 10) {
  result.spamTokens.push({
    mintAddress: tokenAddress,
    ticker: trackerData.symbol || null,
    source: 'solana-tracker-api',
    reason: 'Risk score 10 - bulk price check',
    isManual: false
  });
}
```

### Data Storage

#### PostgreSQL Schema (Source of Truth)

The `list_tokens_spam` table stores complete spam token data:

| Column | Type | Description | Example |
|--------|------|-------------|---------|
| mint_address | text | Primary key - Token's mint address | `7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr` |
| ticker | text | Token ticker symbol (optional) | `SCAM` |
| source | text | Detection source | `manual-review`, `solana-tracker-api` |
| reason | text | Reason for flagging (optional) | `Risk score 10 - automatically flagged` |
| is_manual | boolean | Whether manually flagged | `true` / `false` |
| created_at | timestamptz | First flagged timestamp | `2024-01-15 10:30:00+00` |
| updated_at | timestamptz | Last updated timestamp | `2024-01-15 10:30:00+00` |

#### Redis Cache Layer

Fast lookup cache for real-time filtering:

- **Key**: `stalkchain:spam:tokens`
- **Type**: Redis SET
- **Contents**: Token mint addresses only
- **TTL**: 7 days (refreshed by background sync)
- **Operations**: `SISMEMBER` (single check), `SMISMEMBER` (bulk check)

## Timeout Token Management

### Purpose & Benefits

Timeout tokens are tracked to prevent repeatedly slow API calls:
- **Performance**: Skip tokens that previously timed out
- **User Experience**: Faster portfolio loading
- **API Efficiency**: Reduce unnecessary network calls

### Timeout Configuration

The system uses different timeout values for different operations:

#### Wallet Portfolio Timeout Settings

```js
// actions/wallet/getWalletBalance.js
const DEFAULT_API_TIMEOUT = 2000; // 2 seconds

// Usage in wallet portfolio
const tokenPriceResults = await getTokenPrices(tokenMints, { 
  noRetry: true,
  timeout: DEFAULT_API_TIMEOUT 
});
```

#### Token Price Timeout Settings

```js
// actions/tokens/getTokenPrice.js  
export async function getTokenPrices(tokenAddresses, options = {}) {
  // Set default timeout to 3000ms (3 seconds) if not provided
  options.timeout = options.timeout || 3000;
}
```

#### Where to Change Timeout Settings

1. **Wallet Portfolio Performance**: Modify `DEFAULT_API_TIMEOUT` in `actions/wallet/getWalletBalance.js`
   - Current: 2000ms (2 seconds)
   - Recommended range: 1500-3000ms

2. **Token Price Fetching**: Modify default timeout in `actions/tokens/getTokenPrice.js`
   - Current: 3000ms (3 seconds) 
   - Recommended range: 2000-5000ms

3. **Custom Operations**: Pass timeout in options when calling functions:
   ```js
   // Fast response (1.5 seconds)
   await getTokenPrices(tokens, { timeout: 1500 });
   
   // Balanced response (2.5 seconds)  
   await getTokenPrices(tokens, { timeout: 2500 });
   
   // Thorough response (5 seconds)
   await getTokenPrices(tokens, { timeout: 5000 });
   ```

### How Timeouts Are Detected

```js
// utils/solanaTracker.js
const fetchWithRetry = async (url, options, retries, delay, noRetry, timeout, tokenAddress) => {
  try {
    const response = await axios(url, requestOptions);
    return response.data;
  } catch (error) {
    if (error.code === 'ECONNABORTED') {
      // Add to timeout cache
      if (tokenAddress) {
        await addTimeoutToken(tokenAddress);
        console.log(`Added token ${tokenAddress} to timeout cache due to API timeout`);
      }
      return null;
    }
  }
};
```

### Timeout Cache Structure

- **Key Pattern**: `solanatracker:timeout:token:{mintAddress}`
- **Type**: Redis STRING (value: "1")
- **TTL**: 2 hours
- **Purpose**: Skip price fetching for recently slow tokens

### Usage in Portfolio Building

```js
// actions/wallet/getWalletBalance.js
const [spamMap, timeoutMap, invalidMap] = await Promise.all([
  checkIsSpamTokens(mintAddresses),
  checkTimeoutTokens(mintAddresses),
  checkInvalidTokens(mintAddresses)
]);

const finalFilteredTokens = filteredTokens.filter(token => {
  if (spamMap[token.mint]) {
    spamTokens.push(token);
    return false;
  }
  
  if (timeoutMap[token.mint]) {
    timeoutTokens.push(token);
    return false;
  }
  
  if (invalidMap[token.mint]) {
    invalidTokens.push(token);
    return false;
  }
  
  return true;
});
```

## Invalid Token Management

### Purpose & Benefits

Invalid tokens are those that return 500 errors from external APIs, typically indicating:
- **Non-existent tokens**: Token addresses that don't exist on-chain
- **Malformed addresses**: Invalid mint addresses
- **API-side issues**: Tokens that consistently fail validation

Benefits of tracking invalid tokens:
- **Error Prevention**: Avoid repeated 500 errors
- **Performance**: Skip known invalid tokens immediately
- **User Experience**: Cleaner error handling and faster responses

### How Invalid Tokens Are Detected

```js
// utils/solanaTracker.js
const fetchWithRetry = async (url, options, retries, delay, noRetry, timeout, tokenAddress) => {
  try {
    const response = await axios(url, requestOptions);
    return response.data;
  } catch (error) {
    // Handle 500 errors specifically - invalid/non-existent tokens
    if (error.response && error.response.status === 500) {
      console.error(`Error fetching ${url}: Request failed with status code 500`);
      
      // Add to invalid cache
      if (tokenAddress) {
        await addInvalidToken(tokenAddress);
        console.log(`Added token ${tokenAddress} to invalid cache due to 500 error`);
      }
      
      return null;
    }
  }
};
```

### Invalid Token Cache Structure

- **Key Pattern**: `solanatracker:invalid:token:{mintAddress}`
- **Type**: Redis STRING (value: "1")
- **TTL**: 30 minutes (shorter than timeout tokens as these may be temporary API issues)
- **Purpose**: Skip API calls for tokens that return 500 errors

### Integration with Portfolio System

```js
// actions/wallet/getWalletBalance.js
const [spamMap, timeoutMap, invalidMap] = await Promise.all([
  checkIsSpamTokens(mintAddresses),
  checkTimeoutTokens(mintAddresses),
  checkInvalidTokens(mintAddresses)  // Check for 500 error tokens
]);

// Filter out invalid tokens along with spam and timeout tokens
const finalFilteredTokens = filteredTokens.filter(token => {
  if (invalidMap[token.mint]) {
    invalidTokens.push(token);
    return false;
  }
  return true;
});
```

### Invalid Token API Functions

```js
// utils/cache/invalidTokens.js

// Check single token
const isInvalid = await isInvalidToken('tokenAddress...');

// Check multiple tokens (bulk operation)
const invalidMap = await checkInvalidTokens(['token1...', 'token2...']);

// Add invalid token (automatically called by API utilities)
await addInvalidToken('tokenAddress...');

// Bulk add invalid tokens
await bulkAddInvalidTokens(['token1...', 'token2...']);

// Get all invalid tokens for monitoring
const result = await getAllInvalidTokens();
```

## Real-World Integration Examples

### Wallet Portfolio System

The complete filtering pipeline is best demonstrated in the wallet portfolio system:

```js
// actions/wallet/getWalletBalance.js
export async function getWalletPortfolio(walletAddress) {
  // STEP 1: Get raw token data from Helius
  const tokenDetails = await getWalletTokenDetails(walletAddress);
  
  // STEP 2: Initial filtering (frozen tokens → spam)
  const filteredTokens = [];
  const spamTokens = [];
  
  for (const token of tokens) {
    if (token.frozen) {
      spamTokens.push(token);
    } else {
      filteredTokens.push(token);
    }
  }
  
  // STEP 3: Bulk spam, timeout, and invalid checking
  const [spamMap, timeoutMap, invalidMap] = await Promise.all([
    checkIsSpamTokens(filteredTokens.map(t => t.mint)),
    checkTimeoutTokens(filteredTokens.map(t => t.mint)),
    checkInvalidTokens(filteredTokens.map(t => t.mint))
  ]);
  
  // STEP 4: Filter based on cache results
  const cleanTokens = filteredTokens.filter(token => 
    !spamMap[token.mint] && !timeoutMap[token.mint] && !invalidMap[token.mint]
  );
  
  // STEP 5: Bulk price fetching with risk assessment
  const tokenPriceResults = await getTokenPrices(tokenMints, {
    noRetry: true,
    timeout: DEFAULT_API_TIMEOUT
  });
  
  // STEP 6: Handle newly discovered spam tokens
  if (tokenPriceResults?.spamTokens?.length > 0) {
    await bulkAddSpamTokens(tokenPriceResults.spamTokens);
  }
  
  return {
    // ... portfolio data
    spamTokensCount: spamTokens.length + additionalSpamTokens.length,
    timeoutTokensCount: timeoutTokens.length,
    invalidTokensCount: invalidTokens.length,
    // ... other data
  };
}
```

### KOL Token Holdings Table

In the KOL profile pages, token filtering ensures clean data display:

```js
// Used for displaying KOL token holdings without spam tokens
const kolTokens = await getTokenUsdBalanceForWallets(tokenAddress, kolWallets);

// Spam tokens are automatically filtered in the bulk operations
// Timeout tokens are skipped to maintain fast page loads
// Invalid tokens are filtered to prevent 500 errors
```

### Fresh Wallet Feed

Real-time filtering in the WebSocket feed:

```js
// socket/feed/freshWalletFeed.js
const result = await this.dbClient.query(`
  SELECT swf.*
  FROM stream_wallets_fresh_ht swf
  WHERE swf.id = $1 
  AND swf.input_mint NOT IN (SELECT mint_address FROM list_tokens_spam)
  AND swf.output_mint NOT IN (SELECT mint_address FROM list_tokens_spam)
`, [data.id]);
```

## Automated Synchronization

### Background Worker

A dedicated worker syncs PostgreSQL data to Redis every 15 minutes:

```js
// socket/serviceworker/processor/spamTokenSync.js
class SpamTokenSyncWorker {
  async syncSpamTokens() {
    // 1. Fetch all spam tokens from PostgreSQL
    const { rows } = await query('SELECT mint_address FROM list_tokens_spam');
    
    // 2. Create temporary Redis set
    const tempKey = `${SPAM_TOKENS_KEY}:temp:${Date.now()}`;
    
    // 3. Populate temporary set
    const pipeline = redis.pipeline();
    mintAddresses.forEach(address => {
      pipeline.sadd(tempKey, address);
    });
    await pipeline.exec();
    
    // 4. Atomic swap (zero downtime)
    await redis.multi()
      .rename(tempKey, SPAM_TOKENS_KEY)
      .expire(SPAM_TOKENS_KEY, SPAM_TOKEN_CACHE_TTL)
      .exec();
  }
}
```

### Sync Schedule

- **Immediate**: Runs on service startup
- **Recurring**: Every 15 minutes
- **Zero Downtime**: Uses atomic Redis operations
- **Error Handling**: Graceful failure with logging

## API Reference

### Client-Side Actions

#### `isSpamToken(mintAddress)`

Check if a single token is marked as spam (client-side safe).

```js
// actions/tokens/spamTokens.js
import { isSpamToken } from '@/actions/tokens/spamTokens';

const isSpam = await isSpamToken('7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr');
// Returns: boolean
```

### Server-Side Functions

#### Spam Token Functions

**`addSpamToken(tokenData)`**
```js
// utils/cache/spamTokens.js
import { addSpamToken } from '@/utils/cache/spamTokens';

const result = await addSpamToken({
  mintAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
  ticker: 'SCAM',
  source: 'manual-review',
  reason: 'Fraudulent activity',
  isManual: true
});

// Returns:
// {
//   success: true,
//   postgres: { success: true, data: {...} },
//   redis: { success: true },
//   mintAddress: 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
// }
```

**`checkIsSpamTokens(mintAddresses)`**
```js
// Bulk spam checking (efficient for large lists)
const spamMap = await checkIsSpamTokens([
  '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr',
  'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v'
]);

// Returns:
// {
//   '7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr': false,
//   'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': true
// }
```

**`bulkAddSpamTokens(tokensData)`**
```js
// Efficiently add multiple spam tokens
const result = await bulkAddSpamTokens([
  {
    mintAddress: 'token1...',
    source: 'bulk-risk-check',
    reason: 'Risk score 10'
  },
  {
    mintAddress: 'token2...',
    source: 'bulk-risk-check', 
    reason: 'Risk score 10'
  }
]);

// Returns:
// {
//   success: true,
//   postgres: { success: true, count: 2 },
//   redis: { success: true, count: 2 },
//   totalTokens: 2,
//   validTokensCount: 2,
//   invalidTokensCount: 0
// }
```

#### Timeout Token Functions

**`checkTimeoutTokens(tokenAddresses)`**
```js
// utils/cache/timeoutTokens.js
import { checkTimeoutTokens } from '@/utils/cache/timeoutTokens';

const timeoutMap = await checkTimeoutTokens([
  'tokenA...',
  'tokenB...'
]);

// Returns:
// {
//   'tokenA...': false,
//   'tokenB...': true  // This token recently timed out
// }
```

**`addTimeoutToken(tokenAddress)`**
```js
// Automatically called by API utilities when timeouts occur
const success = await addTimeoutToken('7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr');
// Returns: boolean
```

#### Invalid Token Functions

**`checkInvalidTokens(tokenAddresses)`**
```js
// utils/cache/invalidTokens.js
import { checkInvalidTokens } from '@/utils/cache/invalidTokens';

const invalidMap = await checkInvalidTokens([
  'tokenA...',
  'tokenB...'
]);

// Returns:
// {
//   'tokenA...': false,
//   'tokenB...': true  // This token returned 500 error
// }
```

**`addInvalidToken(tokenAddress)`**
```js
// Automatically called by API utilities when 500 errors occur
const success = await addInvalidToken('7GCihgDB8fe6KNjn2MYtkzZcRjQy3t9GHdC8uHYmW2hr');
// Returns: boolean
```

## Performance Optimization

### Bulk Operations

Always prefer bulk operations over individual calls:

```js
// ❌ Inefficient - individual calls
for (const token of tokens) {
  const isSpam = await checkIsSpamToken(token.mint);
  if (isSpam) filteredTokens.push(token);
}

// ✅ Efficient - bulk operation
const spamMap = await checkIsSpamTokens(tokens.map(t => t.mint));
const filteredTokens = tokens.filter(t => !spamMap[t.mint]);
```

### Caching Strategy

- **Redis TTL**: 
  - 7 days for spam tokens
  - 2 hours for timeout tokens  
  - 30 minutes for invalid tokens
- **Background Sync**: Every 15 minutes to ensure freshness
- **Atomic Updates**: Zero-downtime cache refresh
- **Fallback**: PostgreSQL queries if Redis fails

### Batch Processing

The wallet portfolio system uses intelligent batching:

```js
// Process tokens in batches to avoid memory issues
const BATCH_SIZE = 100;
const BATCH_DELAY = 10; // ms

for (let i = 0; i < tokens.length; i += BATCH_SIZE) {
  const batch = tokens.slice(i, i + BATCH_SIZE);
  await processBatch(batch);
  
  if (i + BATCH_SIZE < tokens.length) {
    await new Promise(resolve => setTimeout(resolve, BATCH_DELAY));
  }
}
```

## Best Practices

### Error Handling

1. **Graceful Degradation**: Continue processing even if spam/timeout/invalid checks fail
2. **Safe Defaults**: Return `false` for checks on errors (avoid false positives)
3. **Logging**: Track errors without exposing sensitive data

```js
export async function checkIsSpamToken(mintAddress) {
  try {
    const isSpam = await redis.sismember(SPAM_TOKENS_KEY, mintAddress.trim());
    return isSpam === 1;
  } catch (error) {
    console.error('Error checking if token is spam:', error);
    return false; // Safe default - don't falsely flag as spam
  }
}
```

### Performance Guidelines

1. **Use Bulk Operations**: Always prefer bulk checks over individual calls
2. **Implement Timeouts**: Set reasonable API timeouts (2-3s default)
3. **Batch Processing**: Process large datasets in smaller chunks
4. **Cache Appropriately**: Use Redis for hot data, PostgreSQL for persistence

### Security Considerations

1. **Input Sanitization**: Always sanitize token addresses
2. **Rate Limiting**: Implement proper rate limits for API endpoints
3. **Validation**: Verify token address formats before processing
4. **Access Control**: Restrict spam token management to authorized users

## Advanced Usage Patterns

### Custom Spam Detection

Extend the system with custom detection logic:

```js
// Custom risk assessment
async function assessCustomRisk(tokenData) {
  const riskFactors = [];
  
  if (tokenData.liquidity < 1000) riskFactors.push('Low liquidity');
  if (tokenData.holders < 100) riskFactors.push('Few holders');
  if (!tokenData.verified) riskFactors.push('Unverified token');
  
  if (riskFactors.length >= 2) {
    await addSpamToken({
      mintAddress: tokenData.mint,
      source: 'custom-risk-assessment',
      reason: riskFactors.join(', '),
      isManual: false
    });
  }
}
```

### Integration with External Services

Monitor external spam lists and sync with StalkChain:

```js
// Sync with external spam databases
async function syncExternalSpamList(externalSpamTokens) {
  const spamTokensToAdd = externalSpamTokens.map(token => ({
    mintAddress: token.address,
    ticker: token.symbol,
    source: 'external-service',
    reason: 'Flagged by external spam database',
    isManual: false
  }));
  
  await bulkAddSpamTokens(spamTokensToAdd);
}
```

## Troubleshooting

### Common Issues

**Missing Data in Portfolio**
```bash
# Check Redis connection
redis-cli ping

# Verify spam tokens set exists
redis-cli SCARD stalkchain:spam:tokens

# Check PostgreSQL data
SELECT COUNT(*) FROM list_tokens_spam;
```

**Slow Portfolio Loading**
```js
// Check timeout token count
const timeoutTokens = await getAllTimeoutTokens();
console.log(`${timeoutTokens.count} tokens in timeout cache`);

// Check invalid token count  
const invalidTokens = await getAllInvalidTokens();
console.log(`${invalidTokens.count} tokens in invalid cache`);

// Consider reducing API timeout for faster responses
const portfolio = await getWalletPortfolio(address, { timeout: 1500 });
```

**Sync Worker Issues**
```bash
# Check worker logs
tail -f logs/spam-token-sync.log

# Manual sync trigger (if needed)
const result = await syncSpamTokens();
console.log(result);
```

### Monitoring & Diagnostics

Track system health with key metrics:

```js
// Monitor token filtering effectiveness
const stats = {
  totalSpamTokens: await redis.scard('stalkchain:spam:tokens'),
  recentTimeouts: await redis.keys('solanatracker:timeout:token:*'),
  recentInvalids: await redis.keys('solanatracker:invalid:token:*'),
  syncLastRun: await redis.get('spam-sync:last-run'),
  portfolioFilterRate: (spamTokensFiltered + timeoutTokensFiltered + invalidTokensFiltered) / totalTokensProcessed
};
```

### Performance Tuning

Optimize for your specific use case:

```js
// Adjust timeouts based on user tolerance
const TIMEOUT_CONFIGS = {
  'fast': 1000,      // 1s - fastest response
  'balanced': 2500,   // 2.5s - good balance  
  'thorough': 5000   // 5s - most complete data
};

// Configure batch sizes based on memory constraints
const BATCH_CONFIGS = {
  'memory-constrained': { size: 25, delay: 20 },
  'balanced': { size: 50, delay: 10 },
  'performance': { size: 100, delay: 5 }
};
```

This comprehensive token filtering system ensures clean, fast, and reliable portfolio data while protecting users from spam tokens, optimizing API performance through intelligent timeout management, and preventing repeated errors from invalid tokens.
