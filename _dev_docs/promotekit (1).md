---
description: >-
  Learn how to use PromoteKit for tracking referrals and affiliate codes in your
  application, with specific implementation details for StalkChain.
---

# PromoteKit

**Target audience:** Developers who need to implement, maintain, or troubleshoot the PromoteKit referral tracking system in the StalkChain application.

> **Key Configuration**: You'll need your organization UUID to implement PromoteKit. This identifier links all tracking to your organization's account. You can find it on the [PromoteKit Dashboard](https://www.promotekit.com/dashboard/) after joining the StalkChain organization.

### Overview

PromoteKit is a referral tracking system that enables StalkChain to track referrals and affiliate codes through Stripe. Our implementation automatically appends referral codes to Stripe checkout links and pricing tables, and we've extended it to also track crypto payments through Helio.

### Implementation

PromoteKit is implemented in StalkChain's Next.js application through scripts added to the `app/layout.js` file.

#### Script Configuration

```jsx
// In app/layout.js
<head>
  {/* Other head elements */}
  
  {/* PromoteKit tracking script with organization UUID */}
  <script 
    async 
    src="https://cdn.promotekit.com/promotekit.js" 
    data-promotekit="{organization-uuid}"
  />
  
  {/* Stripe integration script */}
  <script dangerouslySetInnerHTML={{
    __html: `
      document.addEventListener("DOMContentLoaded", function () {
          setTimeout(function () {
              // Add referral ID to Stripe checkout links
              document.querySelectorAll('a[href^="https://buy.stripe.com/"]').forEach(function (link) {
                  const oldBuyUrl = link.getAttribute("href");
                  const referralId = window.promotekit_referral;
                  if (!oldBuyUrl.includes("client_reference_id")) {
                      const newBuyUrl = oldBuyUrl + "?client_reference_id=" + referralId;
                      link.setAttribute("href", newBuyUrl);
                  }
              });

              // Add referral ID to Stripe pricing tables
              document.querySelectorAll("[pricing-table-id]").forEach(function (element) {
                  element.setAttribute("client-reference-id", window.promotekit_referral);
              });

              // Add referral ID to Stripe buy buttons
              document.querySelectorAll("[buy-button-id]").forEach(function (element) {
                  element.setAttribute("client-reference-id", window.promotekit_referral);
              });
          }, 1500); // Wait 1.5 seconds after page load as recommended by PromoteKit
      });
    `
  }} />
</head>
```

### How It Works

#### Referral Tracking Process

1. **Script Initialization**
   * The PromoteKit script loads with our organization UUID
   * It automatically detects referral parameters in the URL (`ref`, `aff`, `promocode`, or `via`)
   * When a parameter is found, it stores the referral in a cookie and in `window.promotekit_referral`
2. **Stripe Integration**
   * The script runs 1.5 seconds after page load to ensure PromoteKit is initialized, as defined in the setup guide
   * It finds all Stripe checkout links, pricing tables, and buy buttons in the page
   * It adds the referral ID to these elements as `client_reference_id` parameter
   * This passes the referral information to Stripe during checkout
3. **Helio Integration (for Crypto Payments)**
   * We extend the same tracking system to our crypto payment provider
   * The referral ID is passed as `promotekitReferral` in the `additionalJSON` field
   * This allows tracking affiliate-driven conversions for both fiat and crypto payments
4. **Conversion Tracking**
   * When a purchase is made, Stripe receives the referral ID as `client_reference_id`
   * This allows PromoteKit to track which affiliate drove the conversion

#### URL Parameter Detection

PromoteKit automatically detects these parameters in the URL:

| Parameter   | Example                                                | Description               |
| ----------- | ------------------------------------------------------ | ------------------------- |
| `ref`       | `https://stalkchain.com/pricing?ref=museonchain`       | Primary referral code     |
| `aff`       | `https://stalkchain.com/pricing?aff=museonchain`       | Alternative referral code |
| `promocode` | `https://stalkchain.com/pricing?promocode=museonchain` | Promotional code format   |
| `via`       | `https://stalkchain.com/pricing?via=museonchain`       | Source format             |

> **Note:** The PromoteKit dashboard provides `?via={code}` links by default for affiliates to share.

### API Integration

#### Tracking a Referral

When a referral is detected, PromoteKit makes this API call:

**Endpoint:** `POST https://www.promotekit.com/api/referrals/track`

**Request:**

```json
{
    "organizationUid": "{organization-uuid}",
    "code": "museonchain"
}
```

**Response:**

```json
{
    "referral": "70f0183e-2230-4aa3-8a34-447b10fd64a1",
    "cookie_duration": 60
}
```

#### Response Data Structure

| Field             | Type   | Description                                    |
| ----------------- | ------ | ---------------------------------------------- |
| `referral`        | String | UUID or referral code to use for attribution   |
| `cookie_duration` | Number | How many days the referral cookie will persist |

### Manual Tracking

#### Tracking Conversions

You can manually track conversions when a user completes a purchase or signup using the `refer()` method:

```javascript
/**
 * Track a conversion with PromoteKit
 * @param {string} email - User's email (required)
 * @param {string} customerId - Optional Stripe customer ID
 * @returns {Promise<string|'error'>} - Returns the referral code or 'error'
 */
function trackConversion(email, customerId = null) {
  if (typeof window === 'undefined' || !window.promotekit) {
    console.warn('PromoteKit not available');
    return Promise.resolve('error');
  }
  
  return window.promotekit.refer(email, customerId)
    .then(result => {
      console.log('Conversion tracked successfully:', result);
      return result;
    })
    .catch(error => {
      console.error('Failed to track conversion:', error);
      return 'error';
    });
}

// Example usage
document.getElementById('signup-form').addEventListener('submit', async (e) => {
  e.preventDefault();
  const email = document.getElementById('email').value;
  
  // First handle your normal signup process
  const user = await signupUser(email);
  
  // Then track the conversion
  if (user) {
    await trackConversion(email, stripe_customer_id?);
  }
});
```

#### Checking Current Referral

To check the current active referral:

```javascript
// Get the current referral from window object
const currentReferral = window.promotekit_referral;

// Get the saved referral from cookie
const savedReferral = promotekit_get_cookie();

console.log('Current referral:', currentReferral);
console.log('Saved cookie referral:', savedReferral);
```

### Integration with Checkout Systems

#### Stripe Integration

StalkChain uses `client_reference_id` to pass the referral ID to Stripe:

```javascript
// In server-side createStripeCheckout function
'use server';

import Stripe from 'stripe';
import { sanitize } from 'isomorphic-dompurify';

// Sanetize the referralId first before using it in the checkout session

// Create checkout session options
const sessionOptions = {
  payment_method_types: ['card'],
  line_items: [{ price: priceId, quantity: 1 }],
  mode: 'subscription',
  success_url: `${process.env.SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
  cancel_url: `${process.env.SITE_URL}/`,
  
  // Pass the PromoteKit referral ID to Stripe
  client_reference_id: sanitizedReferralId || undefined,
  
  // Include user ID in metadata, not in client_reference_id
  metadata: {
    userId: sanitizedUserId,
    // Other metadata...
  }
};

// Create a checkout session with Stripe
const session = await stripe.checkout.sessions.create(sessionOptions);
```

#### Custom Integration with Stripe Checkout Button

This example shows how we integrate PromoteKit with a Stripe checkout button component:

```jsx
// StripeCheckoutButton.js
'use client'

import { createStripeCheckout } from '@/actions/checkout/stripe';

export default function StripeCheckoutButton({
  userId,
  priceId,
  planName = 'PRO',
  billingPeriodValue = 1,
  billingPeriodUnit = 'month'
}) {
  const [referralId, setReferralId] = useState(null);

  // Get referral ID from PromoteKit when component mounts
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Use referral ID if immediately available
      if (window.promotekit_referral) {
        setReferralId(window.promotekit_referral);
      } else {
        // Check again after a short delay
        const checkReferralInterval = setInterval(() => {
          if (window.promotekit_referral) {
            setReferralId(window.promotekit_referral);
            clearInterval(checkReferralInterval);
          }
        }, 500);
        
        return () => clearInterval(checkReferralInterval);
      }
    }
  }, []);

  const handleCheckout = async () => {
    // Always use the latest referral ID from the window object
    const currentReferralId = typeof window !== 'undefined' 
      ? window.promotekit_referral 
      : referralId;
    
    // Include the referral ID in checkout data
    const response = await createStripeCheckout({
      priceId,
      userId,
      planName,
      referralId: currentReferralId,
      billingPeriodValue,
      billingPeriodUnit
    });

    // Redirect to Stripe if successful
    if (response.success && response.url) {
      window.location.href = response.url;
    }
  };

  return (
    <Button
      onClick={handleCheckout}
      buy-button-id={`stripe-${planName.toLowerCase()}-${billingPeriodValue}-${billingPeriodUnit}`}
    >
      Pay with Card
    </Button>
  );
}
```

#### Helio Integration for Crypto Payments

We extend PromoteKit's tracking to our crypto payment provider (Helio) by passing the referral ID as a custom parameter:

```jsx
// HelioWidgetWrapper.js excerpt
export default function HelioWidgetWrapper({ selectedPlan, billingCycle }) {
  const [userDetails, setUserDetails] = useState(null);
  
  // Load user details including PromoteKit referral
  const loadUserDetails = async () => {
    try {
      const [referralCode, cookieAuthId] = await Promise.all([
        getClientReferredBy(),
        getClientAuthId()
      ]);

      // Get the promotekit referral from the window object
      const promotekitReferral = typeof window !== 'undefined' 
        ? window.promotekit_referral 
        : null;

      setUserDetails({
        referralCode: referralCode || null, // Legacy referral code that is being phased out
        authId: cookieAuthId || null,
        promotekitReferral
      });
    } catch (error) {
      console.error("Error loading user details:", error);
    }
  };

  // Pass the PromoteKit referral ID to Helio
  const baseConfig = useMemo(() => ({
    // Other config...
    additionalJSON: {
      idUser: userAuth?.id || userDetails?.authId || null,
      affiliateCode: userDetails?.referralCode, // Legacy referral code that is being phased out
      promotekitReferral: userDetails?.promotekitReferral // Pass the PromoteKit referral ID
    }
  }), [userAuth?.id, userDetails]);

  // Render Helio widget with config...
}
```

### Debugging

#### Common Issues

1. **Referral ID Not Being Attached to Stripe Elements**
   * Verify the script is being executed after PromoteKit loads
   * Check browser console for JavaScript errors
   * Ensure `window.promotekit_referral` is being set properly
2. **Referrals Not Tracking**
   * Confirm the organization UUID is correct
   * Check that URLs include one of the supported referral parameters
3. **Helio Integration Issues**
   * Verify the `promotekitReferral` is being passed correctly in the `additionalJSON` field
   * Check that the referral ID is being tracked in your backend systems

#### Debugging Tips

Add the following code temporarily to verify the referral is being tracked:

```javascript
console.log('Current referral:', window.promotekit_referral);
```

### Best Practices

1. **URL Structure**
   * Use simple, memorable referral codes in URLs
   * Example: `https://stalkchain.com/?via=museonchain`
2. **Testing**
   * Test referral flows regularly with different parameters
   * Verify referrals appear correctly in both Stripe and Helio dashboards
3. **Stripe Integration**
   * Follow the rule in stripe.js: `client_reference_id should ONLY be promotekit_referral - NEVER use userId here`

***

### API Endpoints

PromoteKit provides two main API endpoints for referral tracking:

| Endpoint                                         | Method | Description                                                    |
| ------------------------------------------------ | ------ | -------------------------------------------------------------- |
| `https://www.promotekit.com/api/referrals/track` | POST   | Tracks a referral visit or looks up an affiliate's tracking ID |
| `https://www.promotekit.com/api/referrals/refer` | POST   | Records a conversion for an existing tracking ID               |

#### 1. Track a Referral Visit

The first endpoint is used for two purposes:

1. When a user visits with a referral code, this sets a tracking cookie
2. It can also be used to look up the tracking ID for an affiliate based on their referral code

**Request:**

* Method: `POST`
* URL: `https://www.promotekit.com/api/referrals/track`
* Headers:
  * Content-Type: `application/json`
* Body:

```json
{
    "organizationUid": "{organization-uuid}",
    "code": "sircallens"
}
```

**Response:**

```json
{
    "referral": "8f740b2c-a617-41aa-b882-6859d415858c",
    "cookie_duration": 60
}
```

In this example, we're sending the affiliate's referral code (`sircallens`) and receiving back their unique tracking ID (`8f740b2c-a617-41aa-b882-6859d415858c`). The system also sets a cookie to track this referral for 60 days if the request comes from a browser.

#### 2. Record a Conversion

The second endpoint records a conversion for a previously tracked referral. This is typically called when a user completes a purchase or signup.

**Request:**

* Method: `POST`
* URL: `https://www.promotekit.com/api/referrals/refer`
* Headers:
  * Content-Type: `application/json`
* Body:

```json
{
    "organizationUid": "{organization-uuid}",
    "code": "8f740b2c-a617-41aa-b882-6859d415858c",
    "email": "<EMAIL>",
    "customerId": null
}
```

**Response:**

```json
{
    "referral": "8f740b2c-a617-41aa-b882-6859d415858c",
    "cookie_duration": 60
}
```

In this example, we're manually attributing a conversion to a specific tracking ID (`8f740b2c-a617-41aa-b882-6859d415858c`). Note that we're using the tracking ID here, not the referral code. This allows us to credit the affiliate even if the user didn't have the tracking cookie set.

#### Response Data Structure

| Field             | Type   | Description                                                  |
| ----------------- | ------ | ------------------------------------------------------------ |
| `referral`        | String | The unique tracking ID associated with the referral code     |
| `cookie_duration` | Number | How many days the referral cookie will persist (default: 60) |

#### Important Notes

1. The `code` parameter in the `/track` endpoint is the public **referral code** that appears in URLs (e.g., `sircallens`)
2. The `code` parameter in the `/refer` endpoint is the unique **tracking ID** (e.g., `8f740b2c-a617-41aa-b882-6859d415858c`)
3. You can use the first endpoint to convert between referral codes and tracking IDs
4. For manually attributing conversions, you need the tracking ID, not the referral code

#### Postman Testing

1. Create a Postman collection with both requests
2. Create an environment variable for your organization UUID
3. Test with a valid referral code to get the tracking ID
4. Use that tracking ID to test the conversion endpoint

#### Security Considerations

* All API requests must be made over HTTPS
* Your organization UUID should be kept secure
* Server-side validation is recommended for conversion tracking
