# Wallet Age Recipe: Getting Wallet Funding Data with Solscan API

This guide explains how to retrieve wallet funding data (wallet age) using the Solscan API, including the logic for grouping related transactions to determine the true funding source.

## Overview

Wallet age analysis involves finding the first funding transaction for a wallet to determine:
- When the wallet was first funded
- What token was used for funding
- Who funded the wallet
- How much was the initial funding

## API Endpoint

```
GET https://pro-api.solscan.io/v2.0/account/transfer
```

## Required Parameters

| Parameter | Value | Description |
|-----------|-------|-------------|
| `address` | `{wallet_address}` | The wallet address to analyze |
| `activity_type` | `['ACTIVITY_SPL_TRANSFER']` | **Array format required** - Filter for SPL token transfers |
| `flow` | `in` | Only incoming transfers |
| `page` | `1` | First page of results |
| `page_size` | `20` | Number of transactions to fetch |
| `sort_by` | `block_time` | Sort by blockchain timestamp |
| `sort_order` | `asc` | Ascending order (oldest first) |

## Headers

**Important**: Only include the `token` header. Do NOT include `Content-Type` for GET requests.

```javascript
{
  'token': 'YOUR_SOLSCAN_API_KEY'
}
```

## Example Request

**Key Points:**
- Use `params` object (not manual URL building)
- `activity_type` must be an array
- Only include `token` header

```javascript
const response = await axios.get(
  'https://pro-api.solscan.io/v2.0/account/transfer',
  {
    params: {
      address: '********************************************',
      activity_type: ['ACTIVITY_SPL_TRANSFER'], // Array format required
      flow: 'in',
      page: 1,
      page_size: 20,
      sort_by: 'block_time',
      sort_order: 'asc'
    },
    headers: { 'token': process.env.SOLSCAN_API_KEY } // Only token header
  }
);
```

## API Response Structure

The Solscan API returns a response with the following structure:

```javascript
{
  "success": true,
  "data": [
    {
      "block_id": *********,
      "trans_id": "4NKsWSbWgjP2e3v638AYVDc1YCNRXfisG9zc9fETyLbN2oR5XGse4Ri4sDinZfzK4EbsVz2Egy3gRiHBkZfLNhMb",
      "block_time": **********,
      "activity_type": "ACTIVITY_SPL_TRANSFER",
      "from_address": "F4Lz7SabcHoHmsED5GQg3h92g47shAaGpyT458XCybu1",
      "from_token_account": "F4Lz7SabcHoHmsED5GQg3h92g47shAaGpyT458XCybu1",
      "to_address": "********************************************",
      "to_token_account": "********************************************",
      "token_address": "So11111111111111111111111111111111111111111",
      "token_decimals": 9,
      "amount": 980000,
      "flow": "in",
      "value": 0.*****************,
      "time": "2024-10-02T13:02:33.000Z"
    }
    // ... more transactions
  ],
  "metadata": {
    "tokens": {
      "So11111111111111111111111111111111111111111": {
        "token_address": "So11111111111111111111111111111111111111111",
        "token_name": "SOL",
        "token_symbol": "SOL",
        "token_icon": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"
      }
    }
  }
}
```

### Key Response Fields

- **`block_time`**: Unix timestamp of the transaction
- **`time`**: ISO date string of the transaction
- **`from_address`**: The wallet that sent the tokens (funding source)
- **`to_address`**: The wallet that received the tokens (target wallet)
- **`token_address`**: The token contract address
- **`amount`**: Raw token amount (needs decimal adjustment)
- **`token_decimals`**: Number of decimals for the token
- **`trans_id`**: Transaction signature/hash
- **`metadata.tokens`**: Token information including symbol, name, and icon

## Transaction Grouping Logic

The key insight is that multiple transactions from the same source within a short time window should be grouped together as a single funding event. This is common because many users will first send a test amount before sending the full amount to verify the wallet address is correct.

### Grouping Algorithm

```javascript
function groupRelatedTransactions(transactions, tokenMetadata = {}) {
  const TIME_WINDOW = 30 * 60; // 30 minutes in seconds
  const groups = [];
  
  transactions.forEach((tx) => {
    // Look for existing group with same token and source within time window
    const existingGroup = groups.find(group => 
      group.tokenAddress === tx.token_address &&
      group.fromAddress === tx.from_address &&
      (tx.block_time - group.lastTimestamp) <= TIME_WINDOW
    );

    if (existingGroup) {
      // Add to existing group
      existingGroup.txCount++;
      existingGroup.totalAmount += (tx.amount * Math.pow(10, -tx.token_decimals));
      existingGroup.lastTimestamp = tx.block_time;
    } else {
      // Create new group
      const tokenMeta = tokenMetadata[tx.token_address] || {};
      
      groups.push({
        tokenAddress: tx.token_address,
        fromAddress: tx.from_address,
        firstTx: tx,
        lastTimestamp: tx.block_time,
        txCount: 1,
        totalAmount: tx.amount * Math.pow(10, -tx.token_decimals),
        tokenMeta: {
          ticker: tokenMeta.token_symbol || '',
          name: tokenMeta.token_name || '',
          icon: tokenMeta.token_icon || ''
        }
      });
    }
  });

  // Return groups sorted by timestamp (oldest first)
  return groups.sort((a, b) => a.firstTx.block_time - b.firstTx.block_time);
}
```

### Why Group Transactions?

1. **Test Transactions**: Users commonly send a small test amount first to verify the wallet address
2. **Full Amount Follow-up**: After confirming the test transaction, they send the remaining amount
3. **True Funding Source**: The first group represents the complete funding event from the same source
4. **Accurate Amounts**: Summing related transactions gives the total funding amount (test + main)

## Complete Implementation

```javascript
async function getWalletFundingData(walletAddress) {
  try {
    const response = await axios.get(
      'https://pro-api.solscan.io/v2.0/account/transfer',
      {
        params: {
          address: walletAddress,
          activity_type: ['ACTIVITY_SPL_TRANSFER'],
          flow: 'in',
          page: 1,
          page_size: 20,
          sort_by: 'block_time',
          sort_order: 'asc'
        },
        headers: { 'token': process.env.SOLSCAN_API_KEY }
      }
    );

    if (!response.data.success || !response.data.data?.length) {
      return null;
    }

    // Group related transactions
    const groupedTxs = groupRelatedTransactions(
      response.data.data, 
      response.data.metadata?.tokens
    );
    
    const firstGroup = groupedTxs[0];
    
    return {
      walletAddress,
      fundingDate: firstGroup.firstTx.time,
      fundingTimestamp: firstGroup.firstTx.block_time,
      fundedFrom: firstGroup.fromAddress,
      fundedToken: firstGroup.tokenAddress,
      fundedAmount: firstGroup.totalAmount,
      signature: firstGroup.firstTx.trans_id,
      relatedTxCount: firstGroup.txCount,
      token: {
        ticker: firstGroup.tokenMeta.ticker,
        name: firstGroup.tokenMeta.name,
        icon: firstGroup.tokenMeta.icon
      }
    };
  } catch (error) {
    console.error('Error fetching wallet funding data:', error);
    throw error;
  }
}
```

## Expected Output

```javascript
{
  walletAddress: "********************************************",
  fundingDate: "2024-10-02T13:02:33.000Z",
  fundingTimestamp: **********,
  fundedFrom: "F4Lz7SabcHoHmsED5GQg3h92g47shAaGpyT458XCybu1",
  fundedToken: "So11111111111111111111111111111111111111111",
  fundedAmount: 0.98, // Adjusted for decimals and grouped
  signature: "4NKsWSbWgjP2e3v638AYVDc1YCNRXfisG9zc9fETyLbN2oR5XGse4Ri4sDinZfzK4EbsVz2Egy3gRiHBkZfLNhMb",
  relatedTxCount: 10, // Number of related transactions in the group
  token: {
    ticker: "SOL",
    name: "SOL",
    icon: "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png"
  }
}
```

## Error Handling

```javascript
// Handle API errors
if (!response.data.success) {
  throw new Error(`Solscan API Error: ${response.data.message || 'Failed to fetch transfers'}`);
}

// Handle no data
if (!response.data.data?.length) {
  return null; // No funding data found
}
```

## Rate Limiting Considerations

- Implement exponential backoff for retries
- Handle 429 (Too Many Requests) responses
- Use appropriate delays between batch requests
- Consider caching results to reduce API calls

## Key Insights

1. **Time Window**: 30-minute window for grouping related transactions
2. **Sorting**: Always sort by `block_time` ascending to get chronological order
3. **Decimals**: Adjust amounts using `token_decimals` field
4. **Metadata**: Use the metadata section for token information
5. **First Group**: The first group after sorting represents the initial funding event

## Common Pitfalls to Avoid

1. **Wrong API URL**: Use `https://pro-api.solscan.io` (Pro API), not `https://api.solscan.io` (free API)
2. **Wrong Headers**: Don't include `Content-Type: application/json` for GET requests
3. **Wrong Parameter Format**: `activity_type` must be an array: `['ACTIVITY_SPL_TRANSFER']`
4. **Manual URL Building**: Use axios `params` object instead of building query strings manually
5. **Missing Authentication**: Ensure `SOLSCAN_API_KEY` environment variable is set

This approach provides accurate wallet age analysis by properly handling complex funding patterns while maintaining simplicity in implementation. 