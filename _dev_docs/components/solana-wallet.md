---
description: >-
  Complete developer guide for the SolanaWallet component with async CEX detection, custom labeling, and performance optimization for tables with 100+ addresses.
---

**Target audience:** Frontend developers implementing wallet address displays in tables, feeds, and modals who need to understand CEX detection, custom naming, and performance optimization.

# SolanaWallet Component Documentation

## Quick Start

### Basic Usage
Display a wallet address with automatic CEX detection:

```jsx
// components/tables/TopHoldersTable.js
import { SolanaWallet } from "@/components/UI/SolanaAddress";

<SolanaWallet 
  address="5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
  size="xs"
  enableCexCheck={true}
/>
```

### Enable Custom Labels
Allow users to rename wallets (requires FavouriteContext):

```jsx
<SolanaWallet 
  address="5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
  renameOption={true}
  onStalkOpen={handleStalkModalOpen}
/>
```

### Disable CEX Detection
For performance in non-interactive contexts:

```jsx
<SolanaWallet 
  address="5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
  enableCexCheck={false}
/>
```

## Props Reference

### Essential Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `address` | `string` | **Required** | Solana wallet address (base58 format) |
| `enableCexCheck` | `boolean` | `true` | Enable async CEX detection and logo transformation |
| `size` | `"xs" \| "small" \| "default"` | `"default"` | Component size variant |

**Size Dimensions:**
- `xs`: 28px height, 175px width
- `small`: 34px height, 180px width  
- `default`: 38px height, 200px width

### Display Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `label` | `string` | `undefined` | Override display name (bypasses custom names lookup) |
| `noLabel` | `boolean` | `false` | Show raw address instead of any labels |
| `full` | `boolean` | `false` | Show complete address vs shortened format |
| `avatar` | `string` | `undefined` | Optional avatar URL for the wallet |

### Interactive Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `renameOption` | `boolean` | `false` | Show edit button for custom naming |
| `onStalkOpen` | `function` | `undefined` | Callback for stalk modal integration |

**onStalkOpen Signature:**
```javascript
onStalkOpen(callback, config)
// callback: function to call on save
// config: { customLabel: string, walletAddress: string }
```

## Real-World Usage Patterns

### In Data Tables (TopHoldersTable)
Perfect for displaying wallet addresses with CEX detection in large tables:

```jsx
// components/tables/TopHoldersTable.js
import { SolanaWallet } from "@/components/UI/SolanaAddress";

const columns = [
  columnHelper.accessor('owner', {
    header: 'Wallet Address',
    cell: (info) => {
      const address = info.getValue();
      const isProgram = info.row.original.isProgram;
      
      return (
        <div className="flex items-center gap-2">
          <SolanaWallet 
            address={address}
            size="xs"
            renameOption={false}
            full={false}
            noLabel={false}
            enableCexCheck={true}
          />
          {isProgram && (
            <Tooltip content="Smart contract or automated program">
              <span className="cursor-help text-lg">🤖</span>
            </Tooltip>
          )}
        </div>
      );
    },
    enableSorting: false,
  })
];
```

### In Modal Headers (SolanaWalletModal)
Display wallet with rename functionality:

```jsx
// modals/SolanaWallet.js
import { SolanaWallet } from "@/components/UI/SolanaAddress";

<SolanaWallet
  address={triggers.walletAddress}
  renameOption={true}
  avatar={triggers?.walletAvatar}
/>
```

### With Custom Naming System
Integrates with FavouriteContext for user-defined names:

```jsx
// The component automatically reads from customNames context
<SolanaWallet 
  address="5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"
  renameOption={true}
/>
// Will show custom name if user has renamed this wallet
```

## The CEX Detection System

### How It Works

```mermaid
flowchart TD
    A("Component Renders") --> B("Show Wallet Icon + Address")
    B --> C{"enableCexCheck?"}
    C -->|No| D("Stay as Wallet")
    C -->|Yes| E("Batch CEX Check")
    E --> F("Redis SET Lookup")
    F --> G{"Is CEX?"}
    G -->|No| D
    G -->|Yes| H("Transform to CEX Display")
    H --> I("Show CEX Logo + Name")
    
    classDef clientProcess fill:#e1f5fe,stroke:#01579b,color:#01579b
    classDef serverProcess fill:#e8f5e9,stroke:#2e7d32,color:#2e7d32
    classDef decision fill:#f8f9fa,stroke:#455a64,color:#455a64
    
    class A,B,D,H,I clientProcess
    class E,F serverProcess
    class C,G decision
```

### Performance: Async Batch Loading

**Problem Solved:** 100+ wallet components loading synchronously blocked page renders.

**Solution:** Intelligent batching with SWR caching:

1. **Component renders immediately** with wallet icon + address
2. **Batch collector** groups multiple CEX checks (50ms delay)
3. **Single API call** checks up to 100 addresses via `/api/cex/batch`
4. **Redis SET lookup** provides O(1) performance
5. **CEX wallets transform** to show exchange logo + name
6. **SWR caches results** for 5 minutes with deduplication

### API Endpoint: /api/cex/batch

**Request:**
```json
POST /api/cex/batch
{
  "addresses": [
    "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1",
    "8BnEgHoWFysVcuFFX7QztDmzuH8r5ZFvyP3sYwn1XTh6"
  ]
}
```

**Response:**
```json
{
  "5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1": {
    "isCex": true,
    "name": "Binance"
  },
  "8BnEgHoWFysVcuFFX7QztDmzuH8r5ZFvyP3sYwn1XTh6": {
    "isCex": false
  }
}
```

**Rate Limits:**
- Maximum 100 addresses per request
- 5-minute HTTP cache headers
- Redis-backed for sub-20ms responses

## Custom Labeling System (FavouriteContext)

### Database Structure

**MongoDB Collections:**
- **`wallet_labels`** - stores custom wallet names per user
- **`favourites`** - stores user's favourite wallets (for stalk functionality)

**wallet_labels Collection Schema:**
```javascript
{
  _id: ObjectId,
  idUser: string,        // User ID from auth system
  address: string,       // Wallet address
  label: string,         // Custom name/label
  createdAt: Date,
  updatedAt: Date
}
```

**favourites Collection Schema:**
```javascript
{
  _id: ObjectId,
  idUser: string,        // User ID from auth system
  type: string,          // "wallet" or "token"
  address: string,       // Wallet/token address
  createdAt: Date
}
```

### How the FavouriteContext Works

**Context Provider Structure:**
```javascript
// context/FavouriteContext.js
export const FavouriteContextProvider = ({ children }) => {
  const [favourites, setFavourites] = useState([]);      // Array from favourites collection
  const [customNames, setCustomNames] = useState({});    // Array from wallet_labels collection

  const favcontext = useMemo(() => {
    return {
      favourites: favourites,
      customNames: customNames,        // This is an ARRAY, not object
      update: () => fetchFavourites(),
      add: async ({ type, address }) => { /* ... */ },
      get: async ({ type, address }) => { /* ... */ },
      remove: async ({ type, address }) => { /* ... */ },
      rename: async ({ address, name }) => { /* ... */ }
    };
  }, [favourites, customNames]);
};
```

### How Custom Names Are Displayed

The component looks up custom names from the `customNames` array:

```javascript
// components/UI/SolanaAddress.js
const itemLabel = useMemo(() => {
  if (!address) return {};
  if (noLabel) return {};
  // Direct label prop overrides context lookup
  if (label) return { label };
  return auth?.permission?.isLoggedin && customNames?.length
    ? customNames?.find((c) => c.address === address)
    : {};
}, [auth?.permission?.isLoggedin, customNames, address, noLabel, label]);
```

### Label Priority Order

The component follows a strict priority hierarchy when determining what text to display:

```javascript
// components/UI/SolanaAddress.js
const itemLabel = useMemo(() => {
  if (!address) return {};
  if (noLabel) return {};
  // Direct label prop overrides context lookup
  if (label) return { label };
  return auth?.permission?.isLoggedin && customNames?.length
    ? customNames?.find((c) => c.address === address)
    : {};
}, [auth?.permission?.isLoggedin, customNames, address, noLabel, label]);
```

**Priority Order (Highest to Lowest):**

1. **`noLabel={true}`** - Shows nothing, returns early
2. **Direct `label` prop** - Highest priority, bypasses all lookups
3. **Custom user name** - From `wallet_labels` MongoDB collection via FavouriteContext
4. **CEX name** - If CEX detection enabled and wallet is a CEX exchange
5. **Shortened address** - Fallback display: `5Q544...ge4j1`

**Display Logic in Practice:**
```javascript
// In the render method, final display decision:
{itemLabel?.label 
  ? itemLabel.label           // Priority 2 or 3: Direct prop or custom name
  : full 
    ? address                 // Show full address if full={true}
    : shortAddress            // Priority 5: Shortened fallback
}

// But if it's a CEX wallet, this entire section is overridden:
if (isCexWallet && !isEditing) {
  // Shows CEX name instead - Priority 4
  return <div>{cexName}</div>
}
```

**Real Examples:**

- **User sets custom name:** `"Whale Wallet"` → Shows `"Whale Wallet"`
- **CEX detected:** `"Binance"` → Shows `"Binance"` with exchange logo
- **No custom name:** `"5Q544fKrFoe6tsEbD7S8EmxGTJYAKtTVhAW5Q5pge4j1"` → Shows `"5Q544...ge4j1"`
- **Direct prop:** `<SolanaWallet label="My DEX Bot" />` → Shows `"My DEX Bot"` (overrides everything)

### Twitter Handle Integration

When a user sets a custom wallet name starting with `@`, the component automatically adds a Twitter link icon:

**Detection Logic:**
```javascript
// components/UI/SolanaAddress.js
const isTwitterHandle = useMemo(() => {
  return itemLabel?.label?.startsWith("@");
}, [itemLabel?.label]);
```

**Rendering Logic:**
```javascript
{isTwitterHandle && (
  <a
    href={`https://twitter.com/${itemLabel.label.slice(1)}`}
    target="_blank"
    rel="noopener noreferrer"
    className="hover:opacity-50 p-1 text-default-500"
    onClick={(e) => e.stopPropagation()}
  >
    <TwitterIcon size={size === "xs" ? 12 : size === "small" ? 12 : 14} />
  </a>
)}
```

**How It Works:**

1. **User sets custom name:** User renames wallet to `"@elonmusk"`
2. **Component detects @:** `itemLabel.label.startsWith("@")` returns `true`
3. **Twitter icon appears:** Small Twitter icon shows next to the wallet name
4. **Link generation:** `itemLabel.label.slice(1)` removes the `@` → `"elonmusk"`
5. **Final URL:** `https://twitter.com/elonmusk`

**Real Usage Example:**

```jsx
// User renames a wallet to "@VitalikButerin"
<SolanaWallet 
  address="0x123..."
  renameOption={true}
  // After user saves "@VitalikButerin" as custom name:
  // - Display shows: "@VitalikButerin" 
  // - Twitter icon appears
  // - Clicking icon opens: https://twitter.com/VitalikButerin
/>
```

**Visual Behavior:**
- **Custom name:** Shows `@VitalikButerin` as the wallet text
- **Twitter icon:** Small blue Twitter icon appears in the controls area
- **Click behavior:** Icon opens Twitter profile in new tab
- **Event prevention:** `onClick={(e) => e.stopPropagation()}` prevents wallet click event

**Icon Sizing by Component Size:**
```javascript
<TwitterIcon size={size === "xs" ? 12 : size === "small" ? 12 : 14} />
```
- `xs` size: 12px Twitter icon
- `small` size: 12px Twitter icon  
- `default` size: 14px Twitter icon

**Note:** This only works for **custom user names** from the FavouriteContext, not for direct `label` props or CEX names.

### Where Custom Names Are Shown

The `isFavourite` check shows if user has this wallet in their stalks:

```javascript
const isFavourite = useMemo(() => {
  return auth?.permission?.isLoggedin && favourites?.length
    ? favourites?.find((f) => f.address === address)
    : false;
}, [auth?.permission?.isLoggedin, favourites, address]);
```

**Visual Indicators:**
- Custom name shows instead of shortened address
- Favourite wallets get yellow star and warning color text: `text-warning`
- Edit button appears when `renameOption={true}`

### Renaming Process

1. **User clicks edit** → Component enters edit mode
2. **User types name** → Stored in `tempName` state
3. **User saves** → Calls `rename()` from context
4. **Context calls action** → `renameWallet()` updates `wallet_labels` collection
5. **Database updated** → MongoDB stores the custom name
6. **UI updates** → Component shows new name immediately

### Integration with Stalks System

The system integrates deeply with the stalks (wallet following) system:

```javascript
// When adding a stalk, it also handles favorites
const result = await addStalk({ 
  address, 
  label: customName, 
  refreshFavourites: true  // This adds to favourites collection
});

// When editing a stalk, it updates the wallet label
const result = await editStalk({
  address,
  label: newName,
  refreshFavourites: true  // This updates wallet_labels collection
});
```

## CEX Logo System

### How CEX Logos Actually Work

**NOT** stored locally - they're served from a CDN via Next.js rewrite:

```javascript
// next.config.mjs
async rewrites() {
  return [
    {
      source: '/crypto-exchange/:path*',
      destination: 'https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/logos/exchanges/:path*',
    },
  ];
}
```

### Logo URL Generation

```javascript
// components/UI/SolanaAddress.js
const getExchangeLogoUrl = useCallback((name) => {
  if (!name) return null;
  // Format exchange name for image URL:
  // - lowercase
  // - replace dots with dashes
  // - replace spaces with dashes
  const formattedName = name
    .toLowerCase()
    .replace(/\./g, '-')
    .replace(/\s+/g, '-');
    
  return `/crypto-exchange/${formattedName}.png`;
}, []);
```

**Examples:**
- `"Binance"` → `/crypto-exchange/binance.png`
- `"Gate.io"` → `/crypto-exchange/gate-io.png`
- `"Crypto Com"` → `/crypto-exchange/crypto-com.png`

### Adding New CEX Exchanges

1. **Add CEX wallet to database:**
   ```sql
   INSERT INTO wallets_cex (address, cex) 
   VALUES ('wallet_address_here', 'Exchange Name');
   ```

2. **Upload logo to CDN** at:
   ```
   https://stalkchain.nyc3.cdn.digitaloceanspaces.com/assets/images/logos/exchanges/exchange-name.png
   ```

3. **Redis cache auto-syncs** from PostgreSQL (1-hour cooldown)

### Size Variants

Add new size by modifying the component's size logic:

```javascript
// Current size system in SolanaWalletComponent
style={{
  height: size === "xs" ? 28 : size === "small" ? 34 : 38,
  width: size === "xs" ? 175 : size === "small" ? 180 : renameOption ? 220 : 200,
}}

// Icon sizes
<WalletIcon
  size={size === "xs" ? 16 : size === "small" ? 18 : 20}
/>

// Text sizes
className={`${size === "xs" ? "text-xs" : size === "small" ? "text-sm" : "text-[14px]"}`}
```

### Adding Interactive Features

The component emits events for integration with other systems:

```javascript
// Wallet click event
emitter.emit("solana-wallet-address-clicked", address, "", {
  walletAvatar: avatar,
});

// Wallet renamed event  
emitter.emit("solana-wallet-renamed", { newLabel: nameToSave, address });
```

**Event Listeners:**
```javascript
import { getEmitter } from "@/utils/eventemitter";
const emitter = getEmitter();

emitter.on("solana-wallet-address-clicked", (address, _, metadata) => {
  // Handle wallet click - typically opens wallet modal
});

emitter.on("solana-wallet-renamed", ({ newLabel, address }) => {
  // Handle wallet rename - update UI or sync data
});
```

## Integration Requirements

### Required Context Providers

**Dashboard Layout:**
```jsx
// app/(dashboard)/layout.js
<AuthContextProvider>
  <FavouriteContextProvider>
    <CexProvider>
      {children}
    </CexProvider>
  </FavouriteContextProvider>
</AuthContextProvider>
```

**Public Layout:**
```jsx
// app/(public)/layout.js
<AuthContextProvider>
  <FavouriteContextProvider>
    {children}
  </FavouriteContextProvider>
</AuthContextProvider>
```

### Required Dependencies

**API Endpoints:**
- `/api/cex/batch` - CEX batch lookup
- Server actions for favourites: `getFavourites()`, `getCustomNames()`, `renameWallet()`

**Database Collections:**
- MongoDB: `wallet_labels`, `favourites` 
- PostgreSQL: `wallets_cex`
- Redis: CEX wallet cache

**NPM Dependencies:**
```json
{
  "swr": "^2.3.0",
  "@heroui/button": "^2.2.19",
  "@heroui/avatar": "^2.2.10",
  "@heroui/spinner": "^2.2.10"
}
```

### Redis Cache Configuration

CEX wallet data is stored in Redis with these keys:
- **SET key:** `stalkchain:cex:wallets` - contains all CEX addresses
- **Data keys:** `stalkchain:cex:wallets:{address}` - contains CEX details JSON

**Cache TTL:** 1 week (auto-syncs from PostgreSQL every hour)

## Troubleshooting

### CEX Detection Not Working

1. **Check Redis connection:**
   ```bash
   redis-cli ping
   redis-cli SCARD stalkchain:cex:wallets
   ```

2. **Verify API endpoint:**
   ```bash
   curl -X POST http://localhost:3000/api/cex/batch \
     -H "Content-Type: application/json" \
     -d '{"addresses":["test_address"]}'
   ```

3. **Check browser network tab** for failed requests or 500 errors

### Performance Issues

1. **Disable CEX checking** in non-critical contexts:
   ```jsx
   <SolanaWallet enableCexCheck={false} />
   ```

2. **Monitor batch sizes** - requests auto-batch but verify grouping:
   ```javascript
   // Check SWR cache in browser DevTools
   console.log(window.__SWR_CACHE__);
   ```

3. **Check Redis performance** - CEX lookups should be <20ms

### Custom Names Not Showing

1. **Verify FavouriteContext** is properly wrapped around component
2. **Check user authentication** - custom names require login
3. **Verify customNames array** contains the address:
   ```javascript
   console.log(customNames.find(c => c.address === targetAddress));
   ```
4. **Check MongoDB collections:**
   ```javascript
   // Check if wallet_labels collection has user's custom names
   db.wallet_labels.find({ idUser: "user_id" });
   ```

## Component Dependencies

```javascript
// components/UI/SolanaAddress.js
import { Button } from "@heroui/button";
import { Avatar } from "@heroui/avatar";
import { Spinner } from "@heroui/spinner";
import { useFavourites } from "@/context/FavouriteContext";
import { useAuth } from "@/context/AuthContext";
import { editStalk } from "@/actions/stalks/editStalk";
import { usePrivy } from "@privy-io/react-auth";
import { useCexCheck } from "@/hooks/useCexBatch";
import { getEmitter } from "@/utils/eventemitter";
```

**Dynamic Imports (for code splitting):**
```javascript
const CopyButton = dynamic(() => import("@/components/dashboard/CopyButton"));
const ExternalSolButton = dynamic(() => import("@/components/dashboard/ExternalSolButton"));
const WalletIcon = dynamic(() => import("@/components/icons/WalletIcon"));
const EditIcon = dynamic(() => import("@/components/icons/EditIcon"));
const TwitterIcon = dynamic(() => import("@/components/icons/TwitterIcon"));
``` 